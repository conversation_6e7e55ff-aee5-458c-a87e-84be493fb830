"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2613],{92562:function(e,t,i){i.d(t,{Z:function(){return k}});var n=i(85893),o=i(67294),r=i(6459),l=i(90038),s=i(90092),a=i(8186),d=i(68029),c=i(73636),u=i(61771),p=i(12519),h=i(9654),g=i(84871),m=i(72246),f=i(31838),x=i(31837),v=i(19399),b=i(72510),y=i(31082),w=i(69056);w.Z.initialize({startOnLoad:!0,theme:"default",securityLevel:"loose"});let _=e=>{let{source:t,id:i}=e,r=(0,o.useRef)(null);return(0,o.useEffect)(()=>{let e=async()=>{if(r.current){r.current.innerHTML=t;let{svg:e,bindFunctions:n}=await w.Z.render("mermaid-diagram-".concat(i),t);r.current&&(r.current.innerHTML=e,null==n||n(r.current))}};return e(),()=>{}},[t]),(0,n.jsx)("div",{className:"fill-available",style:{padding:10,height:"-webkit-fill-available",minHeight:"100%",width:"-webkit-fill-available",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},id:i,ref:r})};i(47426);let j=e=>{let{artifact:t,svgRef:i=(0,o.useRef)(null),shadow:w,app:j}=e,{t:k}=(0,x.$G)("common");(0,r.p)();let[C,S]=(0,o.useState)(""),[E,T]=(0,o.useState)(null),[I,A]=(0,o.useState)(!1),[F,R]=(0,o.useState)(!1),z=(0,o.useRef)(null),[L,B]=(0,o.useState)(!1),D=(0,o.useRef)(null),{openShareModal:M}=(0,y.K)(),[O,P]=(0,o.useState)(1),[N,q]=(0,o.useState)(!1),[H,G]=(0,o.useState)({x:0,y:0}),[W,Z]=(0,o.useState)({x:0,y:0}),[$]=(0,l.a)("(max-width: 768px)"),V=()=>{if(F)document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen();else{let e=z.current;e.requestFullscreen?e.requestFullscreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.msRequestFullscreen&&e.msRequestFullscreen()}R(!F)};(0,o.useEffect)(()=>{let e=()=>{let e=!!document.fullscreenElement;R(e),P(1),G({x:0,y:0});let t=i.current.querySelector("svg");t&&(t.style.transform="translate(0px, 0px) scale(1)",e?(t.parentElement.style.display="flex",t.parentElement.style.justifyContent="center",t.parentElement.style.alignItems="center",t.parentElement.style.width="100%",t.parentElement.style.height="100vh",i.current.style.width="100%"):(t.style.width="100%",t.style.height="100%",t.parentElement.style.margin="0",t.parentElement.style.width="100%",t.parentElement.style.height="-webkit-fill-available",i.current.style.height="100%",Y()))};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}},[i]),(0,o.useEffect)(()=>{if((null==t?void 0:t.type)==="svg"&&(null==t?void 0:t.content))try{S((0,v.w)(t.content)),T(null)}catch(e){console.error("Error setting initial SVG:",e),T("无法加载SVG内容")}},[t]);let Y=()=>{var e,t;let n=null==i?void 0:null===(e=i.current)||void 0===e?void 0:e.querySelector("svg");if(n){if(F){i.current.style.height="100%";return}let e=(0,v.j)(C),o=n.getAttribute("viewBox"),r=parseInt(null===(t=i.current.parentElement.style.maxWidth)||void 0===t?void 0:t.replace("px",""))||0,l=Math.min(window.innerWidth,r);console.log("max width............",l,window.innerWidth,r);let s=(null==e?void 0:e.width)||o.split(" ")[2],a=(null==e?void 0:e.height)||o.split(" ")[3];if(s){let e=a/s;s>.9*l&&(s=.9*l,i.current.style.width="".concat(s,"px")),a=s*e,i.current.style.height="".concat(a,"px")}else i.current.style.height="fit-content";n.style.width="-webkit-fill-available"}};(0,o.useEffect)(()=>{var e;(null==i?void 0:null===(e=i.current)||void 0===e?void 0:e.querySelector("svg"))&&Y()},[F]);let U=(0,o.useCallback)(()=>{Y(),B(!0)},[]);(0,o.useEffect)(()=>{if(C){let e=()=>{var t;let n=null===(t=i.current)||void 0===t?void 0:t.querySelector("svg");n?U():requestAnimationFrame(e)};e()}},[C,U]);let J=e=>{let t;let n=i.current.querySelector("svg");n&&(t="in"===e?O+.1:Math.max(.1,O-.1),n.style.transform="translate(".concat(H.x,"px, ").concat(H.y,"px) scale(").concat(t,")"),P(t))},X=e=>{F&&(q(!0),Z({x:e.clientX-H.x,y:e.clientY-H.y}))},K=e=>{if(!N||!F)return;let t=e.clientX-W.x,n=e.clientY-W.y;G({x:t,y:n});let o=i.current.querySelector("svg");o&&(o.style.transform="translate(".concat(t,"px, ").concat(n,"px) scale(").concat(O,")"))},Q=()=>{q(!1)};return E?(0,n.jsx)(s.x,{color:"red.500",children:k("error_loading_svg")}):((0,o.useEffect)(()=>{let e=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&Y()})},{root:null,rootMargin:"0px",threshold:.01});return D.current&&e.observe(D.current),()=>{D.current&&e.unobserve(D.current)}},[]),(0,n.jsx)(a.g,{spacing:4,align:"center",width:"100%",ref:D,children:(0,n.jsxs)(d.x,{ref:z,width:"100%",display:"flex",justifyContent:"center",alignItems:"center",boxShadow:w?"0 6px 8px rgba(0, 0, 0, 0.15)":"none",borderRadius:"lg",overflow:"hidden",backgroundColor:"white",padding:2,style:{position:"relative",maxWidth:$?window.innerWidth:800},id:"artifact-content-".concat(null==t?void 0:t._id),onMouseEnter:()=>A(!0),onMouseLeave:()=>A(!1),children:[(0,n.jsxs)("div",{ref:i,style:{width:"100%",height:"fit-content",minWidth:300,display:"flex",justifyContent:"flex-start",alignItems:"center",flexDirection:"column",overflow:"hidden",cursor:F?N?"grabbing":"grab":"default",...(null==t?void 0:t.promptId)==="witty_insights"?{background:"linear-gradient(180deg, #009fff 0%, #f0f8ff 100%)",borderRadius:"2px",padding:"2rem",margin:6,boxShadow:"0 8px 16px rgba(31, 38, 135, 0.15)",transition:"transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",_hover:{transform:"translateY(-4px)",boxShadow:"0 12px 24px rgba(31, 38, 135, 0.2)"}}:{}},onMouseDown:X,onMouseMove:K,onMouseUp:Q,onMouseLeave:Q,children:["mermaid"===t.type&&!F&&!!t.title&&(0,n.jsx)(s.x,{height:"-moz-min-content",width:"100%",fontWeight:"500",color:"#555",children:t.title}),t.imageUrl&&(0,n.jsx)("img",{src:t.imageUrl,alt:t.title||"Generated image",style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain",borderRadius:16}}),"svg"===t.type&&(C?(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:C},style:{width:"100%",height:"-webkit-fill-available",display:"flex",justifyContent:"center",alignItems:"center"}}):(0,n.jsx)(s.x,{children:k("loading")})),"markdown"===t.type&&(0,n.jsx)("div",{style:{width:"100%",padding:"4px",overflow:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start"},className:"markdown-content",children:(0,n.jsx)(b.U,{children:t.content})}),"mermaid"===t.type&&(0,n.jsx)(_,{id:"mermaid-chart-"+t._id,source:t.content})]}),I&&(0,n.jsxs)(c.U,{position:"absolute",top:2,right:2,spacing:1,children:[F&&!$&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.u,{label:k("zoom_out"),placement:"bottom",children:(0,n.jsx)(p.h,{icon:(0,n.jsx)(h.H,{size:24}),"aria-label":k("zoom_out"),size:"sm",onClick:()=>J("out")})}),(0,n.jsx)(u.u,{label:k("zoom_in"),placement:"bottom",children:(0,n.jsx)(p.h,{icon:(0,n.jsx)(g.D,{size:24}),"aria-label":k("zoom_in"),size:"sm",onClick:()=>J("in")})})]}),(0,n.jsx)(u.u,{label:k(F?"exit_fullscreen_tooltip":"fullscreen_tooltip"),placement:F?"bottom":"top",children:(0,n.jsx)(p.h,{icon:F?(0,n.jsx)(m.m,{size:28}):(0,n.jsx)(f.i,{size:28}),"aria-label":k("fullscreen_display"),size:"sm",onClick:V})})]})]})}))};var k=j},43598:function(e,t,i){i.d(t,{k:function(){return p}});var n=i(85893);i(67294);var o=i(86426),r=i(6459),l=i(38491),s=i(17597),a=i(61951),d=i(52091),c=i(31837);let u=e=>{let{inviteCode:t,showButton:i}=e,{t:u}=(0,c.$G)("common"),{logon:p}=(0,a.a)(),h=(0,r.p)(),g=(0,o.Nq)({onSuccess:async e=>{try{let i=await fetch("".concat(s.J,"/users/oauth-sign-in"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({tokenInfo:e,inviteCode:t}),credentials:"include"});if(i.ok){let e=await i.json();console.log(e,null==e?void 0:e.data),p(null==e?void 0:e.data),h({title:u("login_success"),status:"success",duration:3e3,isClosable:!0})}else console.error("Failed to login"),h({title:u("login_failed"),status:"error",duration:3e3,isClosable:!0})}catch(e){console.error("Error login:",e),h({title:u("login_error"),description:e.message,status:"error",duration:3e3,isClosable:!0})}}});return(0,o._7)({onSuccess:async e=>{try{let t=await fetch("".concat(s.J,"/users/oauth-sign-in-credential"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({credential:e.credential}),credentials:"include"});if(t.ok){let e=await t.json();console.log(e,null==e?void 0:e.data),p(null==e?void 0:e.data),h({title:u("login_success"),status:"success",duration:3e3,isClosable:!0})}else console.error("Failed to login"),h({title:u("login_failed"),status:"error",duration:3e3,isClosable:!0})}catch(e){console.error("Error login:",e),h({title:u("login_error"),description:e.message,status:"error",duration:3e3,isClosable:!0})}},onError:()=>{console.log("Login Failed")}}),i?(0,n.jsxs)(l.z,{width:"100%",onClick:g,justifyContent:"flex-start",spacing:4,children:[(0,n.jsx)(d.ldW,{style:{padding:4,marginRight:6},size:32,color:"dodgerblue"})," ",u("login_with_google")]}):(0,n.jsx)(n.Fragment,{})},p=e=>{let{inviteCode:t,showButton:i}=e;return(0,n.jsx)(o.rg,{clientId:"988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com",children:(0,n.jsx)(u,{inviteCode:t,showButton:i})})}},71692:function(e,t,i){var n=i(85893),o=i(67294),r=i(31082),l=i(6459),s=i(91816),a=i(68029),d=i(5490),c=i(34614),u=i(14437),p=i(57391),h=i(21360),g=i(70010),m=i(88329),f=i(37945),x=i(38491),v=i(68258),b=i(52091),y=i(31837),w=i(11752),_=i.n(w),j=i(47426);let k=e=>{let{app:t}=e,{t:i}=(0,y.$G)("common"),{isOpen:w,artifactId:k,title:C="",imageDataUrl:S,closeShareModal:E,tool:T}=(0,r.K)(),[I,A]=(0,o.useState)(""),F=(0,l.p)(),R=(0,s.S)({base:!0,md:!1}),{basePath:z}=_()().publicRuntimeConfig;(0,o.useEffect)(()=>{A("".concat(window.location.origin).concat(z,"/share/").concat(t||T,"/").concat(k,"/").concat((null==C?void 0:C.replaceAll(" ","-"))||""))},[k]);let L=async()=>{try{await navigator.clipboard.writeText(I),F({title:i("title_and_link_copied"),status:"success",duration:3e3,isClosable:!0})}catch(e){console.error("Failed to copy:",e),F({title:i("copy_failed"),status:"error",duration:3e3,isClosable:!0})}},B=()=>{L(),F({title:i("title_and_link_copied"),description:i("save_image_and_paste_to_instagram"),status:"success",duration:3e3,isClosable:!0})},D=e=>{let{children:t}=e;return(0,n.jsx)(a.x,{display:"flex",flexDirection:"row",alignItems:"center",gap:"10px",cursor:"pointer",p:2,borderRadius:"md",transition:"background-color 0.2s",_hover:{backgroundColor:"gray.100"},width:"100%",children:t})};return(0,n.jsxs)(d.u_,{isOpen:w,onClose:E,size:R?"full":"2xl",children:[(0,n.jsx)(c.Z,{}),(0,n.jsxs)(u.h,{children:[(0,n.jsx)(p.x,{children:i("share_to_social_platforms")}),(0,n.jsx)(h.o,{}),(0,n.jsx)(g.f,{children:(0,n.jsxs)(m.k,{direction:R||[j.IF.mindmap].includes(t)?"column":"row",gap:4,children:[(0,n.jsx)(a.x,{flex:1,children:(0,n.jsx)(f.E,{src:S,alt:"Card preview",w:"100%",h:"auto",mb:4,borderRadius:"md"})}),(0,n.jsxs)(a.x,{align:"stretch",flex:1,style:{display:[j.IF.mindmap].includes(t)&&!R?"flow":"flex",flexDirection:[j.IF.mindmap].includes(t)&&!R?"row":"column"},children:[(0,n.jsx)(v.B,{url:I,title:C,children:(0,n.jsxs)(D,{children:[(0,n.jsx)(v.Zm,{size:32,round:!0}),(0,n.jsx)("span",{children:i("share_to_twitter")})]})}),(0,n.jsx)(v.Dk,{url:I,quote:"".concat(C,"\n").concat(I),children:(0,n.jsxs)(D,{children:[(0,n.jsx)(v.Vq,{size:32,round:!0}),(0,n.jsx)("span",{children:i("share_to_facebook")})]})}),(0,n.jsx)(v.r2,{url:I,title:C,children:(0,n.jsxs)(D,{children:[(0,n.jsx)(v.pA,{size:32,round:!0}),(0,n.jsx)("span",{children:i("share_to_linkedin")})]})}),(0,n.jsx)(v.N0,{url:I,title:C,children:(0,n.jsxs)(D,{children:[(0,n.jsx)(v.ud,{size:32,round:!0}),(0,n.jsx)("span",{children:i("share_to_whatsapp")})]})}),(0,n.jsx)(a.x,{as:"button",onClick:B,gap:"10px",children:(0,n.jsxs)(D,{children:[(0,n.jsx)(b.Zf_,{size:32}),(0,n.jsx)("span",{children:i("share_to_instagram")})]})}),(0,n.jsx)(v.cG,{url:I,subject:C,body:"".concat(C,"\n\n").concat(I),children:(0,n.jsxs)(D,{children:[(0,n.jsx)(v.LQ,{size:32,round:!0}),(0,n.jsx)("span",{children:i("share_via_email")})]})}),(0,n.jsx)(x.z,{onClick:L,width:"100%",style:{marginRight:10,marginTop:6},children:i("copy_title_and_link")}),(0,n.jsx)(x.z,{as:"a",href:S,download:"card.png",width:"100%",style:{marginRight:10,marginTop:6},children:i("download_image")})]})]})})]})]})};t.Z=k},80153:function(e,t,i){i.d(t,{Z:function(){return ed}});var n=i(85893),o=i(67294),r=i(5108),l=i(12761),s=i(91816),a=i(61771),d=i(12519),c=i(30080);function u(e,t){let i=function(e,t){let i=p(e),n=p(t),o=Math.abs(i.x-n.x),r=Math.abs(i.y-n.y);return o>r?i.x>n.x?l.Ly.Left:l.Ly.Right:i.y>n.y?l.Ly.Top:l.Ly.Bottom}(e,t),[n,o]=function(e,t){var i,n,o,r,s,a;let d=e.internals.handleBounds.source.find(e=>e.position===t),c=d.width/2,u=d.height/2;switch(t){case l.Ly.Left:c=0;break;case l.Ly.Right:c=d.width;break;case l.Ly.Top:u=0;break;case l.Ly.Bottom:u=d.height}let p=((null===(i=e.internals)||void 0===i?void 0:null===(n=i.positionAbsolute)||void 0===n?void 0:n.x)||(null===(o=e.position)||void 0===o?void 0:o.x))+d.x+c,h=((null===(r=e.internals)||void 0===r?void 0:null===(s=r.positionAbsolute)||void 0===s?void 0:s.y)||(null===(a=e.position)||void 0===a?void 0:a.y))+d.y+u;return[p,h]}(e,i);return[n,o,i]}function p(e){var t,i,n,o,r,l,s,a,d,c;return{x:((null===(t=e.internals)||void 0===t?void 0:null===(i=t.positionAbsolute)||void 0===i?void 0:i.x)||(null===(n=e.position)||void 0===n?void 0:n.x))+((null===(o=e.measured)||void 0===o?void 0:o.width)||(null===(r=e.computed)||void 0===r?void 0:r.width))/2,y:((null===(l=e.internals)||void 0===l?void 0:null===(s=l.positionAbsolute)||void 0===s?void 0:s.y)||(null===(a=e.position)||void 0===a?void 0:a.y))+((null===(d=e.measured)||void 0===d?void 0:d.height)||(null===(c=e.computed)||void 0===c?void 0:c.height))/2}}i(51525);var h=i(72510),g=i(83792),m=i(77437),f=i(50567),x=i(55368),v=i(68350),b=i(3236);let y=e=>{let{props:t,value:i,language:r}=e,[l,s]=(0,o.useState)(!1),a=e=>{navigator.clipboard.writeText(e),s(!0),setTimeout(()=>s(!1),2e3)};return(0,n.jsxs)("div",{style:{position:"relative"},children:[(0,n.jsx)(SyntaxHighlighter,{style:vscDarkPlus,language:r,showLineNumbers:!0,wrapLines:!0,customStyle:{borderRadius:4},PreTag:"div",...t,children:i}),(0,n.jsxs)("button",{onClick:()=>a(i),style:{position:"absolute",top:0,right:0,background:"none",border:"none",color:"white",cursor:"pointer",padding:"5px",color:l?"lightskyblue":"white"},children:[l&&(0,n.jsx)(v.w,{color:"lightskyblue",size:16}),!l&&(0,n.jsx)(b.T,{size:16}),l?"Copid":"Copy"]})]})},w=e=>{let{onClick:t,children:i,setArtifactOffsetTop:r}=e,l=(0,o.useRef)(null);return(0,n.jsx)("button",{ref:l,onClick:()=>{var e;r&&r(null==l?void 0:null===(e=l.current)||void 0===e?void 0:e.offsetTop),t&&t()},style:{padding:"5px 10px",margin:"5px",backgroundColor:"#f0f0f0",border:"1px solid #ccc",borderRadius:"4px",cursor:"pointer"},children:i})},_=e=>{let{content:t,artifacts:i,setArtifacts:r,setSelectedArtifact:l,setArtifactOffsetTop:s}=e,[a,d]=(0,o.useState)("");(0,o.useEffect)(()=>{let e=r?(e=>{let t=[],i=0,n=e.replace(/<(svg)[\s\S]*?<\/\1>|```mermaid[\s\S]*?```/gi,e=>{let n;e.startsWith("<svg")?n="SVG":e.startsWith("```mermaid")&&(n="Mermaid",e=e.replace(/```mermaid\n?/,"").replace(/```$/,"")),t.push({id:i,type:n,content:e});let o="[ARTIFACT_".concat(i,"]");return i++,o});return r(t),n})(t):t;d(null==e?void 0:e.replace(/(\S)?(\*\*.*?\*\*)(\S)?/g,(e,t,i,n)=>(t?t+" ":"")+i+(n?" "+n:"")))},[t]);let c=(0,o.useCallback)(e=>{let t=i.find(t=>t.id===e);return t?(0,n.jsxs)(w,{onClick:()=>{l(t)},setArtifactOffsetTop:s,children:["View ",t.type," Content"]}):null},[i]),u=(0,o.useCallback)(e=>{var t;if("string"==typeof e){if(e.includes("[ARTIFACT_")){let t=parseInt(e.match(/\[ARTIFACT_(\d+)\]/)[1],10),i=e.split(/\[ARTIFACT_\d+\]/);return[i[0],c(t),i[1]]}return e}if(Array.isArray(e))return e.map(u).flat();if(null==e?void 0:null===(t=e.props)||void 0===t?void 0:t.children){let t=u(e.props.children);return o.cloneElement(e,{children:t})}return e},[i]);return(0,n.jsx)(h.U,{remarkPlugins:[f.Z,m.Z],rehypePlugins:[g.Z,x.Z],components:{p(e){let{node:t,children:i}=e,o=u(i);return(0,n.jsx)("p",{children:o})},code(e){let{node:t,inline:i,className:o,children:r,...l}=e,s=/language-(\w+)/.exec(o||""),a=String(r).replace(/\n$/,"");if("string"==typeof r&&a.includes("[ARTIFACT_")){let e=parseInt(a.match(/\d+/)[0],10);return c(e)}return s&&["svg"].includes(s[1])?(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:a}}):(0,n.jsx)("div",{className:"nodrag",children:!i&&s?(0,n.jsx)(y,{props:l,language:s[1],value:a}):(0,n.jsx)("code",{className:o,...l,children:r})})}},children:a})};i(58211);var j=i(53646);let k=[{id:"orange",title_bg:"rgb(244, 182, 121)",content_bg:"rgb(255, 235, 220)",border:"#DC7633"},{id:"pink",title_bg:"rgb(245, 193, 192)",content_bg:"rgb(255, 243, 243)",border:"rgb(225, 164, 165)",borderShadow:"#E1A4A5"},{id:"gray",title_bg:"rgb(205, 205, 205)",content_bg:"rgb(248, 248, 245)",border:"#777"},{id:"yellow",title_bg:"rgb(234, 205, 99)",content_bg:"rgb(255, 251, 236)",border:"rgb(202, 169, 72)",borderShadow:"#CAA948"},{id:"green",title_bg:"rgb(190, 219, 144)",content_bg:"#F0FFF0",border:"rgb(180, 179, 124)"},{id:"blue",title_bg:"rgb(167, 221, 215)",content_bg:"azure",border:"rgb(99, 167, 159)",borderShadow:"#63A79F"},{id:"purple",title_bg:"#fa90e6",content_bg:"rgb(255, 239, 255)",border:"mediumorchid"}];var C=i(94680),S=i(67421);let E=e=>{let{key:t,parentIndex:i,nodeId:r,listType:l,text:s,onClick:d,color_theme:c,deleteEnabled:u,onDeleteItem:p,expandEnabled:h,onExpand:g}=e,{t:m}=(0,S.$G)("common"),[f,x]=o.useState(),v=o.useRef();return(0,n.jsx)(a.u,{label:m("deep_dive_to_topic"),"aria-label":"deep div to topic tooltip",children:(0,n.jsxs)("div",{ref:v,onClick:d,style:{border:"1px solid ".concat(c.border),backgroundColor:c.content_bg,boxShadow:"5px 5px 0px rgba(0, 0, 0, 10%)",borderRadius:4,padding:"6px",paddingTop:2,paddingBottom:3,paddingLeft:4,cursor:d?"pointer":void 0,fontSize:13,display:"flow",flexDirection:"row",alignItems:"center",position:"relative"},onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1),children:[(0,n.jsxs)("span",{style:{fontWeight:500,fontSize:16},children:[d?void 0!=i?"-":"+":""," "]})," ",s,f&&h&&(0,n.jsx)(a.u,{label:m("expand_ideas"),"aria-label":"expand ideas tooltip",children:(0,n.jsx)("div",{className:"circle-button",style:{position:"absolute",display:"flex",alignItems:"center",justifyContent:"center",height:20,width:20,top:"50%",transform:"translate(50%, -50%)",right:0,borderColor:c.border,backgroundColor:c.content_bg},onClick:e=>{e.stopPropagation(),g(null==v?void 0:v.current)},children:(0,n.jsx)(C.i,{size:15,color:"dodgerblue"})})})]},t)})},T=e=>{let{key:t,nodeId:i,listType:o,list:r,parentIndex:l,action:s,queryType:a,nodeType:d,aiItemClicked:c,onExpand:u,onDeleteItem:p,expandEnabled:h,ai_generated:g,color_start:m=0,color_theme:f}=e,x=void 0!=l;return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",rowGap:10,paddingRight:x?void 0:10,paddingLeft:x?16:void 0,marginTop:x?void 0:5,marginBottom:x?void 0:5},children:[" ",null==r?void 0:r.map((e,t)=>{let r;if(!e)return;let x=e;"object"==typeof x&&(x=e.name,r=e.branches);let v=f||k[(t+m)%k.length];if(!(x=null==x?void 0:x.trim()))return;let b=(0,n.jsx)(E,{index:t,parentIndex:l,nodeId:i,listType:o,text:x,color_theme:v,expandEnabled:h,onExpand:u?e=>{u(e,x)}:void 0,onClick:c?()=>{c({item:{action:s||"query"},nodeType:d,queryType:a,userInput:x,color_theme:v,ai_generated:g})}:void 0,deleteEnabled:!!p,onDeleteItem:p},t+"");return r&&(b=(0,n.jsxs)(n.Fragment,{children:[b,(0,n.jsx)(T,{list:r,nodeId:i,listType:o,action:s,queryType:a,aiItemClicked:c,onExpand:u,onDeleteItem:p,expandEnabled:h,ai_generated:g,parentIndex:t,color_theme:v},t+"_sub")]})),b})]},t||i)};var I=i(56538),A=i(3819),F=i(99272),R=i(49294);let z={CARD:"card"},L=e=>{let{id:t,text:i,index:r,moveCard:l,children:s,style:a}=e,d=(0,o.useRef)(null),[{handlerId:c},u]=(0,F.L)({accept:z.CARD,collect:e=>({handlerId:e.getHandlerId()}),hover(e,t){var i;if(!d.current)return;let n=e.index;if(n===r)return;let o=null===(i=d.current)||void 0===i?void 0:i.getBoundingClientRect(),s=(o.bottom-o.top)/2,a=t.getClientOffset(),c=a.y-o.top;n<r&&c<s||n>r&&c>s||(l(n,r),e.index=r)}}),[{isDragging:p},h]=(0,R.c)({type:z.CARD,item:()=>({id:t,index:r}),collect:e=>({isDragging:e.isDragging()})});return h(u(d)),(0,n.jsx)("div",{ref:d,style:{...a,opacity:p?0:1},"data-handler-id":c,children:s})};var B=i(7145),D=i.n(B),M=i(67865),O=i(40791);let P=e=>{var t;let{item:i,index:r,nodeId:l,onClick:s,onCheck:d,onUpdatePriority:c,onRemoveTask:u,onTaskBreakdown:p,expandEnabled:h,onExpand:g,priorities:m,color_theme:f,moveCard:x}=e,{t:v}=(0,S.$G)("common"),[b,y]=o.useState(),w=o.useRef(),_=i.done?A.l:I.z;return(0,n.jsx)(L,{index:r,id:i.id,moveCard:x,children:(0,n.jsxs)("div",{style:{position:"relative",border:"1px solid ".concat(f.border),backgroundColor:f.content_bg,boxShadow:"5px 5px 0px rgba(0, 0, 0, 10%)",borderRadius:4,padding:"6px",paddingLeft:4,paddingTop:3,paddingBottom:0,fontSize:13,display:"flex",flexDirection:"row",alignItems:"center",columnGap:4},ref:w,onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[(0,n.jsx)("div",{style:{cursor:"pointer"},onClick:e=>{e.stopPropagation(),d()},children:(0,n.jsx)(_,{color:"#333",size:18})}),(0,n.jsxs)("div",{className:"fill-available",children:[(0,n.jsx)("div",{style:{textDecoration:i.done?"line-through":"none",color:i.done?"GrayText":void 0,marginTop:2,marginBottom:1},children:null===(t=i.description)||void 0===t?void 0:t.trim()}),(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-end",columnGap:3,marginTop:2,fontSize:11,width:"100%"},children:[i.dueDate&&(0,n.jsxs)("div",{children:[v("due_date"),": ",i.dueDate]}),m&&(0,n.jsxs)("div",{style:{color:"GrayText",display:"flex",flexDirection:"row",alignItems:"center"},children:[v("priority"),":",(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center"},children:[i.priority||"Mediumn",(0,n.jsx)(a.u,{label:v("task_priority"),"aria-label":"task priority update",children:(0,n.jsx)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",cursor:"pointer",padding:3},onClick:c,children:(0,n.jsx)(O._,{size:10})})})]})]}),(0,n.jsx)(a.u,{label:v("task_analysis"),"aria-label":"task analysis",children:(0,n.jsx)("div",{className:"transparent-background",style:{padding:2},onClick:()=>{i.done||s()},children:(0,n.jsx)(M.J,{size:15,color:f.border})})}),(0,n.jsx)(a.u,{label:v("task_breakdown"),"aria-label":"task breakdown",children:(0,n.jsx)("div",{className:"transparent-background",style:{padding:2},onClick:e=>{e.stopPropagation(),p()},children:(0,n.jsx)(C.i,{size:13,color:f.border})})})]})]}),b&&h&&(0,n.jsx)(a.u,{label:v("expand_ideas"),"aria-label":"expand ideas tooltip",children:(0,n.jsx)("div",{className:"circle-button",style:{position:"absolute",display:"flex",alignItems:"center",justifyContent:"center",height:20,width:20,top:"50%",transform:"translate(50%, -50%)",right:0,borderColor:f.border,backgroundColor:f.content_bg},onClick:e=>{e.stopPropagation(),g(null==w?void 0:w.current)},children:(0,n.jsx)(C.i,{size:15,color:"dodgerblue"})})})]})})},N=e=>{let{nodeId:t,list:i,priorities:r,listType:l,queryType:s,nodeType:a,aiItemClicked:d,handleUpdatePriority:c,updateNodeData:u,expandEnabled:p,onExpand:h}=e,[g,m]=o.useState(i);o.useEffect(()=>{m(i)},[i]);let f=o.useCallback((e,t)=>{m(i=>D()(i,{$splice:[[e,1],[t,0,i[e]]]}))},[]);return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",rowGap:10,paddingRight:10,marginTop:5,marginBottom:5,cursor:"move"},onDrop:e=>{u(t,{todos:g})},children:[" ",null==g?void 0:g.map((e,o)=>{if(!e)return;let u=null==r?void 0:r.findIndex(t=>t===e.priority),g=k.find(t=>t.id===(e.done&&"gray"||0===u&&"purple"||2===u&&"blue"||"green"));return(0,n.jsx)(P,{index:o,nodeId:t,list:i,item:e,listType:l,priorities:r,color_theme:g,moveCard:f,onCheck:()=>{d()},expandEnabled:p,onExpand:h?t=>{h(t,e.description)}:void 0,onTaskBreakdown:()=>{d({item:{action:"flow_task_breakdown"},nodeType:a,queryType:s,userInput:e.description,color_theme:g})},onClick:()=>{d({item:{action:"flow_task_analysis"},nodeType:a,queryType:s,userInput:e.description,color_theme:g})},onUpdatePriority:t=>c(e,t),onRemoveTask:()=>handleRemoveTask(e)},e.id+"")})]})};var q=i(90038),H=i(2779),G=i(32783);let W=e=>{let{title:t,url:i,loading:r,info:l,color_theme:s,nodeType:d,queryType:c}=e,[u,p]=o.useState(!1),h=o.useRef(null);o.useEffect(()=>{h.current&&p(h.current.scrollHeight>h.current.clientHeight)},[t]);let g=o.useMemo(()=>({container:{display:"-webkit-box",WebkitLineClamp:2,whiteSpace:"pre-line",overflow:"hidden",textOverflow:"ellipsis",WebkitBoxOrient:"vertical"}}),[s]);return(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",backgroundColor:s.title_bg,borderBottomWidth:"1px",borderColor:s.border,padding:"12px",paddingTop:5,paddingBottom:3,paddingLeft:("link"===c||"funblocks_doc"===d)&&6||12,minHeight:20,fontSize:14,fontWeight:500,borderTopLeftRadius:3,borderTopRightRadius:3},children:[("link"===c||"funblocks_doc"===d)&&(0,n.jsxs)("div",{style:{width:18,height:24,marginRight:4,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center"},children:["link"===c&&(0,n.jsx)(H.r,{size:18,color:s.border}),"funblocks_doc"===d&&(0,n.jsx)(G.a,{size:18,color:s.border})]}),(0,n.jsx)("div",{ref:h,style:g.container,children:u?(0,n.jsx)(a.u,{label:t,"aria-label":"expand title tooltip",children:(0,n.jsx)("div",{style:{height:"fit-content"},children:t})}):(0,n.jsx)("div",{children:t})}),r&&(0,n.jsx)(j.Z,{size:18}),!r&&!!l&&(0,n.jsxs)("span",{style:{fontSize:13},children:["(",l,")"]})]})},Z=e=>{var t,i,s;let{data:a,isConnectable:d,selected:c}=e,[u,p]=o.useState(),[h,g]=o.useState(),{t:m}=(0,S.$G)("common");o.useRef(null);let f=o.useRef(null),x=o.useRef(null),[v,b]=o.useState(0),[y,w]=o.useState(-1),[j,C]=o.useState([]),[E,I]=o.useState(0),[A,F]=o.useState(!1),[R,z]=o.useState(),[L,B]=o.useState(),[D,M]=o.useState(),[O,P]=o.useState(),{zoom:H}=(0,r.Sj)(),[G]=(0,q.a)("(max-width: 768px)"),Z=(0,r.hR)(),$=(0,r.SM)(),V=$.find(e=>e.id===Z),{nodeType:Y,title:U,content:J}=a,X=o.useMemo(()=>"slides"===Y||"slideshow"===a.ai_action?480:340,[]),K=o.useMemo(()=>k.find(e=>e.id===a.color_theme)||k.find(e=>"blue"===e.id),[a.color_theme]),Q=o.useCallback(()=>{let e=new CustomEvent("showAIFlowToast");window.dispatchEvent(e)},[]);return(0,n.jsxs)("div",{className:"node",style:"group"==Y?{display:"contents"}:{backgroundColor:K.content_bg,borderColor:K.border,boxShadow:"0px 0px 6px #ccc",borderRadius:"note"==Y?0:void 0,width:"video"===Y&&562||(null==V?void 0:V.width)||X,height:"video"===Y&&"fit-content"||void 0,minHeight:"slideshow"===a.ai_action?9*X/16:void 0,maxHeight:a.minimized?100:void 0,overflowY:a.minimized?"clip":void 0,pointerEvents:"all"},children:[(0,n.jsx)(r.HH,{type:"target",position:l.Ly.Top,id:"TT"}),(0,n.jsx)(r.HH,{type:"target",position:l.Ly.Bottom,id:"BT"}),(0,n.jsx)(r.HH,{type:"target",position:l.Ly.Left,id:"LT"}),(0,n.jsx)(r.HH,{type:"target",position:l.Ly.Right,id:"RT"}),(0,n.jsx)(r.HH,{type:"source",position:l.Ly.Top,id:"a"}),(0,n.jsx)(r.HH,{type:"source",position:l.Ly.Right,id:"b"}),(0,n.jsx)(r.HH,{type:"source",position:l.Ly.Bottom,id:"c"}),(0,n.jsx)(r.HH,{type:"source",position:l.Ly.Left,id:"d"}),(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(n.Fragment,{children:[!["image"].includes(Y)&&(0,n.jsx)(W,{nodeType:Y,queryType:a.queryType,title:U,url:null===(t=a.context)||void 0===t?void 0:t.url,color_theme:K}),!!a.vid&&(0,n.jsx)("iframe",{width:"560",height:"315",ref:x,src:"https://www.youtube.com/embed/".concat(a.vid),title:"FunBlocks AI Video Player",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0}),!!(null===(i=a.content)||void 0===i?void 0:i.src)&&(0,n.jsx)("img",{src:a.content.src,style:{borderRadius:4}}),("link"===a.queryType||["funblocks_doc"].includes(a.nodeType)||J&&"image"!==Y||a.brainstorming_scenario||a.todos||a.items)&&(0,n.jsxs)("div",{className:G||!c?void 0:"nodrag",style:{margin:"12px",marginRight:a.todos||a.items?5:12,marginTop:7,marginBottom:7,fontSize:14,overflowY:"auto",cursor:"text"},ref:f,children:[J&&!["breakdown","brainstorming_perspective","slideshow"].includes(a.ai_action)&&!(null===(s=a.todos)||void 0===s?void 0:s.length)&&(0,n.jsx)(_,{content:J}),!!a.brainstorming_scenario&&(0,n.jsx)("div",{style:{fontSize:13,color:"GrayText"},children:a.brainstorming_scenario}),a.items&&(0,n.jsx)(T,{nodeId:Z,list:a.items,queryType:"tell_more",aiItemClicked:Q,expandEnabled:!0,onExpand:(e,t)=>{Q()}}),a.todos&&(0,n.jsx)(N,{nodeId:Z,list:a.todos,priorities:a.priorities,queryType:"task_breakdown",aiItemClicked:Q,expandEnabled:!0,onExpand:(e,t)=>{Q()},updateNodeData:e=>{Q()},handleUpdatePriority:Q}),(["link"].includes(a.queryType)||["funblocks_doc"].includes(a.nodeType))&&!["prompt","note"].includes(a.nodeType)&&(0,n.jsx)("div",{className:"fill-available",style:{display:"flex",flexDirection:"row",justifyContent:"flex-end"},children:(0,n.jsx)("div",{onClick:async()=>{var e;let t=null===(e=a.context)||void 0===e?void 0:e.url;"funblocks_doc"===Y&&(t="".concat(window.location.origin,"/#/editor?hid=").concat(a.hid,"&space=workspace")),window.open(t,"_blank")},style:{color:K.border,border:"none",padding:"8px 12px",cursor:"pointer",textAlign:"center"},children:m("to_original_page")})})]})]})})]})},$=e=>{let t=e.replace(/(\s*\n\s*)?(?=[\{\}\[\],:])/g,"").replace(RegExp("(?<=[\\{\\}\\[\\],:])(\\s*\\n\\s*)?","g"),"");return t},V=e=>{let t=e=>{let t=e.charCodeAt(0);switch(t){case 8:return"\\b";case 9:return"\\t";case 10:return"\\n";case 12:return"\\f";case 13:return"\\r";case 27:return"\\e";default:return"\\x"+t.toString(16).padStart(2,"0")}};return e.replace(/[\x00-\x1F\x7F]/g,t)},Y=e=>e.replace(/^(\*\*\d+\.\s*)(.+?)(\*\*)$/gm,(e,t,i,n)=>{let o=t.replace(/^\*\*/,"");return"".concat(o,"**").concat(i,"**")}),U=e=>e.replace(/\[((?:\{.*?\}|[^\[\]]*?))\]/g,(e,t)=>{if(t.trim().startsWith("{"))return e;let i=t.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);return"["+(i=i.map(e=>e.trim().replace(/(^"|"$)/g,"").replace(/([^\\])"/g,'$1\\"'))).map(e=>'"'.concat(e,'"')).join(",")+"]"}),J=e=>(e=U(e)).replace(RegExp('([{,]\\s*")([^"]+)":\\s*"(.*?)"\\s*(?=[,\\]}])',"gs"),function(e,t,i,n){let o=n.replace(/([^\\])"/g,'$1\\"');return"".concat(t).concat(i,'": "').concat(o,'"')}),X=e=>{let t=[],i=-1,n=-1;for(let o=0;o<e.length;o++)if("{"===e[o])0===t.length&&(i=o),t.push("{");else if("}"===e[o]&&t.length>0&&(t.pop(),0===t.length)){n=o;break}if(-1==i||-1==n)return null;let o=e.substring(i,n+1);try{return JSON.parse(o)}catch(e){try{return JSON.parse(Y(V($(o))))}catch(e){try{let e=J(Y(V($(o))));return JSON.parse(e)}catch(e){console.error("failed to parse json...........",e,o)}}}};var K=i(17597),Q=i(6459),ee=i(38491),et=i(28493),ei=i(36963),en=i(47426);let eo=[20,20],er={ai_node:Z},el={float_edge:function(e){let{id:t,source:i,target:s,markerEnd:a,style:d}=e,[c,p]=(0,o.useState)(!1),h=(0,r.oR)((0,o.useCallback)(e=>{var t;return null===(t=e.nodeLookup)||void 0===t?void 0:t.get(i)},[i])),g=(0,r.oR)((0,o.useCallback)(e=>{var t;return null===(t=e.nodeLookup)||void 0===t?void 0:t.get(s)},[s])),{setEdges:m}=(0,r._K)(),f=(0,o.useRef)(null);if((0,o.useCallback)(e=>{e.stopPropagation(),m(e=>e.filter(e=>e.id!==t))},[m,t]),!h||!g)return null;let{sx:x,sy:v,tx:b,ty:y,sourcePos:w,targetPos:_}=function(e,t){let[i,n,o]=u(e,t),[r,l,s]=u(t,e);return{sx:i,sy:n,tx:r,ty:l,sourcePos:o,targetPos:s}}(h,g),[j,k,C]=(0,l.OQ)({sourceX:x,sourceY:v,sourcePosition:w,targetPosition:_,targetX:b,targetY:y});return(0,n.jsxs)("g",{ref:f,children:[(0,n.jsx)("path",{id:"".concat(t,"-selector"),className:"react-flow__edge-selector",d:j,strokeWidth:20,fill:"none",stroke:"transparent"}),(0,n.jsx)("path",{id:t,className:"react-flow__edge-path",d:j,strokeWidth:5,markerEnd:a,style:{...d,stroke:c?"#555":null==d?void 0:d.stroke}})]})}},es={x:0,y:0,zoom:1.5},ea=e=>{let{content:t,mimeContents:i,imgGenerated:u,setImgGenerated:p,context:h,ai_action:g,mode:m,app:f,onDocSaved:x,doc:v}=e,[b,y,w]=(0,r.Rr)([]),[_,j,C]=(0,r.ll)([]),[E,T]=(0,o.useState)(null),[I,A]=(0,o.useState)(),F=(0,Q.p)(),{t:R}=(0,S.$G)("common"),[z,L]=(0,o.useState)(null),[B,D]=(0,o.useState)(!1),M=(0,s.S)({base:!0,md:!1}),O=(0,o.useCallback)((e,t,i,n)=>{let o={id:new Date().getTime()+t.length+"",type:"ai_node",data:{aigc_triggered:!0,nodeType:"aigc",...e}};return t.length&&i.push({id:new Date().getTime()+i.length+"",source:n?o.id:t[0].id,target:n?t[0].id:o.id,type:"float_edge",markerEnd:{type:l.QZ.ArrowClosed}}),n?t.unshift(o):t.push(o),o},[]);(0,o.useEffect)(()=>{var e,n,o,r,l;if(!t&&!(null==i?void 0:i.length))return;let s=[],a=[];if([en.IF.erase,en.IF.avatar,en.IF.imageEditor,en.IF.sketch].includes(f))p(null===(e=i[0])||void 0===e?void 0:e.src),(null===(n=i[0])||void 0===n?void 0:n.src)&&O({nodeType:"image",queryType:"image",title:null==h?void 0:h.title,content:{src:i[0].src,caption:t}},s,a);else{let e=X(t),i=null==e?void 0:e.generated;if(!i)return;if("flow_task_breakdown"===g){let e=new Date().getTime(),t=[];null===(o=i.items)||void 0===o||o.forEach(e=>{t.includes(e.priority)||t.push(e.priority)}),t.length||(t=["high","medium","low"]),O({aigc_done:!1,content:"",contentType:"todos",todos:null===(r=i.items)||void 0===r?void 0:r.map((t,i)=>(t.id=e+i,t)),priorities:t.length>0?t:void 0},s,a)}else if("summary_keypoints"===g)O({aigc_done:!1,title:"Summary and key points",content:i.summary,items:i.keypoints},s,a);else{let e=O({ai_action:g,queryType:["movie","book","link","video"].includes(m)&&m||"brainstorming",content:(null==i?void 0:i.central_topic)||(null==i?void 0:i.core_story)||(null==i?void 0:i.initial_analysis)||(null==i?void 0:i.decision)||(null==i?void 0:i.description)||(null==i?void 0:i.summary),title:(null==i?void 0:i.theme)||(null==i?void 0:i.title)||(null==i?void 0:i.problem)||"FunBlocks AI Mindmap",brainstorming_scenario:null==i?void 0:i.target_scenario,is_mindmap_root:!0},s,a),t=(null==i?void 0:i.key_perspectives)||(null==i?void 0:i.primary_branches)||(null==i?void 0:i.branches);(null==t?void 0:t.length)>0&&t.map((t,i)=>{if(!t.branches)return;let n=k[i%k.length];O({queryType:["flow_subtopic_brainstorming","flow_brainstorming","flow_decision_analysis"].includes(g)&&"perspective"||["flow_mindmap","flow_book_mindmap","flow_movie_mindmap","describe_image_mindmap"].includes(g)&&"mindmap_primary_branch"||"perspective",ai_action:["flow_subtopic_brainstorming","flow_brainstorming","flow_decision_analysis"].includes(g)&&"brainstorming_perspective"||"flow_mindmap"===g&&"mindmap_primary_branch"||"flow_book_mindmap"===g&&"book_mindmap_primary_branch"||"flow_movie_mindmap"===g&&"movie_mindmap_primary_branch"||"describe_image_mindmap"===g&&"image_mindmap_primary_branch"||"brainstorming_perspective",title:t.name,items:t.branches,userInput:t.name,context_node_id:e.id,color_theme:null==n?void 0:n.id},s,a)});let n=(null==i?void 0:i.summary_insights)||(null==i?void 0:i.key_insights)||(null==i?void 0:i.transformative_insights)||(null==i?void 0:i.insights);n&&O({queryType:"brainstorming_insights",context_node_id:e.id,title:"Summary insights",items:n,color_theme:null===(l=k.find(e=>"blue"===e.id))||void 0===l?void 0:l.id},s,a)}}if((s=s.map((e,t)=>{if(0===t)e.position={x:0,y:0};else{let i=-(t-1)*(2*Math.PI/(s.length-1));e.position={x:650*Math.cos(i),y:650*Math.sin(i)-(t>s.length/2?300:0)}}return e}))&&["video","link","image","others"].includes(m)){let e=O({nodeType:"video"===m&&"video"||"image"===m&&"image"||"others"===m&&"note"||"aigc",queryType:["video","link","image"].includes(m)&&m||void 0,vid:h.vid,context:["others","image"].includes(m)?void 0:h,title:h.title,content:"others"===m?h.userInput:"image"===m?h:void 0,userInput:"others"===m?h.userInput:void 0},s,a,!0);s[0].position={x:[en.IF.erase,en.IF.avatar,en.IF.imageEditor,en.IF.sketch].includes(f)?-450:-900,y:0},s[1].data.context_node_id=e.id}y(s),j(a),x(null)},[t,i]),(0,o.useEffect)(()=>{if((null==b?void 0:b.length)||!(null==v?void 0:v.jsonString))return;let e=JSON.parse(v.jsonString||"{}");y(e.nodes||[]),j(e.edges||[])},[v]);let P=(0,o.useCallback)(async(e,t)=>{try{let i=new AbortController,n=setTimeout(()=>i.abort(),18e4),o=v?{doc:{hid:v.hid,type:"flow",jsonString:t}}:{doc:{type:"flow",title:e[0].data.userInput||e[0].data.title,jsonString:t,app:f,mode:m},source:"aitools"};A(!0);let r=await fetch("".concat(K.J).concat("/doc/upsert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:o}),credentials:"include",signal:i.signal});if(clearTimeout(n),r.ok){let e=await r.json();(null==e?void 0:e.data)&&x&&x(e.data)}}catch(e){console.error("Error in saving doc:",e)}finally{A(!1)}},[x,v]);(0,o.useEffect)(()=>{if((null==b?void 0:b.length)&&!v&&!I){var e;let t=(null===(e=null==E?void 0:E.getNodes())||void 0===e?void 0:e.length)?E.toObject():{nodes:b,edges:_,viewport:{x:0,y:0,zoom:1}},i=JSON.stringify(t);P(b,i)}},[b,_,E]);let N=(0,o.useCallback)(e=>j(t=>(0,l.Z_)({...e,animated:!0},t)),[]);o.useEffect(()=>{let e=()=>{F({title:R("to_aiflow"),description:(0,n.jsxs)("div",{style:{display:"flex",flexDirection:"column",rowGap:10},children:[(0,n.jsx)("span",{style:{font:18},children:R("to_aiflow_tips")}),(0,n.jsx)(ee.z,{size:"sm",onClick:()=>window.open("https://app.funblocks.net/#/aiflow?hid="+v.hid),children:"FunBlocks AIFlow"})]}),duration:1e4,isClosable:!0})};return window.addEventListener("showAIFlowToast",e),()=>{window.removeEventListener("showAIFlowToast",e)}},[F,R,v]);let q=()=>{z&&(document.fullscreenElement?document.exitFullscreen():z.requestFullscreen().catch(e=>{console.error("Error attempting to enable fullscreen: ".concat(e.message))}))};return(0,o.useEffect)(()=>{let e=()=>{D(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}},[]),(0,n.jsx)(et.W,{backend:ei.PD,children:(0,n.jsxs)("div",{ref:L,style:{position:"relative",width:"100%",height:"100%",backgroundColor:B?"#f8f8f8":"transparent"},children:[M&&u&&(0,n.jsx)("img",{src:u}),!(M&&u)&&(0,n.jsxs)(r.x$,{nodes:b,edges:_,onNodesChange:w,onEdgesChange:C,onConnect:N,onInit:T,nodeTypes:er,edgeTypes:el,snapToGrid:!0,snapGrid:eo,defaultViewport:es,fitView:!0,attributionPosition:"bottom-left",style:{borderRadius:6},children:[(0,n.jsx)(r.Aq,{style:{backgroundColor:"#f8f8f8"}}),(0,n.jsx)(r.ZX,{}),(0,n.jsxs)("div",{style:{position:"absolute",right:"20px",bottom:"20px",zIndex:4,display:"flex",flexDirection:"column",rowGap:10},children:[(0,n.jsx)(a.u,{label:"Open with FunBlocks AIFlow","aria-label":"Open with FunBlocks AIFlow",children:(0,n.jsx)(d.h,{"aria-label":"Open with FunBlocks AIFlow",icon:(0,n.jsx)(c.zmo,{size:20}),onClick:()=>window.open("https://app.funblocks.net/#/aiflow?hid="+v.hid),colorScheme:"gray",variant:"solid",size:"md",borderRadius:"full",boxShadow:"md",_hover:{transform:"scale(1.1)"},transition:"all 0.2s"})}),(0,n.jsx)(a.u,{label:B?"Exit Fullscreen":"Enter Fullscreen","aria-label":"Fullscreen tooltip",children:(0,n.jsx)(d.h,{"aria-label":B?"Exit Fullscreen":"Enter Fullscreen",icon:B?(0,n.jsx)(c.I_n,{size:24}):(0,n.jsx)(c.Nms,{size:24}),onClick:q,colorScheme:"gray",variant:"solid",size:"md",borderRadius:"full",boxShadow:"md",_hover:{transform:"scale(1.1)"},transition:"all 0.2s"})})]})]})]})})};var ed=ea},49515:function(e,t,i){var n=i(85893),o=i(90243),r=i(11163);let l=()=>{let e=(0,r.useRouter)(),t=t=>{let i=t.target.value;e.push(e.pathname,e.asPath,{locale:i})};return(0,n.jsxs)(o.P,{onChange:t,value:e.locale,size:"sm",width:"auto",variant:"filled",borderRadius:"full",children:[(0,n.jsx)("option",{value:"en",children:"English"}),(0,n.jsx)("option",{value:"zh",children:"中文"})]})};t.Z=l},31082:function(e,t,i){i.d(t,{$:function(){return c},K:function(){return u}});var n=i(85893),o=i(67294),r=i(11752),l=i.n(r),s=i(91816);let{basePath:a}=l()().publicRuntimeConfig,d=(0,o.createContext)(),c=e=>{let{children:t}=e,[i,r]=(0,o.useState)(!1),[l,c]=(0,o.useState)(null),[u,p]=(0,o.useState)(null),[h,g]=(0,o.useState)(null),m=(0,s.S)({base:!0,md:!1}),[f,x]=(0,o.useState)(),v=async(e,t,i,n)=>{if(navigator.share)try{let o=await (await fetch(n)).blob(),r=new File([o],"card.png",{type:"image/png"});return await navigator.share({title:t,text:t,url:"".concat(window.location.origin).concat(a,"/share/").concat(e,"/").concat(i),files:[r]}),!0}catch(e){console.error("Error sharing:",e)}return!1},b=async(e,t,i,n)=>{if(m){let o=await v(e,n,t,i);if(o)return}x(e),c(t),p(i),g(n),r(!0)},y=()=>{x(null),r(!1),c(null),p(null),g(null)};return(0,n.jsx)(d.Provider,{value:{tool:f,isOpen:i,artifactId:l,imageDataUrl:u,title:h,openShareModal:b,closeShareModal:y},children:t})},u=()=>(0,o.useContext)(d)},47426:function(e,t,i){i.d(t,{IF:function(){return n},WD:function(){return o}});let n={mindmap:"Mindmap",graphics:"Graphics",infographic:"Infographic",insightcards:"InsightCards",brainstorming:"Brainstorming",mindkit:"MindKit",mindsnap:"MindSnap",businessmodel:"Businessmodel",startupmentor:"Startupmentor",okr:"Okr",decision:"Decision",planner:"Planner",slides:"Slides",onePageSlides:"one-page-slide",erase:"Erase",avatar:"Avatar",imageEditor:"ImageEditor",sketch:"Sketch",youtube:"Youtube",counselor:"Counselor",dreamlens:"DreamLens",horoscope:"Horoscope",art:"Art",photo:"Photo",reading:"Reading",movie:"Movie",criticalThinking:"critical-thinking",reflection:"reflection",refineQuestion:"refine-question",bias:"Bias",poetic:"Poetic",feynman:"Feynman",bloom:"Bloom",solo:"solo",dok:"dok",marzano:"marzano",layeredExplanation:"layered-explanation",promptOptimizer:"prompt-optimizer",lessonPlanner:"lesson-plans",teachingSlides:"teaching-slides",dokAssessment:"dok-assessment"},o=e=>e===n.art&&"Art Insight"||e===n.onePageSlides&&"SlideGenius"||e===n.poetic&&"Poetic Lens"||e===n.bloom&&"Bloom's Taxonomy"||e===n.solo&&"SOLO Taxonomy"||e===n.dok&&"Webb's DOK"||e===n.marzano&&"Marzano's Taxonomy"||e===n.feynman&&"Feynman Tutor"||e===n.reading&&"Reading Map"||e===n.movie&&"CineMap"||e===n.photo&&"Photo Coach"||e===n.decision&&"Decision Analyst"||e===n.criticalThinking&&"Critical Analysis"||e===n.refineQuestion&&"QuestionCraft AI"||e===n.bias&&"LogicLens"||e===n.youtube&&"Youtube Summarizer"||e===n.planner&&"Task Planner"||e===n.startupmentor&&"Startup Mentor"||e===n.businessmodel&&"Business Model Consultant"||e===n.okr&&"OKR Assistant"||e===n.erase&&"Watermarks Eraser"||e===n.avatar&&"Avatar Studio"||e===n.imageEditor&&"AI Image Editor"||e===n.sketch&&"AI Sketch"||e===n.insightcards&&"InsightCards"||e===n.layeredExplanation&&"MindLadder"||e===n.lessonPlanner&&"Lesson Plans"||e===n.teachingSlides&&"Teaching Slides"||e===n.dokAssessment&&"DOK Assessment"||e.charAt(0).toUpperCase()+e.slice(1)},45744:function(e,t,i){i.d(t,{Bf:function(){return o},OR:function(){return s},gp:function(){return l}});var n=i(46293);let o=async(e,t,i,n,o,l)=>{try{if(t){let l=await r(n);o(e,t,l,i)}else l({description:"Have nothing to share",status:"error",duration:3e3,isClosable:!0})}catch(e){l({title:"Failed to share",description:"Please try again later",status:"error",duration:3e3,isClosable:!0}),console.error("分享图片时出错:",e)}},r=e=>new Promise((t,i)=>{let o=e.current;(0,n.YM)(o).then(e=>{let i=document.createElement("canvas"),n=i.getContext("2d"),o=new Image;o.onload=()=>{i.width=o.width+60,i.height=o.height+60+80+20,n.fillStyle="white",n.fillRect(0,0,i.width,i.height),n.shadowColor="rgba(0, 0, 0, 0.15)",n.shadowBlur=10,n.shadowOffsetX=4,n.shadowOffsetY=6,n.beginPath(),n.roundRect(30,30,o.width,o.height,10),n.fillStyle="white",n.fill(),n.shadowColor="transparent",n.shadowBlur=0,n.shadowOffsetX=0,n.shadowOffsetY=0,n.drawImage(o,30,30);let e=document.getElementById("qrcode-canvas").toDataURL("image/png");if(e){let o=new Image;o.onload=()=>{n.shadowColor="rgba(0, 0, 0, 0.1)",n.shadowBlur=5,n.shadowOffsetX=2,n.shadowOffsetY=2,n.drawImage(o,i.width-80-30,i.height-80-30,80,80),n.shadowColor="transparent",n.shadowBlur=0,n.shadowOffsetX=0,n.shadowOffsetY=0,n.font="15px Arial",n.fillStyle="#666666",n.textAlign="right",n.fillText("Generated with",i.width-30-80-18,i.height-30-80+15);let e=i.width-30-80-18,r=n.createLinearGradient(e-200,0,e,0);r.addColorStop(0,"#4361ee"),r.addColorStop(1,"#7209b7"),n.font="bold 24px Arial",n.fillStyle=r,n.textAlign="right",n.fillText("FunBlocks AI Graphics",e,i.height-30-40+5),n.font="20px Arial",n.fillStyle="#444444",n.textAlign="right",n.fillText("Where wit meets wisdom",e,i.height-30-5),t(i.toDataURL("image/png"))},o.src=e}else t(i.toDataURL("image/png"))},o.src=e}).catch(e=>{console.error("Error getting image data:",e),i(e)})}),l=async(e,t)=>{try{let t=await r(e),i=document.createElement("a");i.download="artifact.png",i.href=t,i.click()}catch(e){t({title:"下载图片时出错",description:"请稍后再试",status:"error",duration:3e3,isClosable:!0}),console.error("下载图片时出错:",e)}},s=(e,t)=>e.includes(t)||e.includes("/imgproxy?")||e.startsWith("data:")?e:t+"/imgproxy?url="+encodeURIComponent(e)},19399:function(e,t,i){i.d(t,{j:function(){return a},w:function(){return o}});let n=(e,t)=>{if("string"!=typeof e)return e;if(e.endsWith("%")){let i=parseFloat(e)/100;return t*i}let i=parseFloat(e);return isNaN(i)?e:i},o=e=>{var t,i,o,l,s,a,d,c;let u=e.replace(/@import\s+url\([^)]+\);?/g,""),p=new DOMParser,h=p.parseFromString(u,"image/svg+xml"),g=h.documentElement;if("parsererror"===g.tagName)throw Error("Invalid SVG content");let m=parseFloat(g.getAttribute("width"))||(null===(t=g.viewBox)||void 0===t?void 0:null===(i=t.baseVal)||void 0===i?void 0:i.width),f=parseFloat(g.getAttribute("height"))||(null===(o=g.viewBox)||void 0===o?void 0:null===(l=o.baseVal)||void 0===l?void 0:l.height),x=g.getElementsByTagName("*");for(let e of x)["x","y","width","height"].forEach(t=>{if(e.hasAttribute(t)){let i=e.getAttribute(t),o=n(i,"x"===t||"width"===t?m:f);e.setAttribute(t,o)}});if(!g.getAttribute("viewBox")){let e=m||(null===(s=g.width)||void 0===s?void 0:null===(a=s.baseVal)||void 0===a?void 0:a.value)||400,t=f||(null===(d=g.height)||void 0===d?void 0:null===(c=d.baseVal)||void 0===c?void 0:c.value)||800;g.setAttribute("viewBox","0 0 ".concat(e," ").concat(t))}g.setAttribute("preserveAspectRatio","xMidYMid meet"),r(g);let v=new XMLSerializer;return v.serializeToString(g)},r=e=>{let t=e.querySelectorAll("text");t.forEach(t=>{var i,n;let o=parseFloat(t.getAttribute("x")||0),r=parseFloat(t.getAttribute("y")||0),l=parseFloat(t.getAttribute("width")||0),a=parseFloat(e.getAttribute("width")||(null===(i=e.viewBox)||void 0===i?void 0:null===(n=i.baseVal)||void 0===n?void 0:n.width)),d=l||.8*a,c=t.querySelectorAll("tspan");if(c.length>0)c.forEach((e,t)=>{e.setAttribute("x",o),t>0?e.setAttribute("dy","1.2em"):e.setAttribute("y",r)});else{let i=t.textContent,n=i.split(/\s+/);t.textContent="";let l="",a=document.createElementNS("http://www.w3.org/2000/svg","tspan");a.setAttribute("x",o),a.setAttribute("y",r),t.appendChild(a),n.forEach((i,n)=>{let r=l+(l?" ":"")+i,c=s(r+" ",t,e);c>d&&n>0?(a.textContent=l,l=i,(a=document.createElementNS("http://www.w3.org/2000/svg","tspan")).setAttribute("x",o),a.setAttribute("dy","1.2em"),t.appendChild(a)):l=r}),a.textContent=l}})},l=e=>{let t={},i=e.getElementsByTagName("style");for(let e of i){let i=e.textContent,n=i.match(/[^\{\}]+\{[^\}]+\}/g);n&&n.forEach(e=>{let[i,n]=e.split("{"),o=n.replace("}","").split(";");o.forEach(e=>{let[n,o]=e.split(":").map(e=>e.trim());n&&o&&(t[i.trim()]||(t[i.trim()]={}),t[i.trim()][n]=o)})})}return t},s=(e,t,i)=>{let n=document.createElement("canvas"),o=n.getContext("2d"),r=l(i),s=t.getAttribute("font-size"),a=t.getAttribute("font-family"),d=t.getAttribute("class");return d&&r[".".concat(d)]&&(s=s||r[".".concat(d)]["font-size"],a=a||r[".".concat(d)]["font-family"]),s=s||r.text&&r.text["font-size"]||r[".content"]&&r[".content"]["font-size"]||"14px",a=a||r.text&&r.text["font-family"]||r[".content"]&&r[".content"]["font-family"]||"Arial, sans-serif",o.font="".concat(s,"px ").concat(a),o.measureText(e).width};function a(e){var t,i,n,o;let r=new DOMParser,l=r.parseFromString(e.replace(/@import\s+url\([^)]+\);?/g,""),"image/svg+xml"),s=l.documentElement,a=s.getAttribute("width")||(null===(t=s.viewBox)||void 0===t?void 0:null===(i=t.baseVal)||void 0===i?void 0:i.width),d=s.getAttribute("height")||(null===(n=s.viewBox)||void 0===n?void 0:null===(o=n.baseVal)||void 0===o?void 0:o.height);if(!a||!d){let e=s.getAttribute("viewBox");if(e){let[,,t,i]=e.split(" ").map(Number);a=a||t,d=d||i}}return{width:a,height:d}}}}]);