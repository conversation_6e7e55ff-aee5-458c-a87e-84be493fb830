"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5460],{86426:function(e,t,n){n.d(t,{Nq:function(){return s},_7:function(){return l},rg:function(){return i}});var r=n(67294);let o=(0,r.createContext)(null);function i({clientId:e,nonce:t,onScriptLoadSuccess:n,onScriptLoadError:i,children:a}){let s=function(e={}){let{nonce:t,onScriptLoadSuccess:n,onScriptLoadError:o}=e,[i,a]=(0,r.useState)(!1),s=(0,r.useRef)(n);s.current=n;let l=(0,r.useRef)(o);return l.current=o,(0,r.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;a(!0),null===(e=s.current)||void 0===e||e.call(s)},e.onerror=()=>{var e;a(!1),null===(e=l.current)||void 0===e||e.call(l)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),i}({nonce:t,onScriptLoadSuccess:n,onScriptLoadError:i}),l=(0,r.useMemo)(()=>({clientId:e,scriptLoadedSuccessfully:s}),[e,s]);return r.createElement(o.Provider,{value:l},a)}function a(){let e=(0,r.useContext)(o);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}function s({flow:e="implicit",scope:t="",onSuccess:n,onError:o,onNonOAuthError:i,overrideScope:s,state:l,...u}){let{clientId:c,scriptLoadedSuccessfully:f}=a(),d=(0,r.useRef)(),p=(0,r.useRef)(n);p.current=n;let m=(0,r.useRef)(o);m.current=o;let h=(0,r.useRef)(i);h.current=i,(0,r.useEffect)(()=>{var n,r;if(!f)return;let o=null===(r=null===(n=null==window?void 0:window.google)||void 0===n?void 0:n.accounts)||void 0===r?void 0:r.oauth2["implicit"===e?"initTokenClient":"initCodeClient"]({client_id:c,scope:s?t:`openid profile email ${t}`,callback:e=>{var t,n;if(e.error)return null===(t=m.current)||void 0===t?void 0:t.call(m,e);null===(n=p.current)||void 0===n||n.call(p,e)},error_callback:e=>{var t;null===(t=h.current)||void 0===t||t.call(h,e)},state:l,...u});d.current=o},[c,f,e,t,l]);let v=(0,r.useCallback)(e=>{var t;return null===(t=d.current)||void 0===t?void 0:t.requestAccessToken(e)},[]),b=(0,r.useCallback)(()=>{var e;return null===(e=d.current)||void 0===e?void 0:e.requestCode()},[]);return"implicit"===e?v:b}function l({onSuccess:e,onError:t,promptMomentNotification:n,cancel_on_tap_outside:o,prompt_parent_id:i,state_cookie_domain:s,hosted_domain:l,use_fedcm_for_prompt:u=!1,disabled:c,auto_select:f}){let{clientId:d,scriptLoadedSuccessfully:p}=a(),m=(0,r.useRef)(e);m.current=e;let h=(0,r.useRef)(t);h.current=t;let v=(0,r.useRef)(n);v.current=n,(0,r.useEffect)(()=>{var e,t,n,r,a,b,g,y,w;if(p){if(c){null===(n=null===(t=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===t?void 0:t.id)||void 0===n||n.cancel();return}return null===(b=null===(a=null===(r=null==window?void 0:window.google)||void 0===r?void 0:r.accounts)||void 0===a?void 0:a.id)||void 0===b||b.initialize({client_id:d,callback:e=>{var t;if(!(null==e?void 0:e.credential))return null===(t=h.current)||void 0===t?void 0:t.call(h);let{credential:n,select_by:r}=e;m.current({credential:n,clientId:function(e){var t;let n=null!==(t=null==e?void 0:e.clientId)&&void 0!==t?t:null==e?void 0:e.client_id;return n}(e),select_by:r})},hosted_domain:l,cancel_on_tap_outside:o,prompt_parent_id:i,state_cookie_domain:s,use_fedcm_for_prompt:u,auto_select:f}),null===(w=null===(y=null===(g=null==window?void 0:window.google)||void 0===g?void 0:g.accounts)||void 0===y?void 0:y.id)||void 0===w||w.prompt(v.current),()=>{var e,t,n;null===(n=null===(t=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===t?void 0:t.id)||void 0===n||n.cancel()}}},[d,p,o,i,s,l,u,c,f])}},83658:function(e,t,n){n.d(t,{q:function(){return i}});var r=n(95372),o=n(67294);function i(e={}){let{onClose:t,onOpen:n,isOpen:i,id:a}=e,s=(0,r.W)(n),l=(0,r.W)(t),[u,c]=(0,o.useState)(e.defaultIsOpen||!1),f=void 0!==i?i:u,d=void 0!==i,p=(0,o.useId)(),m=a??`disclosure-${p}`,h=(0,o.useCallback)(()=>{d||c(!1),l?.()},[d,l]),v=(0,o.useCallback)(()=>{d||c(!0),s?.()},[d,s]),b=(0,o.useCallback)(()=>{f?h():v()},[f,v,h]);return{isOpen:f,onOpen:v,onClose:h,onToggle:b,isControlled:d,getButtonProps:function(e={}){return{...e,"aria-expanded":f,"aria-controls":m,onClick(t){e.onClick?.(t),b()}}},getDisclosureProps:function(e={}){return{...e,hidden:!f,id:m}}}}},49882:function(e,t,n){n.d(t,{O:function(){return i}});var r=n(67294),o=n(95372);function i(e,t,n,i){let a=(0,o.W)(n);return(0,r.useEffect)(()=>{let r="function"==typeof e?e():e??document;if(n&&r)return r.addEventListener(t,a,i),()=>{r.removeEventListener(t,a,i)}},[t,e,i,a,n]),()=>{let n="function"==typeof e?e():e??document;n?.removeEventListener(t,a,i)}}},38389:function(e,t,n){n.d(t,{U:function(){return o}});var r=n(24278);let o=(0,r.I)({displayName:"HamburgerIcon",viewBox:"0 0 24 24",d:"M 3 5 A 1.0001 1.0001 0 1 0 3 7 L 21 7 A 1.0001 1.0001 0 1 0 21 5 L 3 5 z M 3 11 A 1.0001 1.0001 0 1 0 3 13 L 21 13 A 1.0001 1.0001 0 1 0 21 11 L 3 11 z M 3 17 A 1.0001 1.0001 0 1 0 3 19 L 21 19 A 1.0001 1.0001 0 1 0 21 17 L 3 17 z"})},12519:function(e,t,n){n.d(t,{h:function(){return s}});var r=n(85893),o=n(67294),i=n(38491),a=n(49381);let s=(0,a.G)((e,t)=>{let{icon:n,children:a,isRound:s,"aria-label":l,...u}=e,c=n||a,f=(0,o.isValidElement)(c)?(0,o.cloneElement)(c,{"aria-hidden":!0,focusable:!1}):null;return(0,r.jsx)(i.z,{px:"0",py:"0",borderRadius:s?"full":void 0,ref:t,"aria-label":l,...u,children:f})});s.displayName="IconButton"},94691:function(e,t,n){n.d(t,{h:function(){return s}});var r=n(29062),o=n(20397),i=n(67294);function a(e){let t=e.composedPath?.()?.[0]??e.target,{tagName:n,isContentEditable:r}=t;return"INPUT"!==n&&"TEXTAREA"!==n&&!0!==r}function s(e={}){let{ref:t,isDisabled:n,isFocusable:s,clickOnEnter:l=!0,clickOnSpace:u=!0,onMouseDown:c,onMouseUp:f,onClick:d,onKeyDown:p,onKeyUp:m,tabIndex:h,onMouseOver:v,onMouseLeave:b,...g}=e,[y,w]=(0,i.useState)(!0),[x,O]=(0,i.useState)(!1),E=function(){let e=(0,i.useRef)(new Map),t=e.current,n=(0,i.useCallback)((t,n,r,o)=>{e.current.set(r,{type:n,el:t,options:o}),t.addEventListener(n,r,o)},[]),r=(0,i.useCallback)((t,n,r,o)=>{t.removeEventListener(n,r,o),e.current.delete(r)},[]);return(0,i.useEffect)(()=>()=>{t.forEach((e,t)=>{r(e.el,e.type,t,e.options)})},[r,t]),{add:n,remove:r}}(),k=e=>{e&&"BUTTON"!==e.tagName&&w(!1)},C=n&&!s,j=(0,i.useCallback)(e=>{if(n){e.stopPropagation(),e.preventDefault();return}let t=e.currentTarget;t.focus(),d?.(e)},[n,d]),D=(0,i.useCallback)(e=>{x&&a(e)&&(e.preventDefault(),e.stopPropagation(),O(!1),E.remove(document,"keyup",D,!1))},[x,E]),N=(0,i.useCallback)(e=>{if(p?.(e),n||e.defaultPrevented||e.metaKey||!a(e.nativeEvent)||y)return;let t=l&&"Enter"===e.key,r=u&&" "===e.key;if(r&&(e.preventDefault(),O(!0)),t){e.preventDefault();let t=e.currentTarget;t.click()}E.add(document,"keyup",D,!1)},[n,y,p,l,u,E,D]),_=(0,i.useCallback)(e=>{if(m?.(e),n||e.defaultPrevented||e.metaKey||!a(e.nativeEvent)||y)return;let t=u&&" "===e.key;if(t){e.preventDefault(),O(!1);let t=e.currentTarget;t.click()}},[u,y,n,m]),P=(0,i.useCallback)(e=>{0===e.button&&(O(!1),E.remove(document,"mouseup",P,!1))},[E]),I=(0,i.useCallback)(e=>{if(0!==e.button)return;if(n){e.stopPropagation(),e.preventDefault();return}y||O(!0);let t=e.currentTarget;t.focus({preventScroll:!0}),E.add(document,"mouseup",P,!1),c?.(e)},[n,y,c,E,P]),S=(0,i.useCallback)(e=>{0===e.button&&(y||O(!1),f?.(e))},[f,y]),A=(0,i.useCallback)(e=>{if(n){e.preventDefault();return}v?.(e)},[n,v]),M=(0,i.useCallback)(e=>{x&&(e.preventDefault(),O(!1)),b?.(e)},[x,b]),L=(0,r.lq)(t,k);return y?{...g,ref:L,type:"button","aria-disabled":C?void 0:n,disabled:C,onClick:j,onMouseDown:c,onMouseUp:f,onKeyUp:m,onKeyDown:p,onMouseOver:v,onMouseLeave:b}:{...g,ref:L,role:"button","data-active":(0,o.P)(x),"aria-disabled":n?"true":void 0,tabIndex:C?void 0:y?h:h||0,onClick:j,onMouseDown:I,onMouseUp:S,onKeyUp:_,onKeyDown:N,onMouseOver:A,onMouseLeave:M}}},28228:function(e,t,n){n.d(t,{n:function(){return v}});var r=n(29062),o=n(52110),i=n(67294);function a(e){return e.sort((e,t)=>{let n=e.compareDocumentPosition(t);if(n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY)return -1;if(n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS)return 1;if(!(n&Node.DOCUMENT_POSITION_DISCONNECTED)&&!(n&Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC))return 0;throw Error("Cannot sort the given nodes.")})}let s=e=>"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE;function l(e,t,n){let r=e+1;return n&&r>=t&&(r=0),r}function u(e,t,n){let r=e-1;return n&&r<0&&(r=t),r}let c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=e=>e;var d=Object.defineProperty,p=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t,n)=>(p(e,"symbol"!=typeof t?t+"":t,n),n);class h{constructor(){m(this,"descendants",new Map),m(this,"register",e=>{if(null!=e)return s(e)?this.registerNode(e):t=>{this.registerNode(t,e)}}),m(this,"unregister",e=>{this.descendants.delete(e);let t=a(Array.from(this.descendants.keys()));this.assignIndex(t)}),m(this,"destroy",()=>{this.descendants.clear()}),m(this,"assignIndex",e=>{this.descendants.forEach(t=>{let n=e.indexOf(t.node);t.index=n,t.node.dataset.index=t.index.toString()})}),m(this,"count",()=>this.descendants.size),m(this,"enabledCount",()=>this.enabledValues().length),m(this,"values",()=>{let e=Array.from(this.descendants.values());return e.sort((e,t)=>e.index-t.index)}),m(this,"enabledValues",()=>this.values().filter(e=>!e.disabled)),m(this,"item",e=>{if(0!==this.count())return this.values()[e]}),m(this,"enabledItem",e=>{if(0!==this.enabledCount())return this.enabledValues()[e]}),m(this,"first",()=>this.item(0)),m(this,"firstEnabled",()=>this.enabledItem(0)),m(this,"last",()=>this.item(this.descendants.size-1)),m(this,"lastEnabled",()=>{let e=this.enabledValues().length-1;return this.enabledItem(e)}),m(this,"indexOf",e=>e?this.descendants.get(e)?.index??-1:-1),m(this,"enabledIndexOf",e=>null==e?-1:this.enabledValues().findIndex(t=>t.node.isSameNode(e))),m(this,"next",(e,t=!0)=>{let n=l(e,this.count(),t);return this.item(n)}),m(this,"nextEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let r=this.enabledIndexOf(n.node),o=l(r,this.enabledCount(),t);return this.enabledItem(o)}),m(this,"prev",(e,t=!0)=>{let n=u(e,this.count()-1,t);return this.item(n)}),m(this,"prevEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let r=this.enabledIndexOf(n.node),o=u(r,this.enabledCount()-1,t);return this.enabledItem(o)}),m(this,"registerNode",(e,t)=>{if(!e||this.descendants.has(e))return;let n=Array.from(this.descendants.keys()).concat(e),r=a(n);t?.disabled&&(t.disabled=!!t.disabled);let o={node:e,index:-1,...t};this.descendants.set(e,o),this.assignIndex(r)})}}function v(){let[e,t]=(0,o.k)({name:"DescendantsProvider",errorMessage:"useDescendantsContext must be used within DescendantsProvider"}),n=e=>{let n=t(),[o,a]=(0,i.useState)(-1),s=(0,i.useRef)(null);c(()=>()=>{s.current&&n.unregister(s.current)},[]),c(()=>{if(!s.current)return;let e=Number(s.current.dataset.index);o==e||Number.isNaN(e)||a(e)});let l=e?f(n.register(e)):f(n.register);return{descendants:n,index:o,enabledIndex:n.enabledIndexOf(s.current),register:(0,r.lq)(l,s)}},a=()=>{let e=(0,i.useRef)(new h);return c(()=>()=>e.current.destroy()),e.current};return[e,t,a,n]}},88329:function(e,t,n){n.d(t,{k:function(){return a}});var r=n(85893),o=n(49381),i=n(64993);let a=(0,o.G)(function(e,t){let{direction:n,align:o,justify:a,wrap:s,basis:l,grow:u,shrink:c,...f}=e;return(0,r.jsx)(i.m.div,{ref:t,__css:{display:"flex",flexDirection:n,alignItems:o,justifyContent:a,flexWrap:s,flexBasis:l,flexGrow:u,flexShrink:c},...f})});a.displayName="Flex"},24278:function(e,t,n){n.d(t,{I:function(){return s}});var r=n(85893),o=n(67294),i=n(12553),a=n(49381);function s(e){let{viewBox:t="0 0 24 24",d:n,displayName:s,defaultProps:l={}}=e,u=o.Children.toArray(e.path),c=(0,a.G)((e,o)=>(0,r.jsx)(i.J,{ref:o,viewBox:t,...l,...e,children:u.length?u:(0,r.jsx)("path",{fill:"currentColor",d:n})}));return c.displayName=s,c}},37945:function(e,t,n){n.d(t,{E:function(){return u}});var r=n(85893),o=n(8297),i=n(49381);let a=(0,i.G)(function(e,t){let{htmlWidth:n,htmlHeight:o,alt:i,...a}=e;return(0,r.jsx)("img",{width:n,height:o,ref:t,alt:i,...a})});a.displayName="NativeImage";var s=n(65566),l=n(64993);let u=(0,i.G)(function(e,t){let{fallbackSrc:n,fallback:i,src:u,srcSet:c,align:f,fit:d,loading:p,ignoreFallback:m,crossOrigin:h,fallbackStrategy:v="beforeLoadOrError",referrerPolicy:b,...g}=e,y=null!=p||m||!(void 0!==n||void 0!==i),w=(0,s.d)({...e,crossOrigin:h,ignoreFallback:y}),x=(0,s.z)(w,v),O={ref:t,objectFit:d,objectPosition:f,...y?g:(0,o.C)(g,["onError","onLoad"])};return x?i||(0,r.jsx)(l.m.img,{as:a,className:"chakra-image__placeholder",src:n,...O}):(0,r.jsx)(l.m.img,{as:a,src:u,srcSet:c,crossOrigin:h,loading:p,referrerPolicy:b,className:"chakra-image",...O})});u.displayName="Image"},65566:function(e,t,n){n.d(t,{d:function(){return i},z:function(){return a}});var r=n(12408),o=n(67294);function i(e){let{loading:t,src:n,srcSet:i,onLoad:a,onError:s,crossOrigin:l,sizes:u,ignoreFallback:c}=e,[f,d]=(0,o.useState)("pending");(0,o.useEffect)(()=>{d(n?"loading":"pending")},[n]);let p=(0,o.useRef)(null),m=(0,o.useCallback)(()=>{if(!n)return;h();let e=new Image;e.src=n,l&&(e.crossOrigin=l),i&&(e.srcset=i),u&&(e.sizes=u),t&&(e.loading=t),e.onload=e=>{h(),d("loaded"),a?.(e)},e.onerror=e=>{h(),d("failed"),s?.(e)},p.current=e},[n,l,i,u,a,s,t]),h=()=>{p.current&&(p.current.onload=null,p.current.onerror=null,p.current=null)};return(0,r.G)(()=>{if(!c)return"loading"===f&&m(),()=>{h()}},[f,m,c]),c?"loaded":f}let a=(e,t)=>"loaded"!==e&&"beforeLoadOrError"===t||"failed"===e&&"onError"===t},57879:function(e,t,n){n.d(t,{r:function(){return u}});var r=n(85893),o=n(65544),i=n(34926),a=n(49381),s=n(73035),l=n(64993);let u=(0,a.G)(function(e,t){let n=(0,s.m)("Link",e),{className:a,isExternal:u,...c}=(0,o.L)(e);return(0,r.jsx)(l.m.a,{target:u?"_blank":void 0,rel:u?"noopener":void 0,ref:t,className:(0,i.cx)("chakra-link",a),...c,__css:n})});u.displayName="Link"},91816:function(e,t,n){n.d(t,{S:function(){return s}});var r=n(79115),o=n(1185),i=n(90038),a=n(37984);function s(e,t){let n=(0,r.Kn)(t)?t:{fallback:t??"base"},s=function(e){let t=(0,r.Kn)(e)?e:{fallback:e??"base"},n=(0,a.F)(),o=n.__breakpoints.details.map(({minMaxQuery:e,breakpoint:t})=>({breakpoint:t,query:e.replace("@media screen and ","")})),s=o.map(e=>e.breakpoint===t.fallback),l=(0,i.a)(o.map(e=>e.query),{fallback:s,ssr:t.ssr}),u=l.findIndex(e=>!0==e);return o[u]?.breakpoint??t.fallback}(n),l=(0,a.F)();if(!s)return;let u=Array.from(l.__breakpoints?.keys||[]),c=Array.isArray(e)?Object.fromEntries(Object.entries((0,o.Yq)(e,u)).map(([e,t])=>[e,t])):e;return function(e,t,n=o.AV){let r=Object.keys(e).indexOf(t);if(-1!==r)return e[t];let i=n.indexOf(t);for(;i>=0;){let t=n[i];if(e.hasOwnProperty(t)){r=i;break}i-=1}if(-1!==r){let t=n[r];return e[t]}}(c,s,u)}},90038:function(e,t,n){n.d(t,{a:function(){return i}});var r=n(67294),o=n(28497);function i(e,t={}){let{ssr:n=!0,fallback:i}=t,{getWindow:a}=(0,o.O)(),s=Array.isArray(e)?e:[e],l=Array.isArray(i)?i:[i];l=l.filter(e=>null!=e);let[u,c]=(0,r.useState)(()=>s.map((e,t)=>({media:e,matches:n?!!l[t]:a().matchMedia(e).matches})));return(0,r.useEffect)(()=>{let e=a();c(s.map(t=>({media:t,matches:e.matchMedia(t).matches})));let t=s.map(t=>e.matchMedia(t)),n=e=>{c(t=>t.slice().map(t=>t.media===e.media?{...t,matches:e.matches}:t))};return t.forEach(e=>{"function"==typeof e.addListener?e.addListener(n):e.addEventListener("change",n)}),()=>{t.forEach(e=>{"function"==typeof e.removeListener?e.removeListener(n):e.removeEventListener("change",n)})}},[a]),u.map(e=>e.matches)}},12095:function(e,t,n){n.d(t,{j:function(){return c}});var r=n(85893),o=n(34926),i=n(90608),a=n(6662),s=n(49381),l=n(64993);let u=(0,s.G)((e,t)=>{let n=(0,i.x)();return(0,r.jsx)(l.m.button,{ref:t,...e,__css:{display:"inline-flex",appearance:"none",alignItems:"center",outline:0,...n.button}})}),c=(0,s.G)((e,t)=>{let{children:n,as:i,...s}=e,c=(0,a.zZ)(s,t);return(0,r.jsx)(i||u,{...c,className:(0,o.cx)("chakra-menu__menu-button",e.className),children:(0,r.jsx)(l.m.span,{__css:{pointerEvents:"none",flex:"1 1 auto",minW:0},children:e.children})})});c.displayName="MenuButton"},63162:function(e,t,n){n.d(t,{s:function(){return p}});var r=n(85893),o=n(34926),i=n(90608),a=n(49381),s=n(64993);let l=(0,a.G)((e,t)=>{let n=(0,i.x)();return(0,r.jsx)(s.m.span,{ref:t,...e,__css:n.command,className:"chakra-menu__command"})});l.displayName="MenuCommand";var u=n(67294);let c=e=>{let{className:t,children:n,...a}=e,l=(0,i.x)(),c=u.Children.only(n),f=(0,u.isValidElement)(c)?(0,u.cloneElement)(c,{focusable:"false","aria-hidden":!0,className:(0,o.cx)("chakra-menu__icon",c.props.className)}):null,d=(0,o.cx)("chakra-menu__icon-wrapper",t);return(0,r.jsx)(s.m.span,{className:d,...a,__css:l.icon,children:f})};c.displayName="MenuIcon";let f=(0,a.G)((e,t)=>{let{type:n,...o}=e,a=(0,i.x)(),l=o.as||n?n??void 0:"button",c=(0,u.useMemo)(()=>({textDecoration:"none",color:"inherit",userSelect:"none",display:"flex",width:"100%",alignItems:"center",textAlign:"start",flex:"0 0 auto",outline:0,...a.item}),[a.item]);return(0,r.jsx)(s.m.button,{ref:t,type:l,...o,__css:c})});var d=n(6662);let p=(0,a.G)((e,t)=>{let{icon:n,iconSpacing:i="0.75rem",command:a,commandSpacing:s="0.75rem",children:u,...p}=e,m=(0,d.iX)(p,t),h=n||a?(0,r.jsx)("span",{style:{pointerEvents:"none",flex:1},children:u}):u;return(0,r.jsxs)(f,{...m,className:(0,o.cx)("chakra-menu__menuitem",m.className),children:[n&&(0,r.jsx)(c,{fontSize:"0.8em",marginEnd:i,children:n}),h,a&&(0,r.jsx)(l,{marginStart:s,children:a})]})});p.displayName="MenuItem"},34302:function(e,t,n){n.d(t,{q:function(){return p}});var r=n(85893),o=n(34926),i=n(68928),a=n(97340),s=n(90608),l=n(6662),u=n(64993),c=n(49381);let f={enter:{visibility:"visible",opacity:1,scale:1,transition:{duration:.2,ease:[.4,0,.2,1]}},exit:{transitionEnd:{visibility:"hidden"},opacity:0,scale:.8,transition:{duration:.1,easings:"easeOut"}}},d=(0,u.m)(a.E.div),p=(0,c.G)(function(e,t){let{rootProps:n,motionProps:a,...c}=e,{isOpen:p,onTransitionEnd:m,unstable__animationState:h}=(0,l.Xh)(),v=(0,l._l)(c,t),b=(0,l.Qh)(n),g=(0,s.x)();return(0,r.jsx)(u.m.div,{...b,__css:{zIndex:e.zIndex??g.list?.zIndex},children:(0,r.jsx)(d,{variants:f,initial:!1,animate:p?"enter":"exit",__css:{outline:0,...g.list},...a,...v,className:(0,o.cx)("chakra-menu__menu-list",v.className),onUpdate:m,onAnimationComplete:(0,i.P)(h.onComplete,v.onAnimationComplete)})})});p.displayName="MenuList"},90608:function(e,t,n){n.d(t,{v:function(){return p},x:function(){return d}});var r=n(85893),o=n(65544),i=n(52110),a=n(2847),s=n(67294),l=n(6662),u=n(37984),c=n(73035);let[f,d]=(0,i.k)({name:"MenuStylesContext",errorMessage:"useMenuStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Menu />\" "}),p=e=>{let{children:t}=e,n=(0,c.j)("Menu",e),i=(0,o.L)(e),{direction:d}=(0,u.F)(),{descendants:p,...m}=(0,l.H9)({...i,direction:d}),h=(0,s.useMemo)(()=>m,[m]),{isOpen:v,onClose:b,forceUpdate:g}=h;return(0,r.jsx)(l.wN,{value:p,children:(0,r.jsx)(l.Kb,{value:h,children:(0,r.jsx)(f,{value:n,children:(0,a.P)(t,{isOpen:v,onClose:b,forceUpdate:g})})})})};p.displayName="Menu"},6662:function(e,t,n){n.d(t,{wN:function(){return w},Kb:function(){return k},H9:function(){return D},zZ:function(){return N},Xh:function(){return C},iX:function(){return S},_l:function(){return P},Qh:function(){return I}});var r=n(83658),o=n(67294),i=n(95372);function a(e,t){let n=e.composedPath?.()[0]??e.target;if(n){let e=s(n);if(!e.contains(n))return!1}return!t.current?.contains(n)}function s(e){return e?.ownerDocument??document}var l=n(59255),u=n(90191),c=n(4840),f=n(49882),d=n(29062),p=n(52110),m=n(20397),h=n(68928),v=n(1071),b=n(28228),g=n(66822),y=n(94691);let[w,x,O,E]=(0,b.n)(),[k,C]=(0,p.k)({strict:!1,name:"MenuContext"});function j(e){return e?.ownerDocument??document}function D(e={}){let{id:t,closeOnSelect:n=!0,closeOnBlur:d=!0,initialFocusRef:p,autoSelect:m=!0,isLazy:h,isOpen:v,defaultIsOpen:b,onClose:y,onOpen:w,placement:x="bottom-start",lazyBehavior:E="unmount",direction:k,computePositionOnMount:C=!1,...D}=e,N=(0,o.useRef)(null),_=(0,o.useRef)(null),P=(0,o.useRef)(!0),I=O(),S=(0,o.useCallback)(()=>{requestAnimationFrame(()=>{N.current?.focus({preventScroll:!1})})},[]),A=(0,o.useCallback)(()=>{let e=setTimeout(()=>{if(p)p.current?.focus();else if(I.count()){let e=I.firstEnabled();e&&H(e.index)}else N.current?.focus({preventScroll:!1})});$.current.add(e)},[I,p]),M=(0,o.useCallback)(()=>{let e=setTimeout(()=>{if(I.count()){let e=I.lastEnabled();e&&H(e.index)}else N.current?.focus({preventScroll:!1})});$.current.add(e)},[I]),L=(0,o.useCallback)(()=>{w?.(),m?A():S()},[m,A,S,w]),{isOpen:T,onOpen:R,onClose:q,onToggle:W}=(0,r.q)({isOpen:v,defaultIsOpen:b,onClose:y,onOpen:L});!function(e){let{ref:t,handler:n,enabled:r=!0}=e,l=(0,i.W)(n),u=(0,o.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),c=u.current;(0,o.useEffect)(()=>{if(!r)return;let e=e=>{a(e,t)&&(c.isPointerDown=!0)},o=e=>{if(c.ignoreEmulatedMouseEvents){c.ignoreEmulatedMouseEvents=!1;return}c.isPointerDown&&n&&a(e,t)&&(c.isPointerDown=!1,l(e))},i=e=>{c.ignoreEmulatedMouseEvents=!0,n&&c.isPointerDown&&a(e,t)&&(c.isPointerDown=!1,l(e))},u=s(t.current);return u.addEventListener("mousedown",e,!0),u.addEventListener("mouseup",o,!0),u.addEventListener("touchstart",e,!0),u.addEventListener("touchend",i,!0),()=>{u.removeEventListener("mousedown",e,!0),u.removeEventListener("mouseup",o,!0),u.removeEventListener("touchstart",e,!0),u.removeEventListener("touchend",i,!0)}},[n,t,l,c,r])}({enabled:T&&d,ref:N,handler:e=>{let t=e.composedPath?.()?.[0]??e.target;_.current?.contains(t)||q()}});let z=(0,g.D)({...D,enabled:T||C,placement:x,direction:k}),[B,H]=(0,o.useState)(-1);!function(e,t){let{shouldFocus:n,visible:r,focusRef:o}=t,i=n&&!r;(0,c.r)(()=>{let t;if(!i||function(e){let t=e.current;if(!t)return!1;let n=(0,l.vY)(t);return!(!n||t.contains(n))&&!!(0,u.Wq)(n)}(e))return;let n=o?.current||e.current;if(n)return t=requestAnimationFrame(()=>{n.focus({preventScroll:!0})}),()=>{cancelAnimationFrame(t)}},[i,e,o])}(N,{focusRef:_,visible:T,shouldFocus:!0});let V=function(e){let{isOpen:t,ref:n}=e,[r,i]=(0,o.useState)(t),[a,s]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{a||(i(t),s(!0))},[t,a,r]),(0,f.O)(()=>n.current,"animationend",()=>{i(t)}),{present:!(!t&&!r),onComplete(){let e=(0,l.kR)(n.current),t=new e.CustomEvent("animationend",{bubbles:!0});n.current?.dispatchEvent(t)}}}({isOpen:T,ref:N}),[U,F]=function(e,...t){let n=function(e,t){let n=(0,o.useId)();return(0,o.useMemo)(()=>e||[t,n].filter(Boolean).join("-"),[e,t,n])}(e);return(0,o.useMemo)(()=>t.map(e=>`${e}-${n}`),[n,t])}(t,"menu-button","menu-list"),G=(0,o.useCallback)(()=>{R(),S()},[R,S]),$=(0,o.useRef)(new Set([]));(0,o.useEffect)(()=>{let e=$.current;return()=>{e.forEach(e=>clearTimeout(e)),e.clear()}},[]),(0,c.r)(()=>{T||(H(-1),N.current?.scrollTo(0,0))},[T]),(0,c.r)(()=>{T&&-1===B&&S()},[B,T]),(0,o.useEffect)(()=>{if(!T)return;let e=I.item(B);e?.node?.focus({preventScroll:!P.current})},[I,B,T]);let K=(0,o.useCallback)(()=>{R(),A()},[A,R]),X=(0,o.useCallback)(()=>{P.current=!0,R(),M()},[R,M]),Y=(0,o.useCallback)(()=>{let e=j(N.current),t=N.current?.contains(e.activeElement);if(!(T&&!t))return;let n=I.item(B)?.node;n?.focus({preventScroll:!P.current})},[T,B,I]);return{openAndFocusMenu:G,openAndFocusFirstItem:K,openAndFocusLastItem:X,onTransitionEnd:Y,unstable__animationState:V,descendants:I,popper:z,buttonId:U,menuId:F,forceUpdate:z.forceUpdate,orientation:"vertical",isOpen:T,onToggle:W,onOpen:R,onClose:q,menuRef:N,buttonRef:_,focusedIndex:B,closeOnSelect:n,closeOnBlur:d,autoSelect:m,setFocusedIndex:H,isLazy:h,lazyBehavior:E,initialFocusRef:p,scrollIntoViewRef:P}}function N(e={},t=null){let n=C(),{onToggle:r,popper:i,openAndFocusFirstItem:a,openAndFocusLastItem:s,scrollIntoViewRef:l}=n,u=(0,o.useCallback)(e=>{let t=e.key,n={Enter:a,ArrowDown:a,ArrowUp:s}[t];n&&(l.current=!0,e.preventDefault(),e.stopPropagation(),n(e))},[a,s,l]);return{...e,ref:(0,d.lq)(n.buttonRef,t,i.referenceRef),id:n.buttonId,"data-active":(0,m.P)(n.isOpen),"aria-expanded":n.isOpen,"aria-haspopup":"menu","aria-controls":n.menuId,onClick:(0,h.v)(e.onClick,r),onKeyDown:(0,h.v)(e.onKeyDown,u)}}function _(e){return function(e){if(!(null!=e&&"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE))return!1;let t=e.ownerDocument.defaultView??window;return e instanceof t.HTMLElement}(e)&&!!e?.getAttribute("role")?.startsWith("menuitem")}function P(e={},t=null){let n=C();if(!n)throw Error("useMenuContext: context is undefined. Seems you forgot to wrap component within <Menu>");let{focusedIndex:r,setFocusedIndex:i,menuRef:a,isOpen:s,onClose:l,menuId:u,isLazy:c,lazyBehavior:f,scrollIntoViewRef:p,unstable__animationState:m}=n,b=x(),g=function(e={}){let{timeout:t=300,preventDefault:n=()=>!0}=e,[r,i]=(0,o.useState)([]),a=(0,o.useRef)(void 0),s=()=>{a.current&&(clearTimeout(a.current),a.current=null)},l=()=>{s(),a.current=setTimeout(()=>{i([]),a.current=null},t)};return(0,o.useEffect)(()=>s,[]),function(e){return t=>{if("Backspace"===t.key){let e=[...r];e.pop(),i(e);return}if(function(e){let{key:t}=e;return 1===t.length||t.length>1&&/[^a-zA-Z0-9]/.test(t)}(t)){let o=r.concat(t.key);n(t)&&(t.preventDefault(),t.stopPropagation()),i(o),e(o.join("")),l()}}}}({preventDefault:e=>" "!==e.key&&_(e.target)}),y=(0,o.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=e.key,n={Tab:e=>e.preventDefault(),Escape:e=>{e.stopPropagation(),l()},ArrowDown:()=>{p.current=!0;let e=b.nextEnabled(r)??b.firstEnabled();e&&i(e.index)},ArrowUp:()=>{p.current=!0;let e=b.prevEnabled(r)??b.firstEnabled();e&&i(e.index)}}[t];if(n){e.preventDefault(),n(e);return}let o=g(e=>{let t=function(e,t,n,r){if(null==t)return r;if(!r){let r=e.find(e=>n(e).toLowerCase().startsWith(t.toLowerCase()));return r}let o=e.filter(e=>n(e).toLowerCase().startsWith(t.toLowerCase()));if(o.length>0){let t;if(o.includes(r)){let e=o.indexOf(r);return(t=e+1)===o.length&&(t=0),o[t]}return t=e.indexOf(o[0]),e[t]}return r}(b.values(),e,e=>e?.node?.textContent??"",b.item(r));if(t){let e=b.indexOf(t.node);i(e)}});_(e.target)&&o(e)},[b,r,g,l,i,p]),w=(0,o.useRef)(!1);s&&(w.current=!0);let O=(0,v.k)({wasSelected:w.current,enabled:c,mode:f,isSelected:m.present});return{...e,ref:(0,d.lq)(a,t),children:O?e.children:null,tabIndex:-1,role:"menu",id:u,style:{...e.style,transformOrigin:"var(--popper-transform-origin)"},"aria-orientation":"vertical",onKeyDown:(0,h.v)(e.onKeyDown,y)}}function I(e={}){let{popper:t,isOpen:n}=C();return t.getPopperProps({...e,style:{visibility:n?"visible":"hidden",...e.style}})}function S(e={},t=null){let{onMouseEnter:n,onMouseMove:r,onMouseLeave:i,onClick:a,onFocus:s,isDisabled:l,isFocusable:u,closeOnSelect:c,type:f,...p}=e,m=C(),{setFocusedIndex:h,focusedIndex:v,closeOnSelect:b,onClose:g,menuId:w,scrollIntoViewRef:x}=m,O=(0,o.useRef)(null),k=`${w}-menuitem-${(0,o.useId)()}`,{index:D,register:N}=E({disabled:l&&!u}),P=(0,o.useCallback)(e=>{n?.(e),l||(x.current=!1,h(D))},[h,D,l,n,x]),I=(0,o.useCallback)(e=>{r?.(e),O.current&&!function(e){let t=j(e);return t.activeElement===e}(O.current)&&P(e)},[P,r]),S=(0,o.useCallback)(e=>{i?.(e),l||h(-1)},[h,l,i]),A=(0,o.useCallback)(e=>{a?.(e),_(e.currentTarget)&&(c??b)&&g()},[g,a,b,c]),M=(0,o.useCallback)(e=>{s?.(e),h(D)},[h,s,D]),L=(0,y.h)({onClick:A,onFocus:M,onMouseEnter:P,onMouseMove:I,onMouseLeave:S,ref:(0,d.lq)(N,O,t),isDisabled:l,isFocusable:u});return{...p,...L,type:f??L.type,id:k,role:"menuitem",tabIndex:D===v?0:-1}}},66822:function(e,t,n){n.d(t,{D:function(){return eE}});var r,o,i,a,s,l=n(29062);function u(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function c(e){var t=u(e).Element;return e instanceof t||e instanceof Element}function f(e){var t=u(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function d(e){if("undefined"==typeof ShadowRoot)return!1;var t=u(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var p=Math.max,m=Math.min,h=Math.round;function v(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function b(){return!/^((?!chrome|android).)*safari/i.test(v())}function g(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&f(e)&&(o=e.offsetWidth>0&&h(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&h(r.height)/e.offsetHeight||1);var a=(c(e)?u(e):window).visualViewport,s=!b()&&n,l=(r.left+(s&&a?a.offsetLeft:0))/o,d=(r.top+(s&&a?a.offsetTop:0))/i,p=r.width/o,m=r.height/i;return{width:p,height:m,top:d,right:l+p,bottom:d+m,left:l,x:l,y:d}}function y(e){var t=u(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function w(e){return e?(e.nodeName||"").toLowerCase():null}function x(e){return((c(e)?e.ownerDocument:e.document)||window.document).documentElement}function O(e){return g(x(e)).left+y(e).scrollLeft}function E(e){return u(e).getComputedStyle(e)}function k(e){var t=E(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function C(e){var t=g(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function j(e){return"html"===w(e)?e:e.assignedSlot||e.parentNode||(d(e)?e.host:null)||x(e)}function D(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(w(t))>=0?t.ownerDocument.body:f(t)&&k(t)?t:e(j(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=u(r),a=o?[i].concat(i.visualViewport||[],k(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(D(j(a)))}function N(e){return f(e)&&"fixed"!==E(e).position?e.offsetParent:null}function _(e){for(var t=u(e),n=N(e);n&&["table","td","th"].indexOf(w(n))>=0&&"static"===E(n).position;)n=N(n);return n&&("html"===w(n)||"body"===w(n)&&"static"===E(n).position)?t:n||function(e){var t=/firefox/i.test(v());if(/Trident/i.test(v())&&f(e)&&"fixed"===E(e).position)return null;var n=j(e);for(d(n)&&(n=n.host);f(n)&&0>["html","body"].indexOf(w(n));){var r=E(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var P="bottom",I="right",S="left",A="auto",M=["top",P,I,S],L="start",T="viewport",R="popper",q=M.reduce(function(e,t){return e.concat([t+"-"+L,t+"-end"])},[]),W=[].concat(M,[A]).reduce(function(e,t){return e.concat([t,t+"-"+L,t+"-end"])},[]),z=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],B={placement:"bottom",modifiers:[],strategy:"absolute"};function H(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var V={passive:!0};function U(e){return e.split("-")[0]}function F(e){return e.split("-")[1]}function G(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function $(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?U(o):null,a=o?F(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case P:t={x:s,y:n.y+n.height};break;case I:t={x:n.x+n.width,y:l};break;case S:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=i?G(i):null;if(null!=u){var c="y"===u?"height":"width";switch(a){case L:t[u]=t[u]-(n[c]/2-r[c]/2);break;case"end":t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}var K={top:"auto",right:"auto",bottom:"auto",left:"auto"};function X(e){var t,n,r,o,i,a,s,l=e.popper,c=e.popperRect,f=e.placement,d=e.variation,p=e.offsets,m=e.position,v=e.gpuAcceleration,b=e.adaptive,g=e.roundOffsets,y=e.isFixed,w=p.x,O=void 0===w?0:w,k=p.y,C=void 0===k?0:k,j="function"==typeof g?g({x:O,y:C}):{x:O,y:C};O=j.x,C=j.y;var D=p.hasOwnProperty("x"),N=p.hasOwnProperty("y"),A=S,M="top",L=window;if(b){var T=_(l),R="clientHeight",q="clientWidth";T===u(l)&&"static"!==E(T=x(l)).position&&"absolute"===m&&(R="scrollHeight",q="scrollWidth"),("top"===f||(f===S||f===I)&&"end"===d)&&(M=P,C-=(y&&T===L&&L.visualViewport?L.visualViewport.height:T[R])-c.height,C*=v?1:-1),(f===S||("top"===f||f===P)&&"end"===d)&&(A=I,O-=(y&&T===L&&L.visualViewport?L.visualViewport.width:T[q])-c.width,O*=v?1:-1)}var W=Object.assign({position:m},b&&K),z=!0===g?(t={x:O,y:C},n=u(l),r=t.x,o=t.y,{x:h(r*(i=n.devicePixelRatio||1))/i||0,y:h(o*i)/i||0}):{x:O,y:C};return(O=z.x,C=z.y,v)?Object.assign({},W,((s={})[M]=N?"0":"",s[A]=D?"0":"",s.transform=1>=(L.devicePixelRatio||1)?"translate("+O+"px, "+C+"px)":"translate3d("+O+"px, "+C+"px, 0)",s)):Object.assign({},W,((a={})[M]=N?C+"px":"",a[A]=D?O+"px":"",a.transform="",a))}var Y={left:"right",right:"left",bottom:"top",top:"bottom"};function Z(e){return e.replace(/left|right|bottom|top/g,function(e){return Y[e]})}var Q={start:"end",end:"start"};function J(e){return e.replace(/start|end/g,function(e){return Q[e]})}function ee(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&d(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function et(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function en(e,t,n){var r,o,i,a,s,l,f,d,m,h;return t===T?et(function(e,t){var n=u(e),r=x(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;var c=b();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s+O(e),y:l}}(e,n)):c(t)?((r=g(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):et((o=x(e),a=x(o),s=y(o),l=null==(i=o.ownerDocument)?void 0:i.body,f=p(a.scrollWidth,a.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),d=p(a.scrollHeight,a.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),m=-s.scrollLeft+O(o),h=-s.scrollTop,"rtl"===E(l||a).direction&&(m+=p(a.clientWidth,l?l.clientWidth:0)-f),{width:f,height:d,x:m,y:h}))}function er(){return{top:0,right:0,bottom:0,left:0}}function eo(e){return Object.assign({},er(),e)}function ei(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function ea(e,t){void 0===t&&(t={});var n,r,o,i,a,s,l,u=t,d=u.placement,h=void 0===d?e.placement:d,v=u.strategy,b=void 0===v?e.strategy:v,y=u.boundary,O=u.rootBoundary,k=u.elementContext,C=void 0===k?R:k,N=u.altBoundary,S=u.padding,A=void 0===S?0:S,L=eo("number"!=typeof A?A:ei(A,M)),q=e.rects.popper,W=e.elements[void 0!==N&&N?C===R?"reference":R:C],z=(n=c(W)?W:W.contextElement||x(e.elements.popper),s=(a=[].concat("clippingParents"===(r=void 0===y?"clippingParents":y)?(o=D(j(n)),c(i=["absolute","fixed"].indexOf(E(n).position)>=0&&f(n)?_(n):n)?o.filter(function(e){return c(e)&&ee(e,i)&&"body"!==w(e)}):[]):[].concat(r),[void 0===O?T:O]))[0],(l=a.reduce(function(e,t){var r=en(n,t,b);return e.top=p(r.top,e.top),e.right=m(r.right,e.right),e.bottom=m(r.bottom,e.bottom),e.left=p(r.left,e.left),e},en(n,s,b))).width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l),B=g(e.elements.reference),H=$({reference:B,element:q,strategy:"absolute",placement:h}),V=et(Object.assign({},q,H)),U=C===R?V:B,F={top:z.top-U.top+L.top,bottom:U.bottom-z.bottom+L.bottom,left:z.left-U.left+L.left,right:U.right-z.right+L.right},G=e.modifiersData.offset;if(C===R&&G){var K=G[h];Object.keys(F).forEach(function(e){var t=[I,P].indexOf(e)>=0?1:-1,n=["top",P].indexOf(e)>=0?"y":"x";F[e]+=K[n]*t})}return F}function es(e,t,n){return p(e,m(t,n))}function el(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function eu(e){return["top",I,P,S].some(function(t){return e[t]>=0})}var ec=(i=void 0===(o=(r={defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,l=u(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",n.update,V)}),s&&l.addEventListener("resize",n.update,V),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",n.update,V)}),s&&l.removeEventListener("resize",n.update,V)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=$({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:U(t.placement),variation:F(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,X(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,X(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];f(o)&&w(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});f(r)&&w(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=W.reduce(function(e,n){var r,o,a,s,l,u;return e[n]=(r=t.rects,a=[S,"top"].indexOf(o=U(n))>=0?-1:1,l=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],u=s[1],l=l||0,u=(u||0)*a,[S,I].indexOf(o)>=0?{x:u,y:l}:{x:l,y:u}),e},{}),s=a[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,u=n.padding,c=n.boundary,f=n.rootBoundary,d=n.altBoundary,p=n.flipVariations,m=void 0===p||p,h=n.allowedAutoPlacements,v=t.options.placement,b=U(v),g=[v].concat(l||(b!==v&&m?function(e){if(U(e)===A)return[];var t=Z(e);return[J(e),t,J(t)]}(v):[Z(v)])).reduce(function(e,n){var r,o,i,a,s,l,d,p,v,b,g,y;return e.concat(U(n)===A?(o=(r={placement:n,boundary:c,rootBoundary:f,padding:u,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,l=r.flipVariations,p=void 0===(d=r.allowedAutoPlacements)?W:d,0===(g=(b=(v=F(o))?l?q:q.filter(function(e){return F(e)===v}):M).filter(function(e){return p.indexOf(e)>=0})).length&&(g=b),Object.keys(y=g.reduce(function(e,n){return e[n]=ea(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[U(n)],e},{})).sort(function(e,t){return y[e]-y[t]})):n)},[]),y=t.rects.reference,w=t.rects.popper,x=new Map,O=!0,E=g[0],k=0;k<g.length;k++){var C=g[k],j=U(C),D=F(C)===L,N=["top",P].indexOf(j)>=0,_=N?"width":"height",T=ea(t,{placement:C,boundary:c,rootBoundary:f,altBoundary:d,padding:u}),R=N?D?I:S:D?P:"top";y[_]>w[_]&&(R=Z(R));var z=Z(R),B=[];if(i&&B.push(T[j]<=0),s&&B.push(T[R]<=0,T[z]<=0),B.every(function(e){return e})){E=C,O=!1;break}x.set(C,B)}if(O)for(var H=m?3:1,V=function(e){var t=g.find(function(t){var n=x.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},G=H;G>0&&"break"!==V(G);G--);t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,a=n.boundary,s=n.rootBoundary,l=n.altBoundary,u=n.padding,c=n.tether,f=void 0===c||c,d=n.tetherOffset,h=void 0===d?0:d,v=ea(t,{boundary:a,rootBoundary:s,padding:u,altBoundary:l}),b=U(t.placement),g=F(t.placement),y=!g,w=G(b),x="x"===w?"y":"x",O=t.modifiersData.popperOffsets,E=t.rects.reference,k=t.rects.popper,j="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,D="number"==typeof j?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),N=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,A={x:0,y:0};if(O){if(void 0===o||o){var M,T="y"===w?"top":S,R="y"===w?P:I,q="y"===w?"height":"width",W=O[w],z=W+v[T],B=W-v[R],H=f?-k[q]/2:0,V=g===L?E[q]:k[q],$=g===L?-k[q]:-E[q],K=t.elements.arrow,X=f&&K?C(K):{width:0,height:0},Y=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:er(),Z=Y[T],Q=Y[R],J=es(0,E[q],X[q]),ee=y?E[q]/2-H-J-Z-D.mainAxis:V-J-Z-D.mainAxis,et=y?-E[q]/2+H+J+Q+D.mainAxis:$+J+Q+D.mainAxis,en=t.elements.arrow&&_(t.elements.arrow),eo=en?"y"===w?en.clientTop||0:en.clientLeft||0:0,ei=null!=(M=null==N?void 0:N[w])?M:0,el=es(f?m(z,W+ee-ei-eo):z,W,f?p(B,W+et-ei):B);O[w]=el,A[w]=el-W}if(void 0!==i&&i){var eu,ec,ef=O[x],ed="y"===x?"height":"width",ep=ef+v["x"===w?"top":S],em=ef-v["x"===w?P:I],eh=-1!==["top",S].indexOf(b),ev=null!=(eu=null==N?void 0:N[x])?eu:0,eb=eh?ep:ef-E[ed]-k[ed]-ev+D.altAxis,eg=eh?ef+E[ed]+k[ed]-ev-D.altAxis:em,ey=f&&eh?(ec=es(eb,ef,eg))>eg?eg:ec:es(f?eb:ep,ef,f?eg:em);O[x]=ey,A[x]=ey-ef}t.modifiersData[r]=A}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n,r=e.state,o=e.name,i=e.options,a=r.elements.arrow,s=r.modifiersData.popperOffsets,l=U(r.placement),u=G(l),c=[S,I].indexOf(l)>=0?"height":"width";if(a&&s){var f=eo("number"!=typeof(t="function"==typeof(t=i.padding)?t(Object.assign({},r.rects,{placement:r.placement})):t)?t:ei(t,M)),d=C(a),p=r.rects.reference[c]+r.rects.reference[u]-s[u]-r.rects.popper[c],m=s[u]-r.rects.reference[u],h=_(a),v=h?"y"===u?h.clientHeight||0:h.clientWidth||0:0,b=f["y"===u?"top":S],g=v-d[c]-f["y"===u?P:I],y=v/2-d[c]/2+(p/2-m/2),w=es(b,y,g);r.modifiersData[o]=((n={})[u]=w,n.centerOffset=w-y,n)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&ee(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ea(t,{elementContext:"reference"}),s=ea(t,{altBoundary:!0}),l=el(a,r),u=el(s,o,i),c=eu(l),f=eu(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":f})}}]}).defaultModifiers)?[]:o,s=void 0===(a=r.defaultOptions)?B:a,function(e,t,n){void 0===n&&(n=s);var r,o={placement:"bottom",orderedModifiers:[],options:Object.assign({},B,s),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},a=[],l=!1,d={state:o,setOptions:function(n){var r,l,u,f,m,h="function"==typeof n?n(o.options):n;p(),o.options=Object.assign({},s,o.options,h),o.scrollParents={reference:c(e)?D(e):e.contextElement?D(e.contextElement):[],popper:D(t)};var v=(l=Object.keys(r=[].concat(i,o.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return r[e]}),u=new Map,f=new Set,m=[],l.forEach(function(e){u.set(e.name,e)}),l.forEach(function(e){f.has(e.name)||function e(t){f.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!f.has(t)){var n=u.get(t);n&&e(n)}}),m.push(t)}(e)}),z.reduce(function(e,t){return e.concat(m.filter(function(e){return e.phase===t}))},[]));return o.orderedModifiers=v.filter(function(e){return e.enabled}),o.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var i=r({state:o,name:t,instance:d,options:void 0===n?{}:n});a.push(i||function(){})}}),d.update()},forceUpdate:function(){if(!l){var e,t,n,r,i,a,s,c,p,m,v,b,E=o.elements,j=E.reference,D=E.popper;if(H(j,D)){o.rects={reference:(t=_(D),n="fixed"===o.options.strategy,r=f(t),c=f(t)&&(a=h((i=t.getBoundingClientRect()).width)/t.offsetWidth||1,s=h(i.height)/t.offsetHeight||1,1!==a||1!==s),p=x(t),m=g(j,c,n),v={scrollLeft:0,scrollTop:0},b={x:0,y:0},(r||!r&&!n)&&(("body"!==w(t)||k(p))&&(v=(e=t)!==u(e)&&f(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:y(e)),f(t)?(b=g(t,!0),b.x+=t.clientLeft,b.y+=t.clientTop):p&&(b.x=O(p))),{x:m.left+v.scrollLeft-b.x,y:m.top+v.scrollTop-b.y,width:m.width,height:m.height}),popper:C(D)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach(function(e){return o.modifiersData[e.name]=Object.assign({},e.data)});for(var N=0;N<o.orderedModifiers.length;N++){if(!0===o.reset){o.reset=!1,N=-1;continue}var P=o.orderedModifiers[N],I=P.fn,S=P.options,A=void 0===S?{}:S,M=P.name;"function"==typeof I&&(o=I({state:o,options:A,name:M,instance:d})||o)}}}},update:function(){return r||(r=new Promise(function(e){Promise.resolve().then(function(){r=void 0,e(new Promise(function(e){d.forceUpdate(),e(o)}))})})),r},destroy:function(){p(),l=!0}};if(!H(e,t))return d;function p(){a.forEach(function(e){return e()}),a=[]}return d.setOptions(n).then(function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)}),d}),ef=n(67294),ed=n(38898);let ep={name:"matchWidth",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.width=`${e.rects.reference.width}px`},effect:({state:e})=>()=>{let t=e.elements.reference;e.elements.popper.style.width=`${t.offsetWidth}px`}},em={name:"transformOrigin",enabled:!0,phase:"write",fn:({state:e})=>{eh(e)},effect:({state:e})=>()=>{eh(e)}},eh=e=>{e.elements.popper.style.setProperty(ed.Dq.transformOrigin.var,(0,ed.mv)(e.placement))},ev={name:"positionArrow",enabled:!0,phase:"afterWrite",fn:({state:e})=>{eb(e)}},eb=e=>{if(!e.placement)return;let t=eg(e.placement);if(e.elements?.arrow&&t){Object.assign(e.elements.arrow.style,{[t.property]:t.value,width:ed.Dq.arrowSize.varRef,height:ed.Dq.arrowSize.varRef,zIndex:-1});let n={[ed.Dq.arrowSizeHalf.var]:`calc(${ed.Dq.arrowSize.varRef} / 2 - 1px)`,[ed.Dq.arrowOffset.var]:`calc(${ed.Dq.arrowSizeHalf.varRef} * -1)`};for(let t in n)e.elements.arrow.style.setProperty(t,n[t])}},eg=e=>e.startsWith("top")?{property:"bottom",value:ed.Dq.arrowOffset.varRef}:e.startsWith("bottom")?{property:"top",value:ed.Dq.arrowOffset.varRef}:e.startsWith("left")?{property:"right",value:ed.Dq.arrowOffset.varRef}:e.startsWith("right")?{property:"left",value:ed.Dq.arrowOffset.varRef}:void 0,ey={name:"innerArrow",enabled:!0,phase:"main",requires:["arrow"],fn:({state:e})=>{ew(e)},effect:({state:e})=>()=>{ew(e)}},ew=e=>{if(!e.elements.arrow)return;let t=e.elements.arrow.querySelector("[data-popper-arrow-inner]");if(!t)return;let n=(0,ed.Ke)(e.placement);n&&t.style.setProperty("--popper-arrow-default-shadow",n),Object.assign(t.style,{transform:"rotate(45deg)",background:ed.Dq.arrowBg.varRef,top:0,left:0,width:"100%",height:"100%",position:"absolute",zIndex:"inherit",boxShadow:"var(--popper-arrow-shadow, var(--popper-arrow-default-shadow))"})},ex={"start-start":{ltr:"left-start",rtl:"right-start"},"start-end":{ltr:"left-end",rtl:"right-end"},"end-start":{ltr:"right-start",rtl:"left-start"},"end-end":{ltr:"right-end",rtl:"left-end"},start:{ltr:"left",rtl:"right"},end:{ltr:"right",rtl:"left"}},eO={"auto-start":"auto-end","auto-end":"auto-start","top-start":"top-end","top-end":"top-start","bottom-start":"bottom-end","bottom-end":"bottom-start"};function eE(e={}){let{enabled:t=!0,modifiers:n,placement:r="bottom",strategy:o="absolute",arrowPadding:i=8,eventListeners:a=!0,offset:s,gutter:u=8,flip:c=!0,boundary:f="clippingParents",preventOverflow:d=!0,matchWidth:p,direction:m="ltr"}=e,h=(0,ef.useRef)(null),v=(0,ef.useRef)(null),b=(0,ef.useRef)(null),g=function(e,t="ltr"){let n=ex[e]?.[t]||e;return"ltr"===t?n:eO[e]??n}(r,m),y=(0,ef.useRef)(()=>{}),w=(0,ef.useCallback)(()=>{t&&h.current&&v.current&&(y.current?.(),b.current=ec(h.current,v.current,{placement:g,modifiers:[ey,ev,em,{...ep,enabled:!!p},{name:"eventListeners",...(0,ed.$B)(a)},{name:"arrow",options:{padding:i}},{name:"offset",options:{offset:s??[0,u]}},{name:"flip",enabled:!!c,options:{padding:8}},{name:"preventOverflow",enabled:!!d,options:{boundary:f}},...n??[]],strategy:o}),b.current.forceUpdate(),y.current=b.current.destroy)},[g,t,n,p,a,i,s,u,c,d,f,o]);(0,ef.useEffect)(()=>()=>{h.current||v.current||(b.current?.destroy(),b.current=null)},[]);let x=(0,ef.useCallback)(e=>{h.current=e,w()},[w]),O=(0,ef.useCallback)((e={},t=null)=>({...e,ref:(0,l.lq)(x,t)}),[x]),E=(0,ef.useCallback)(e=>{v.current=e,w()},[w]),k=(0,ef.useCallback)((e={},t=null)=>({...e,ref:(0,l.lq)(E,t),style:{...e.style,position:o,minWidth:p?void 0:"max-content",inset:"0 auto auto 0"}}),[o,E,p]),C=(0,ef.useCallback)((e={},t=null)=>{let{size:n,shadowColor:r,bg:o,style:i,...a}=e;return{...a,ref:t,"data-popper-arrow":"",style:function(e){let{size:t,shadowColor:n,bg:r,style:o}=e,i={...o,position:"absolute"};return t&&(i["--popper-arrow-size"]=t),n&&(i["--popper-arrow-shadow-color"]=n),r&&(i["--popper-arrow-bg"]=r),i}(e)}},[]),j=(0,ef.useCallback)((e={},t=null)=>({...e,ref:t,"data-popper-arrow-inner":""}),[]);return{update(){b.current?.update()},forceUpdate(){b.current?.forceUpdate()},transformOrigin:ed.Dq.transformOrigin.varRef,referenceRef:x,popperRef:E,getPopperProps:k,getArrowProps:C,getArrowInnerProps:j,getReferenceProps:O}}},38898:function(e,t,n){n.d(t,{$B:function(){return u},Dq:function(){return o},Ke:function(){return i},mv:function(){return s}});let r=(e,t)=>({var:e,varRef:t?`var(${e}, ${t})`:`var(${e})`}),o={arrowShadowColor:r("--popper-arrow-shadow-color"),arrowSize:r("--popper-arrow-size","8px"),arrowSizeHalf:r("--popper-arrow-size-half"),arrowBg:r("--popper-arrow-bg"),transformOrigin:r("--popper-transform-origin"),arrowOffset:r("--popper-arrow-offset")};function i(e){return e.includes("top")?"1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("bottom")?"-1px -1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("right")?"-1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("left")?"1px -1px 0px 0 var(--popper-arrow-shadow-color)":void 0}let a={top:"bottom center","top-start":"bottom left","top-end":"bottom right",bottom:"top center","bottom-start":"top left","bottom-end":"top right",left:"right center","left-start":"right top","left-end":"right bottom",right:"left center","right-start":"left top","right-end":"left bottom"},s=e=>a[e],l={scroll:!0,resize:!0};function u(e){return"object"==typeof e?{enabled:!0,options:{...l,...e}}:{enabled:e,options:l}}},90243:function(e,t,n){n.d(t,{P:function(){return m}});var r=n(85893),o=n(65544),i=n(35831),a=n(20397),s=n(67294),l=n(34926),u=n(49381),c=n(64993);let f=(0,u.G)(function(e,t){let{children:n,placeholder:o,className:i,...a}=e;return(0,r.jsxs)(c.m.select,{...a,ref:t,className:(0,l.cx)("chakra-select",i),children:[o&&(0,r.jsx)("option",{value:"",children:o}),n]})});f.displayName="SelectField";var d=n(16013),p=n(73035);let m=(0,u.G)((e,t)=>{let n=(0,p.j)("Select",e),{rootProps:s,placeholder:l,icon:u,color:m,height:h,h:v,minH:g,minHeight:y,iconColor:w,iconSize:x,...O}=(0,o.L)(e),[E,k]=function(e,t){let n={},r={};for(let[o,i]of Object.entries(e))t.includes(o)?n[o]=i:r[o]=i;return[n,r]}(O,i.oE),C=(0,d.Y)(k),j={paddingEnd:"2rem",...n.field,_focus:{zIndex:"unset",...n.field?._focus}};return(0,r.jsxs)(c.m.div,{className:"chakra-select__wrapper",__css:{width:"100%",height:"fit-content",position:"relative",color:m},...E,...s,children:[(0,r.jsx)(f,{ref:t,height:v??h,minH:g??y,placeholder:l,...C,__css:j,children:e.children}),(0,r.jsx)(b,{"data-disabled":(0,a.P)(C.disabled),...(w||m)&&{color:w||m},__css:n.icon,...x&&{fontSize:x},children:u})]})});m.displayName="Select";let h=e=>(0,r.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),v=(0,c.m)("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),b=e=>{let{children:t=(0,r.jsx)(h,{}),...n}=e,o=(0,s.cloneElement)(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return(0,r.jsx)(v,{...n,className:"chakra-select__icon-wrapper",children:(0,s.isValidElement)(t)?o:null})};b.displayName="SelectIcon"},73636:function(e,t,n){n.d(t,{U:function(){return a}});var r=n(85893),o=n(3712),i=n(49381);let a=(0,i.G)((e,t)=>(0,r.jsx)(o.K,{align:"center",...e,direction:"row",ref:t}));a.displayName="HStack"},6459:function(e,t,n){n.d(t,{p:function(){return u}});var r=n(67294),o=n(2847),i=n(60975),a=n(11065),s=n(88559),l=n(70052);function u(e){let{theme:t}=(0,l.uP)(),n=(0,s.OX)();return(0,r.useMemo)(()=>(function(e,t){let n=n=>({...t,...n,position:function(e,t){let n=e??"bottom";return({"top-start":{ltr:"top-left",rtl:"top-right"},"top-end":{ltr:"top-right",rtl:"top-left"},"bottom-start":{ltr:"bottom-left",rtl:"bottom-right"},"bottom-end":{ltr:"bottom-right",rtl:"bottom-left"}})[n]?.[t]??n}(n?.position??t?.position,e)}),r=e=>{let t=n(e),r=(0,i.C)(t);return a.f.notify(r,t)};return r.update=(e,t)=>{a.f.update(e,n(t))},r.promise=(e,t)=>{let n=r({...t.loading,status:"loading",duration:null});e.then(e=>r.update(n,{status:"success",duration:5e3,...(0,o.P)(t.success,e)})).catch(e=>r.update(n,{status:"error",duration:5e3,...(0,o.P)(t.error,e)}))},r.closeAll=a.f.closeAll,r.close=a.f.close,r.isActive=a.f.isActive,r})(t.direction,{...n,...e}),[e,t.direction,n])}},61771:function(e,t,n){n.d(t,{u:function(){return D}});var r=n(85893),o=n(65544),i=n(8297),a=n(70562),s=n(97340),l=n(51526),u=n(67294);let c={exit:{scale:.85,opacity:0,transition:{opacity:{duration:.15,easings:"easeInOut"},scale:{duration:.2,easings:"easeInOut"}}},enter:{scale:1,opacity:1,transition:{opacity:{easings:"easeOut",duration:.2},scale:{duration:.2,ease:[.175,.885,.4,1.1]}}}};var f=n(83658),d=n(49882),p=n(29062),m=n(21326),h=n(68928),v=n(66822),b=n(38898);let g=e=>e.current?.ownerDocument||document,y=e=>e.current?.ownerDocument?.defaultView||window,w="chakra-ui:close-tooltip";var x=n(37984),O=n(93977),E=n(64993),k=n(49381),C=n(73035);let j=(0,E.m)(s.E.div),D=(0,k.G)((e,t)=>{let n;let s=(0,C.m)("Tooltip",e),k=(0,o.L)(e),D=(0,x.F)(),{children:N,label:_,shouldWrapChildren:P,"aria-label":I,hasArrow:S,bg:A,portalProps:M,background:L,backgroundColor:T,bgColor:R,motionProps:q,animatePresenceProps:W,...z}=k,B=L??T??A??R;if(B){s.bg=B;let e=D.__cssMap?.[`colors.${B}`]?.varRef??B;s[b.Dq.arrowBg.var]=e}let H=function(e={}){let{openDelay:t=0,closeDelay:n=0,closeOnClick:r=!0,closeOnMouseDown:o,closeOnScroll:i,closeOnPointerDown:a=o,closeOnEsc:s=!0,onOpen:l,onClose:c,placement:x,id:O,isOpen:E,defaultIsOpen:k,arrowSize:C=10,arrowShadowColor:j,arrowPadding:D,modifiers:N,isDisabled:_,gutter:P,offset:I,direction:S,...A}=e,{isOpen:M,onOpen:L,onClose:T}=(0,f.q)({isOpen:E,defaultIsOpen:k,onOpen:l,onClose:c}),{referenceRef:R,getPopperProps:q,getArrowInnerProps:W,getArrowProps:z}=(0,v.D)({enabled:M,placement:x,arrowPadding:D,modifiers:N,gutter:P,offset:I,direction:S}),B=(0,u.useId)(),H=`tooltip-${O??B}`,V=(0,u.useRef)(null),U=(0,u.useRef)(void 0),F=(0,u.useCallback)(()=>{U.current&&(clearTimeout(U.current),U.current=void 0)},[]),G=(0,u.useRef)(void 0),$=(0,u.useCallback)(()=>{G.current&&(clearTimeout(G.current),G.current=void 0)},[]),K=(0,u.useCallback)(()=>{$(),T()},[T,$]),X=((0,u.useEffect)(()=>{let e=g(V);return e.addEventListener(w,K),()=>e.removeEventListener(w,K)},[K,V]),()=>{let e=g(V),t=y(V);e.dispatchEvent(new t.CustomEvent(w))}),Y=(0,u.useCallback)(()=>{if(!_&&!U.current){M&&X();let e=y(V);U.current=e.setTimeout(L,t)}},[X,_,M,L,t]),Z=(0,u.useCallback)(()=>{F();let e=y(V);G.current=e.setTimeout(K,n)},[n,K,F]),Q=(0,u.useCallback)(()=>{M&&r&&Z()},[r,Z,M]),J=(0,u.useCallback)(()=>{M&&a&&Z()},[a,Z,M]),ee=(0,u.useCallback)(e=>{M&&"Escape"===e.key&&Z()},[M,Z]);(0,d.O)(()=>g(V),"keydown",s?ee:void 0),(0,d.O)(()=>{if(!i)return null;let e=V.current;if(!e)return null;let t=function e(t){return["html","body","#document"].includes(t.localName)?t.ownerDocument.body:(0,m.Re)(t)&&function(e){let t=e.ownerDocument.defaultView||window,{overflow:n,overflowX:r,overflowY:o}=t.getComputedStyle(e);return/auto|scroll|overlay|hidden/.test(n+o+r)}(t)?t:e("html"===t.localName?t:t.assignedSlot||t.parentElement||t.ownerDocument.documentElement)}(e);return"body"===t.localName?y(V):t},"scroll",()=>{M&&i&&K()},{passive:!0,capture:!0}),(0,u.useEffect)(()=>{_&&(F(),M&&T())},[_,M,T,F]),(0,u.useEffect)(()=>()=>{F(),$()},[F,$]),(0,d.O)(()=>V.current,"pointerleave",Z);let et=(0,u.useCallback)((e={},t=null)=>{let n={...e,ref:(0,p.lq)(V,t,R),onPointerEnter:(0,h.v)(e.onPointerEnter,e=>{"touch"!==e.pointerType&&Y()}),onClick:(0,h.v)(e.onClick,Q),onPointerDown:(0,h.v)(e.onPointerDown,J),onFocus:(0,h.v)(e.onFocus,Y),onBlur:(0,h.v)(e.onBlur,Z),"aria-describedby":M?H:void 0};return n},[Y,Z,J,M,H,Q,R]),en=(0,u.useCallback)((e={},t=null)=>q({...e,style:{...e.style,[b.Dq.arrowSize.var]:C?`${C}px`:void 0,[b.Dq.arrowShadowColor.var]:j}},t),[q,C,j]),er=(0,u.useCallback)((e={},t=null)=>{let n={...e.style,position:"relative",transformOrigin:b.Dq.transformOrigin.varRef};return{ref:t,...A,...e,id:H,role:"tooltip",style:n}},[A,H]);return{isOpen:M,show:Y,hide:Z,getTriggerProps:et,getTooltipProps:er,getTooltipPositionerProps:en,getArrowProps:z,getArrowInnerProps:W}}({...z,direction:D.direction}),V=!(0,u.isValidElement)(N)||P;if(V)n=(0,r.jsx)(E.m.span,{display:"inline-block",tabIndex:0,...H.getTriggerProps(),children:N});else{let e=u.Children.only(N);n=(0,u.cloneElement)(e,H.getTriggerProps(e.props,function(e){let t=u.version;return"string"!=typeof t||t.startsWith("18.")?e?.ref:e?.props?.ref}(e)))}let U=!!I,F=H.getTooltipProps({},t),G=U?(0,i.C)(F,["role","id"]):F,$=(0,a.e)(F,["role","id"]);return _?(0,r.jsxs)(r.Fragment,{children:[n,(0,r.jsx)(l.M,{...W,children:H.isOpen&&(0,r.jsx)(O.h,{...M,children:(0,r.jsx)(E.m.div,{...H.getTooltipPositionerProps(),__css:{zIndex:s.zIndex,pointerEvents:"none"},children:(0,r.jsxs)(j,{variants:c,initial:"exit",animate:"enter",exit:"exit",...q,...G,__css:s,children:[_,U&&(0,r.jsx)(E.m.span,{srOnly:!0,...$,children:I}),S&&(0,r.jsx)(E.m.div,{"data-popper-arrow":!0,className:"chakra-tooltip__arrow-wrapper",children:(0,r.jsx)(E.m.div,{"data-popper-arrow-inner":!0,className:"chakra-tooltip__arrow",__css:{bg:s.bg}})})]})})})})]}):(0,r.jsx)(r.Fragment,{children:N})});D.displayName="Tooltip"},82145:function(e,t,n){n.d(t,{Lj:function(){return r},Sh:function(){return o},p$:function(){return i}});let r={ease:[.25,.1,.25,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1],easeInOut:[.4,0,.2,1]},o={enter:{duration:.2,ease:r.easeOut},exit:{duration:.1,ease:r.easeIn}},i={enter:(e,t)=>({...e,delay:"number"==typeof t?t:t?.enter}),exit:(e,t)=>({...e,delay:"number"==typeof t?t:t?.exit})}},1071:function(e,t,n){n.d(t,{k:function(){return r}});function r(e){let{wasSelected:t,enabled:n,isSelected:r,mode:o="unmount"}=e;return!n||!!r||"keepMounted"===o&&!!t}},59255:function(e,t,n){n.d(t,{dh:function(){return a},kR:function(){return o},vY:function(){return s}});var r=n(21326);function o(e){return i(e)?.defaultView??window}function i(e){return(0,r.Re)(e)?e.ownerDocument:document}function a(e){return e.view??window}function s(e){return i(e).activeElement}},90191:function(e,t,n){n.d(t,{EB:function(){return a},Wq:function(){return s}});var r=n(21326);let o=e=>e.hasAttribute("tabindex"),i=e=>o(e)&&-1===e.tabIndex;function a(e){if(!(0,r.Re)(e)||(0,r.oI)(e)||(0,r.nV)(e))return!1;let{localName:t}=e;if(["input","select","textarea","button"].indexOf(t)>=0)return!0;let n={a:()=>e.hasAttribute("href"),audio:()=>e.hasAttribute("controls"),video:()=>e.hasAttribute("controls")};return t in n?n[t]():!!(0,r.iU)(e)||o(e)}function s(e){return!!e&&(0,r.Re)(e)&&a(e)&&!i(e)}},25054:function(e,t,n){n.d(t,{w_:function(){return c}});var r=n(67294),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(o),a=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function c(e){return t=>r.createElement(f,s({attr:u({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,u({key:n},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var n,{attr:o,size:i,title:l}=e,c=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,a),f=i||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,c,{className:n,style:u(u({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),e.children)};return void 0!==i?r.createElement(i.Consumer,null,e=>t(e)):t(o)}}}]);