(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4008],{64977:function(n,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/art",function(){return r(66785)}])},66785:function(n,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return c},default:function(){return u}});var a=r(85893),s=r(9008),e=r.n(s),i=r(47896);r(11752);var o=r(47426);r(63214);var c=!0;function u(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(e(),{children:[(0,a.jsx)("title",{children:"FunBlocks AI Art Insight: AI-Powered Art Appreciation & Analysis | FunBlocks AI Tools"}),(0,a.jsx)("meta",{name:"description",content:"Unlock deeper understanding of art with FunBlocks AI Art Insight. Get instant, AI-powered mind maps analyzing artworks, photos, & cultural significance. Perfect for educators, enthusiasts, & researchers."}),(0,a.jsx)("meta",{name:"keywords",content:"AI art analysis, art appreciation, art history, AI art tool, mind map, art education, art research, professional art analysis, art critique, cultural significance, composition analysis, color analysis, technique analysis, photography feedback"})]}),(0,a.jsx)(i.Z,{app:o.IF.art})]})}},63214:function(n,t,r){"use strict";r(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=64977)}),_N_E=n.O()}]);