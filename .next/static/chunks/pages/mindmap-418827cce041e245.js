(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1424],{33688:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/mindmap",function(){return t(68585)}])},68585:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSP:function(){return d},default:function(){return g}});var a=t(85893),i=t(9008),o=t.n(i),r=t(47896),s=t(11752),p=t.n(s),m=t(47426),c=t(31837),l=t(83454),d=!0;function g(n){let{initial_showcases:e}=n,{basePath:t}=p()().publicRuntimeConfig,i=l.env.NEXT_PUBLIC_SITE_URL||"https://www.funblocks.net",s="".concat(i).concat(t,"/mindmap"),{t:d}=(0,c.$G)("mindmap"),g={"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:d("mindmap_faq_1_q"),acceptedAnswer:{"@type":"Answer",text:d("mindmap_faq_1_a")}},{"@type":"Question",name:d("mindmap_faq_2_q"),acceptedAnswer:{"@type":"Answer",text:d("mindmap_faq_2_a")}},{"@type":"Question",name:d("mindmap_faq_3_q"),acceptedAnswer:{"@type":"Answer",text:d("mindmap_faq_3_a")}},{"@type":"Question",name:d("mindmap_faq_4_q"),acceptedAnswer:{"@type":"Answer",text:d("mindmap_faq_4_a")}},{"@type":"Question",name:d("mindmap_faq_5_q"),acceptedAnswer:{"@type":"Answer",text:d("mindmap_faq_5_a")}}]};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(o(),{children:[(0,a.jsx)("title",{children:"FunBlocks AI Mindmap: AI-Powered Mind Mapping Tool | Create Visual Mind Maps in One Click"}),(0,a.jsx)("meta",{name:"description",content:"FunBlocks AI Mindmap transforms complex information into clear, visual mind maps with one click. Perfect for students, professionals, researchers, and educators. Boost creativity, enhance learning, and organize knowledge effortlessly with AI-powered mind mapping."}),(0,a.jsx)("meta",{name:"keywords",content:"AI mind map, mind mapping tool, AI-powered mind map, visual thinking, knowledge management, brainstorming, project planning, study research, ChatGPT mind map alternative, AI assistant, FunBlocks AI, one-click mind map, mind map generator, visual learning, concept mapping, knowledge organization, educational tool, productivity tool, information visualization, hierarchical organization, cognitive mapping, learning enhancement, creative thinking, idea organization, visual notes"}),(0,a.jsx)("link",{rel:"canonical",href:s}),(0,a.jsx)("meta",{property:"og:title",content:"FunBlocks AI Mindmap: Create Visual Mind Maps in One Click | AI-Powered Mind Mapping Tool"}),(0,a.jsx)("meta",{property:"og:description",content:"Transform complex information into clear, visual mind maps with AI assistance. Perfect for learning, brainstorming, project planning, and knowledge management. Try our free AI mind mapping tool today!"}),(0,a.jsx)("meta",{property:"og:type",content:"website"}),(0,a.jsx)("meta",{property:"og:url",content:s}),(0,a.jsx)("meta",{property:"og:image",content:"".concat(i,"/img/portfolio/thumbnails/aitools_mindmap_book_generated.png")}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,a.jsx)("meta",{name:"twitter:title",content:"FunBlocks AI Mindmap: AI-Powered Mind Mapping Tool"}),(0,a.jsx)("meta",{name:"twitter:description",content:"Create comprehensive mind maps from any content with one click using AI. Transform books, videos, websites, and more into visual knowledge maps."}),(0,a.jsx)("meta",{name:"twitter:image",content:"".concat(i,"/img/portfolio/thumbnails/aitools_mindmap_book_generated.png")}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"FunBlocks AI Mindmap",applicationCategory:"BusinessApplication",operatingSystem:"Web",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},description:"AI-powered mind mapping tool that transforms complex information into clear, visual mind maps with one click.",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"625"}})}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(g)}})]}),(0,a.jsx)(r.Z,{app:m.IF.mindmap,showcases:e})]})}}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=33688)}),_N_E=n.O()}]);