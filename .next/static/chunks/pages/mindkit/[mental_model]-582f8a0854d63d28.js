(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2667],{38447:function(n,e,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/mindkit/[mental_model]",function(){return i(40129)}])},40129:function(n,e,i){"use strict";i.r(e),i.d(e,{__N_SSP:function(){return d},default:function(){return c}});var t=i(85893),o=i(9008),s=i.n(o),a=i(47896);i(11752);var r=i(47426),l=i(11163),d=!0;function c(){let n=(0,l.useRouter)(),{mental_model:e}=n.query;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(s(),{children:[(0,t.jsx)("title",{children:"FunBlocks AI Brainstorming with Classic Mental Models | FunBlocks AI Tools"}),(0,t.jsx)("meta",{name:"description",content:"FunBlocks AI provides AI-driven tools for whiteboarding, mind mapping, presentations, and infographics. Leverage mental models and AI for enhanced decision-making, problem-solving, and knowledge integration."}),(0,t.jsx)("meta",{name:"keywords",content:"AI tools, mental models, mind mapping, whiteboarding, presentations, infographics, problem solving, decision making, knowledge management, strategic planning, risk assessment, market analysis, AI-powered productivity, visual communication"})]}),(0,t.jsx)(a.Z,{app:r.IF.mindkit,mental_model:null==e?void 0:e.replace("-"," ")})]})}}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=38447)}),_N_E=n.O()}]);