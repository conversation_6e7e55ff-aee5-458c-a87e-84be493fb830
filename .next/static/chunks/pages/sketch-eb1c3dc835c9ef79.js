(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1434],{98951:function(t,n,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/sketch",function(){return e(42579)}])},42579:function(t,n,e){"use strict";e.r(n),e.d(n,{__N_SSG:function(){return c},default:function(){return u}});var r=e(85893),i=e(9008),s=e.n(i),a=e(47896);e(11752);var o=e(47426);e(63214);var c=!0;function u(){return(0,r.jsxs)(r.Frag<PERSON>,{children:[(0,r.jsxs)(s(),{children:[(0,r.jsx)("title",{children:"AI Sketch - Transform Your Drawings into Art | FunBlocks AI Tools"}),(0,r.jsx)("meta",{name:"description",content:"AI Sketch lets you draw simple sketches on a whiteboard and transform them into stunning artwork with AI. Choose from multiple art styles including anime, realistic, cartoon, watercolor, and more. Perfect for artists, designers, and creative enthusiasts!"}),(0,r.jsx)("meta",{name:"keywords",content:"AI sketch, AI drawing, sketch to art, AI artwork generator, digital drawing, AI art styles, creative AI, drawing enhancement, sketch transformation, AI artist, whiteboard drawing, touch drawing, mobile drawing"})]}),(0,r.jsx)(a.Z,{app:o.IF.sketch})]})}},63214:function(t,n,e){"use strict";e(47426)}},function(t){t.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return t(t.s=98951)}),_N_E=t.O()}]);