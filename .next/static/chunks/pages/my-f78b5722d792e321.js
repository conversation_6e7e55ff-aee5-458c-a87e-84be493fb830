(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9236],{35015:function(n,e,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/my",function(){return s(86378)}])},28534:function(n,e,s){"use strict";var t=s(85893),i=s(91816),l=s(88329),o=s(57879),c=s(90608),r=s(12095),a=s(12519),h=s(34302),u=s(63162),d=s(38491),x=s(38389),f=s(31837),p=s(61951),j=s(49515),g=s(43598);let k=n=>{let{leftLink:e}=n,{t:s}=(0,f.$G)("common"),{user:k,logout:w,openLoginModal:b}=(0,p.a)(),_=(0,i.S)({base:!0,md:!1});return(0,t.jsxs)(l.k,{as:"header",top:0,width:"100%",bg:"white",boxShadow:"md",p:4,alignItems:"center",justifyContent:"space-between",children:[e,!e&&(0,t.jsx)(o.r,{href:"/",fontSize:"2xl",fontWeight:"bold",color:"blue.500",isExternal:!0,children:"FunBlocks AI"}),_?(0,t.jsxs)(c.v,{children:[(0,t.jsx)(r.j,{as:a.h,icon:(0,t.jsx)(x.U,{}),variant:"ghost"}),(0,t.jsxs)(h.q,{children:[(0,t.jsx)(u.s,{as:o.r,href:"https://app.funblocks.net/#/aiplans",isExternal:!0,children:s("pricing")}),!k&&(0,t.jsx)(u.s,{onClick:b,children:s("login")}),k&&(0,t.jsx)(u.s,{onClick:w,children:s("logout")})]})]}):(0,t.jsxs)(l.k,{alignItems:"center",gap:4,children:[(0,t.jsx)(o.r,{href:"https://app.funblocks.net/#/aiplans",isExternal:!0,children:s("pricing")}),(0,t.jsx)(j.Z,{}),k?(0,t.jsx)(d.z,{onClick:()=>{console.log("logout clicked........"),w()},children:s("logout")}):(0,t.jsx)(g.k,{})]})]})};e.Z=k},86378:function(n,e,s){"use strict";s.r(e),s.d(e,{__N_SSG:function(){return p},default:function(){return j}});var t=s(85893);s(9008);var i=s(8186),l=s(57879),o=s(11752),c=s.n(o),r=s(47426),a=s(28534),h=s(86734),u=s(31082),d=s(71508),x=s(71692);let{basePath:f}=c()().publicRuntimeConfig;var p=!0;function j(){return(0,t.jsx)(u.$,{children:(0,t.jsxs)(i.g,{width:"100%",position:"relative",overflowY:"hidden",gap:"4px",children:[(0,t.jsx)(a.Z,{leftLink:(0,t.jsx)(l.r,{href:f,fontSize:"2xl",fontWeight:"bold",color:"blue.500",isExternal:!0,children:"FunBlocks AI Tools"})}),(0,t.jsx)(x.Z,{}),(0,t.jsx)("div",{style:{display:"none"},children:(0,t.jsx)(d.Q,{value:"https://www.funblocks.net".concat(f),size:100,id:"qrcode-canvas"})}),(0,t.jsx)(i.g,{overflowY:"auto",height:"calc(100vh - 76px)",alignItems:"center",width:"100%",py:10,children:(0,t.jsx)(h.Z,{collection:"my",app:Object.values(r.IF)})})]})})}}},function(n){n.O(0,[4838,3365,1265,9769,805,8417,5460,1582,7919,2613,6734,9774,2888,179],function(){return n(n.s=35015)}),_N_E=n.O()}]);