(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5524],{6124:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/solo",function(){return t(95732)}])},95732:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSG:function(){return u},default:function(){return c}});var r=t(85893),o=t(9008),s=t.n(o),i=t(47896);t(11752);var a=t(47426);t(63214);var u=!0;function c(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(s(),{children:[(0,r.jsx)("title",{children:"AI SOLOBrain - Educational Course Designer Based on SOLO Taxonomy | FunBlocks AI"}),(0,r.jsx)("meta",{name:"description",content:"AI SOLOBrain helps educators and learners create comprehensive educational plans using SOLO Taxonomy. Generate structured mind maps that break down complex subjects into progressive learning journeys."}),(0,r.jsx)("meta",{name:"keywords",content:"SOLO Taxonomy, educational tool, learning objectives, cognitive levels, AI education, AI tools, AI mind map, AI Brainstorming, teaching strategies, curriculum planning, instructional design, Structure of Observed Learning Outcomes"})]}),(0,r.jsx)(i.Z,{app:a.IF.solo})]})}},63214:function(n,e,t){"use strict";t(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=6124)}),_N_E=n.O()}]);