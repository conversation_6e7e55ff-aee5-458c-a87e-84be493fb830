(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2583],{78551:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/movie",function(){return e(23686)}])},23686:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSP:function(){return c},default:function(){return u}});var i=e(85893),s=e(9008),a=e.n(s),r=e(47896);e(11752);var o=e(47426),c=!0;function u(n){let{initial_showcases:t}=n;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a(),{children:[(0,i.jsx)("title",{children:"AI CineMap: AI-Powered Film Analysis & Mind Maps | FunBlocks AI Tools"}),(0,i.jsx)("meta",{name:"description",content:"Unlock deeper insights into your favorite films with AI CineMap. Generate interactive mind maps analyzing narrative structure, cinematography, themes, and cultural context. Perfect for film enthusiasts, students, and content creators."}),(0,i.jsx)("meta",{name:"keywords",content:"AI film analysis, film mind map, cinema analysis, movie analysis, film studies, film education, narrative structure, cinematography, thematic analysis, cultural context, AI tools, film enthusiasts, content creators, interactive visualization, FunBlocks AI"})]}),(0,i.jsx)(r.Z,{app:o.IF.movie,showcases:t})]})}}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=78551)}),_N_E=n.O()}]);