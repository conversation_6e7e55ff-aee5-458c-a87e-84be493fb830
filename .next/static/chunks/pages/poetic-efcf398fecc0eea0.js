(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3516],{36966:function(t,e,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/poetic",function(){return n(22242)}])},22242:function(t,e,n){"use strict";n.r(e),n.d(e,{__N_SSG:function(){return a},default:function(){return u}});var r=n(85893),i=n(9008),o=n.n(i),s=n(47896);n(11752);var c=n(47426);n(63214);var a=!0;function u(){return(0,r.jsxs)(r.<PERSON>ag<PERSON>,{children:[(0,r.jsxs)(o(),{children:[(0,r.jsx)("title",{children:"AI-Powered Cultural Poetry Generator | FunBlocks AI Tools"}),(0,r.jsx)("meta",{name:"description",content:"Transform images into culturally rich poetic commentaries with FunBlocks AI. Generate witty and insightful poems in multiple languages, reflecting diverse literary traditions. Perfect for content creators and culture enthusiasts."}),(0,r.jsx)("meta",{name:"keywords",content:"AI poetry generator, cultural poetry, multilingual poetry, image to poetry, AI poetic lens, cultural insights, literary traditions, content creation, social media captions, cross-cultural communication, Chinese poetry, Japanese poetry, French poetry, English poetry"})]}),(0,r.jsx)(s.Z,{app:c.IF.poetic})]})}},63214:function(t,e,n){"use strict";n(47426)}},function(t){t.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return t(t.s=36966)}),_N_E=t.O()}]);