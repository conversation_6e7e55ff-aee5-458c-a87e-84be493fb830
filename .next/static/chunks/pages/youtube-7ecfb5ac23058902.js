(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[397],{10703:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/youtube",function(){return t(86520)}])},86520:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSG:function(){return a},default:function(){return c}});var i=t(85893),o=t(9008),s=t.n(o),u=t(47896);t(11752);var r=t(47426);t(63214);var a=!0;function c(){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(s(),{children:[(0,i.jsx)("title",{children:" AI Youtube Video Summarizer | FunBlocks AI Tools"}),(0,i.jsx)("meta",{name:"description",content:"Unlock knowledge from YouTube videos instantly with FunBlocks AI. Transform lengthy videos into interactive summaries, knowledge maps, and AI-driven insights for proactive learning and time-saving exploration."}),(0,i.jsx)("meta",{name:"keywords",content:"YouTube summary, AI video analysis, knowledge map, interactive learning, AI insights, video summarization tool, YouTube education, active learning, content extraction, key points, main ideas, FunBlocks AI"})]}),(0,i.jsx)(u.Z,{app:r.IF.youtube})]})}},63214:function(n,e,t){"use strict";t(47426)}},function(n){n.O(0,[4838,3365,1265,9769,805,8127,4817,594,8417,5460,1582,2658,1664,7919,901,2613,5408,6734,5794,9774,2888,179],function(){return n(n.s=10703)}),_N_E=n.O()}]);