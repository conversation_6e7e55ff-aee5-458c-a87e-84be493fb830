"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[906],{28534:function(e,i,t){var a=t(85893),o=t(91816),n=t(88329),r=t(57879),s=t(90608),l=t(12095),d=t(12519),c=t(34302),g=t(63162),m=t(38491),p=t(38389),h=t(31837),b=t(61951),x=t(49515),u=t(43598);let _=e=>{let{leftLink:i}=e,{t}=(0,h.$G)("common"),{user:_,logout:f,openLoginModal:w}=(0,b.a)(),j=(0,o.S)({base:!0,md:!1});return(0,a.jsxs)(n.k,{as:"header",top:0,width:"100%",bg:"white",boxShadow:"md",p:4,alignItems:"center",justifyContent:"space-between",children:[i,!i&&(0,a.jsx)(r.r,{href:"/",fontSize:"2xl",fontWeight:"bold",color:"blue.500",isExternal:!0,children:"FunBlocks AI"}),j?(0,a.jsxs)(s.v,{children:[(0,a.jsx)(l.j,{as:d.h,icon:(0,a.jsx)(p.U,{}),variant:"ghost"}),(0,a.jsxs)(c.q,{children:[(0,a.jsx)(g.s,{as:r.r,href:"https://app.funblocks.net/#/aiplans",isExternal:!0,children:t("pricing")}),!_&&(0,a.jsx)(g.s,{onClick:w,children:t("login")}),_&&(0,a.jsx)(g.s,{onClick:f,children:t("logout")})]})]}):(0,a.jsxs)(n.k,{alignItems:"center",gap:4,children:[(0,a.jsx)(r.r,{href:"https://app.funblocks.net/#/aiplans",isExternal:!0,children:t("pricing")}),(0,a.jsx)(x.Z,{}),_?(0,a.jsx)(m.z,{onClick:()=>{console.log("logout clicked........"),f()},children:t("logout")}):(0,a.jsx)(u.k,{})]})]})};i.Z=_},60906:function(e,i,t){t.d(i,{C:function(){return U}});var a=t(85893),o=t(68029),n=t(91816),r=t(57879),s=t(88329),l=t(74032),d=t(8186),c=t(12553),g=t(50232),m=t(90092),p=t(38491),h=t(31046),b=t(43321),x=t(15715),u=t(73636),_=t(82206),f=t(61771),w=t(37945),j=t(90038),y=t(86561),k=t(21502),v=t(75133),I=t(19109),S=t(449),C=t(67294),A=t(52091),z=t(39715),M=t(31837),q=t(28534),T=t(97340),L=t(11752),R=t.n(L),E=t(51306),B=t(30080),W=t(19030),H=t(80966),P=t(50980),X=t(17157),G=t(43706);let U={Mindmap:[{category:"Mindmap",icon:A.TdV,title:"AI Mindmap",description:"ai_mindmap_desc",link:"/mindmap",tag:"Mindmap Generator",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.ArU,title:"AI MindLadder",description:"ai_layeredexplanation_desc",link:"/layered-explanation",tag:"AI Education",gradient:"linear(to-r, blue.400, purple.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.FcZ,title:"AI MarzanoBrain",description:"ai_marzano_desc",link:"/marzano",tag:"AI Education",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.FcZ,title:"AI BloomBrain",description:"ai_bloom_desc",link:"/bloom",tag:"AI Education",gradient:"linear(to-r, red.400, yellow.500)",bgColor:"rgba(255, 204, 204, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.FcZ,title:"AI SOLOBrain",description:"ai_solo_desc",link:"/solo",tag:"AI Education",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.FcZ,title:"AI DOKBrain",description:"ai_dok_desc",link:"/dok",tag:"AI Education",gradient:"linear(to-r, cyan.400, blue.400)",bgColor:"rgba(149, 235, 245, 0.2)",isLaunched:!0},{category:"Mindmap",icon:B.VCi,title:"AI DOK Assessment",description:"ai_dok_assessment_desc",link:"/dok-assessment",tag:"AI Education",gradient:"linear(to-r, purple.400, blue.500)",bgColor:"rgba(196, 181, 253, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.$yZ,title:"AI Feynman",description:"ai_feynman_desc",link:"/feynman",tag:"AI Education",gradient:"linear(to-r, orange.400, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.Qkp,title:"AI Brainstorming",description:"ai_brainstorming_desc",link:"/brainstorming",tag:"Creative Thinking",gradient:"linear(to-r, orange.400, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.nUJ,title:"AI MindKit",description:"ai_mindkit_desc",link:"/mindkit",tag:"Creative Thinking",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.V2E,title:"AI Youtube Summarizer",description:"ai_youtube_desc",link:"/youtube",tag:"Mindmap Generator",gradient:"linear(to-r, red.400, yellow.500)",bgColor:"rgba(255, 204, 204, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.ULU,title:"AI Critical Analysis",description:"ai_criticalthinking_desc",link:"/critical-thinking",tag:"Critical Thinking",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.MXt,title:"AI Question Craft",description:"ai_refinequestion_desc",link:"/refine-question",tag:"Critical Thinking",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.ULU,title:"AI LogicLens",description:"ai_bias_desc",link:"/bias",tag:"Critical Thinking",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:B.AB4,title:"AI Reflection",description:"ai_reflection_desc",link:"/reflection",tag:"Critical Thinking",gradient:"linear(to-r, orange.500, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.pno,title:"AI Decision Analyzer",description:"ai_decision_desc",link:"/decision",tag:"Critical Thinking",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.ULU,title:"AI OKR Assistant",description:"ai_okr_desc",link:"/okr",tag:"Business Insights",gradient:"linear(to-r, orange.500, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.$yZ,title:"AI Startup Mentor",description:"ai_startupmentor_desc",link:"/startupmentor",tag:"Business Insights",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.MZI,title:"AI Business Model Analyzer",description:"ai_businessmodel_desc",link:"/businessmodel",tag:"Business Insights",gradient:"linear(to-r, cyan.400, blue.400)",bgColor:"rgba(149, 235, 245, 0.2)",isLaunched:!0},{category:"Mindmap",icon:A.qGN,title:"AI Task Planner",description:"ai_planner_desc",link:"/planner",tag:"Business Insights",gradient:"linear(to-r, yellow.400, orange.500)",bgColor:"rgba(255, 236, 179, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.$0H,title:"AI Counselor",description:"ai_counselor_desc",link:"/counselor",tag:"Psychological Insights",gradient:"linear(to-r, cyan.400, blue.400)",bgColor:"rgba(169, 235, 245, 0.2)",isLaunched:!0},{category:"Mindmap",icon:A.TLr,title:"AI DreamLens",description:"ai_dreamlens_desc",link:"/dreamlens",tag:"Psychological Insights",gradient:"linear(to-r, orange.500, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.QJe,title:"AI Horoscope",description:"ai_horoscope_desc",link:"/horoscope",tag:"Psychological Insights",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.H3h,title:"AI Art Insight",description:"ai_art_desc",link:"/art",tag:"Image Insights",gradient:"linear(to-r, orange.500, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.Dmm,title:"AI Photo Coach",description:"ai_photo_desc",link:"/photo",tag:"Image Insights",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.DdF,title:"AI Poetic Lens",description:"ai_poetic_desc",link:"/poetic",tag:"Image Insights",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0},{category:"Mindmap",icon:A.Mp$,title:"AI Reading Map",description:"ai_reading_desc",link:"/reading",tag:"Mindmap Generator",gradient:"linear(to-r, cyan.400, blue.400)",bgColor:"rgba(149, 235, 245, 0.2)",isLaunched:!0},{category:"Mindmap",icon:A.rHK,title:"AI CineMap",description:"ai_movie_desc",link:"/movie",tag:"Mindmap Generator",gradient:"linear(to-r, red.400, yellow.500)",bgColor:"rgba(255, 204, 204, 0.3)",isLaunched:!0}],Infographics:[{category:"Infographics",icon:A.NMh,title:"AI Graphics",description:"ai_graphics_desc",link:"/graphics",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Infographics",icon:A.Pl6,title:"AI Infographic Generator",description:"ai_infographic_desc",link:"/infographic",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0},{category:"Infographics",icon:A.nUJ,title:"AI MindSnap",description:"ai_mindsnap_desc",link:"/mindsnap",gradient:"linear(to-r, orange.400, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Infographics",icon:A.UZO,title:"AI InsightCards",description:"ai_insightcards_desc",link:"/insightcards",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(210, 214, 255, 0.3)",isLaunched:!0}],Slides:[{category:"Slides",icon:A.lMi,title:"AI PPT/Slides",description:"ai_slides_desc",link:"/slides",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0},{category:"Slides",icon:A.Qkp,title:"AI SlideGenius",description:"ai_onepageslide_desc",link:"/one-page-slide",gradient:"linear(to-r, orange.400, red.500)",bgColor:"rgba(254, 235, 200, 0.3)",isLaunched:!0},{category:"Slides",icon:A.m9R,title:"AI EduSlides",tag:"AI Education",description:"ai_teachingslides_desc",link:"/teaching-slides",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0}],Images:[{category:"Images",icon:A._TT,title:"AI Sketch",description:"ai_sketch_desc",link:"/sketch",tag:"Creative Drawing",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(237, 242, 247, 0.3)",isLaunched:!0},{category:"Images",icon:B.zmo,title:"AI Image Editor",description:"ai_imageeditor_desc",link:"/image-editor",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(237, 213, 255, 0.3)",isLaunched:!0},{category:"Images",icon:A.m9R,title:"AI Avatar Generator",description:"ai_avatar_desc",link:"/avatar",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Images",icon:B.kk0,title:"AI Watermark Remover",description:"ai_erase_desc",link:"/erase",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(209, 250, 229, 0.3)",isLaunched:!0}],Text:[{category:"Text",icon:A.m9R,title:"AI Prompt Optimizer",description:"ai_promptoptimizer_desc",link:"/prompt-optimizer",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Text",icon:A.m9R,title:"AI Lesson Plans",description:"ai_lessonplans_desc",tag:"AI Education",link:"/lesson-plans",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0},{category:"Text",icon:A.m9R,title:"AI DOK Assessment",description:"ai_dokassessment_desc",tag:"AI Education",link:"/dok-assessment",gradient:"linear(to-r, cyan.400, blue.500)",bgColor:"rgba(179, 245, 255, 0.3)",isLaunched:!0}]},J=(0,T.E)(o.x),F=e=>{let{icon:i,title:t,description:h,link:b,isLaunched:x,tag:u,category:_,isFeatured:f,isNew:w}=e,{color:j,shade:y}=(e=>{switch(e){case"Mindmap Generator":return{color:"blue",shade:"300"};case"AI Education":return{color:"teal",shade:"400"};case"Creative Thinking":return{color:"purple",shade:"500"};case"Critical Thinking":return{color:"cyan",shade:"600"};case"Business Insights":return{color:"green",shade:"600"};case"Psychological Insights":return{color:"pink",shade:"500"};case"Image Insights":return{color:"red",shade:"300"};default:if("Slides"===_)return{color:"blue",shade:"600"};if("Infographics"===_)return{color:"green",shade:"400"};return{color:"blue",shade:"500"}}})(u),{t:k}=(0,M.$G)("common");return(0,n.S)({base:!0,md:!1}),(0,a.jsxs)(J,{as:r.r,href:x&&b,target:"_blank",rel:"noopener noreferrer",whileHover:{y:-5},p:0,borderRadius:"lg",boxShadow:"md",bg:"white",position:"relative",overflow:"hidden",_hover:{textDecoration:"none",transform:"scale(1.02)",boxShadow:"lg"},transition:"all 0.3s",border:"1px solid",borderColor:"gray.100",height:"100%",display:"flex",flexDirection:"column",role:"group","aria-label":t,touchAction:"manipulation",children:[(0,a.jsx)(o.x,{h:"4px",bg:"".concat(j,".").concat(y),w:"100%",opacity:.8}),(0,a.jsx)(s.k,{position:"absolute",top:2,right:2,flexDirection:"column",alignItems:"flex-end",gap:1,zIndex:"1",children:u&&(0,a.jsx)(l.C,{bg:"".concat(j,".50"),color:"".concat(j,".").concat(y),borderRadius:"full",px:2,py:.5,fontSize:{base:"2xs",md:"xs"},fontWeight:"medium",children:u})}),(0,a.jsxs)(o.x,{p:{base:4,md:5},pt:{base:3,md:4},display:"flex",flexDirection:"column",height:"100%",minH:{base:"auto",md:"180px"},flex:"1",children:[(0,a.jsxs)(d.g,{spacing:{base:2,md:4},align:"start",flex:"1",children:[(0,a.jsxs)(s.k,{width:"100%",direction:{base:"row",sm:"row"},align:{base:"center",sm:"center"},gap:{base:3,sm:3},children:[(0,a.jsx)(o.x,{p:{base:2,md:2.5},borderRadius:"md",bg:"".concat(j,".50"),minWidth:{base:"36px",md:"auto"},display:"flex",alignItems:"center",justifyContent:"center",children:(0,a.jsx)(c.J,{as:i,w:{base:5,md:6},h:{base:5,md:6},color:"".concat(j,".").concat(y)})}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:"gray.700",noOfLines:2,lineHeight:"shorter",children:t})]}),h&&(0,a.jsx)(m.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},noOfLines:3,lineHeight:"1.5",mt:{base:1,md:2},children:h})]}),(0,a.jsx)(p.z,{rightIcon:x?(0,a.jsx)(A.Z1Y,{}):void 0,variant:"outline",colorScheme:x?j:"gray",size:{base:"md",md:"md"},disabled:!x,mt:{base:3,md:4},mb:{base:0,md:0},width:"full",height:{base:"40px",md:"40px"},_hover:{transform:"translateX(4px)",bg:"".concat(j,".50")},transition:"all 0.2s",fontSize:{base:"sm",md:"md"},fontWeight:"medium",_groupHover:{bg:"".concat(j,".50"),borderColor:"".concat(j,".400")},children:x?k("try_for_free","Try for Free Now"):k("coming_soon","Coming soon ...")})]})]})},O=[{id:"deep-thinking",title:"deep_thinking",description:"deep_thinking_desc",icon:A.Qkp,color:"blue"},{id:"boosted-creativity",title:"boosted_creativity",description:"boosted_creativity_desc",icon:A.iih,color:"orange"},{id:"enhanced-productivity",title:"enhanced_productivity",description:"enhanced_productivity_desc",icon:A.ef0,color:"green"}];(0,T.E)(s.k);let Z=()=>{let e=(0,h.ff)("gray.50","gray.900"),i=(0,h.ff)("gray.800","white"),t=(0,h.ff)("gray.600","gray.300"),u=(0,n.S)({base:!0,md:!1});(0,n.S)({base:!0,sm:!1});let{t:_}=(0,M.$G)("common"),f={mindMap:(0,h.ff)("blue.500","red.400"),slides:(0,h.ff)("cyan.500","purple.400"),infographics:(0,h.ff)("green.500","green.400"),images:(0,h.ff)("red.500","orange.400"),text:(0,h.ff)("purple.500","orange.400"),ai:(0,h.ff)("blue.500","blue.400"),highlight:(0,h.ff)("teal.500","teal.300"),cardBg:(0,h.ff)("white","gray.800"),cardBorder:(0,h.ff)("gray.200","gray.700")},w={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.4,when:"beforeChildren",staggerChildren:u?.1:.2}}},j={hidden:{y:15,opacity:0},visible:{y:0,opacity:1,transition:{duration:u?.3:.5}}},y=[{id:"mindmap",title:_("category_mindmap_title"),icon:A.TdV,color:f.mindMap,link:"Mindmap",description:_("category_mindmap_desc")},{id:"infographics",title:_("category_infographics_title"),icon:A.NMh,color:f.infographics,link:"Infographics",description:_("category_infographics_desc")},{id:"slides",title:_("category_slides_title"),icon:A.lMi,color:f.slides,link:"Slides",description:_("category_slides_desc")},{id:"images",title:_("category_images_title"),icon:A.H3h,color:f.images,link:"Images",description:_("category_images_desc")},{id:"text",title:_("category_text_title"),icon:A.kpq,color:f.text,link:"Text",description:_("category_text_desc")}];return(0,a.jsxs)(E.Z,{bg:e,children:[(0,a.jsxs)(d.g,{spacing:{base:4,md:5},mb:{base:8,md:12},as:T.E.div,variants:w,initial:"hidden",animate:"visible",maxW:"4xl",mx:"auto",textAlign:"center",px:{base:3,md:0},width:"100%",children:[(0,a.jsxs)(o.x,{as:T.E.div,variants:j,display:"flex",justifyContent:"center",width:"100%",gap:4,flexWrap:{base:"nowrap",sm:"wrap"},alignItems:"center",children:[(0,a.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_source=badge-funblocks-ai-brainstorming",target:"_blank",children:(0,a.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=963998&theme=light&period=daily&t=1747700452719",alt:"FunBlocks AI Brainstorming - AI-Powered Brainstorming to Ignite Unlimited Creativity | Product Hunt",style:{width:"250px",height:"54px"},width:"250",height:"54"})}),(0,a.jsx)("a",{href:"https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_source=badge-funblocks-ai-brainstorming",target:"_blank",children:(0,a.jsx)("img",{src:"https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=963998&theme=light&period=weekly&topic_id=204&t=1747700452719",alt:"FunBlocks AI Brainstorming - AI-Powered Brainstorming to Ignite Unlimited Creativity | Product Hunt",style:{width:"250px",height:"54px"},width:"250",height:"54"})})]}),(0,a.jsxs)(s.k,{as:T.E.div,variants:j,mb:{base:1,md:2},direction:{base:"column",sm:"row"},gap:{base:2,sm:2},justify:"center",align:"center",width:"100%",flexWrap:{base:"nowrap",sm:"wrap"},children:[(0,a.jsx)(l.C,{colorScheme:"teal",fontSize:{base:"2xs",sm:"xs",md:"sm"},px:{base:2,md:3},py:{base:1,md:1},borderRadius:"full",textTransform:"uppercase",fontWeight:"medium",children:_("hero_badge_1")}),(0,a.jsx)(l.C,{colorScheme:"teal",fontSize:{base:"2xs",sm:"xs",md:"sm"},px:{base:2,md:3},py:{base:1,md:1},borderRadius:"full",textTransform:"uppercase",fontWeight:"medium",children:_("hero_badge_2")})]}),(0,a.jsxs)(g.X,{as:T.E.h1,variants:j,size:{base:"lg",sm:"xl",md:"2xl"},color:i,lineHeight:{base:"1.3",md:"1.2"},fontWeight:"bold",mb:{base:3,md:4},px:{base:2,md:0},children:[(0,a.jsx)(o.x,{as:"span",color:"blue.500",children:_("hero_heading_1")})," ",_("hero_heading_2")," ",(0,a.jsx)(o.x,{as:"span",bgGradient:"linear(to-r, blue.400, teal.500)",bgClip:"text",children:_("hero_heading_3")})]}),(0,a.jsxs)(m.x,{as:T.E.p,variants:j,fontSize:{base:"sm",sm:"md",md:"xl"},color:t,maxW:"3xl",mb:{base:4,md:6},px:{base:2,md:0},lineHeight:{base:"1.6",md:"1.7"},children:[_("platform_description_1")," ",_("hero_description")]}),(0,a.jsxs)(s.k,{as:T.E.div,variants:j,mt:{base:1,md:2},direction:{base:"column",sm:"row"},width:{base:"100%",sm:"auto"},justify:"center",align:"center",gap:{base:3,sm:4},children:[(0,a.jsx)(p.z,{as:r.r,href:"#ai_tools",size:{base:"md",md:"lg"},colorScheme:"blue",rightIcon:(0,a.jsx)(A.Z1Y,{}),_hover:{transform:"translateY(-2px)"},transition:"all 0.3s",px:{base:5,md:8},py:{base:6,md:6},borderRadius:"full",width:{base:"100%",sm:"auto"},fontSize:{base:"sm",md:"md"},fontWeight:"medium",height:{base:"48px",md:"auto"},children:_("explore_tools")}),(0,a.jsx)(p.z,{as:r.r,href:"#thinking-benefits-heading",size:{base:"md",md:"lg"},variant:"outline",colorScheme:"blue",_hover:{transform:"translateY(-2px)"},transition:"all 0.3s",px:{base:5,md:8},py:{base:6,md:6},borderRadius:"full",width:{base:"100%",sm:"auto"},fontSize:{base:"sm",md:"md"},fontWeight:"medium",height:{base:"48px",md:"auto"},children:_("learn_more")})]})]}),(0,a.jsx)(b.M,{columns:{base:1,md:3},spacing:{base:5,md:8},mb:{base:10,md:16},as:T.E.div,variants:w,initial:"hidden",animate:"visible",px:{base:3,md:0},width:"100%",children:O.map((e,o)=>(0,a.jsx)(J,{variants:j,bg:f.cardBg,p:{base:5,md:6},borderRadius:"lg",boxShadow:"md",border:"1px solid",borderColor:f.cardBorder,transition:"all 0.3s",_hover:{transform:"translateY(-5px)",boxShadow:"lg"},cursor:"pointer",onClick:()=>{let e=document.getElementById("thinking-benefits-heading");e&&e.scrollIntoView({behavior:"smooth"})},role:"group","aria-label":_(e.title),touchAction:"manipulation",children:(0,a.jsxs)(s.k,{direction:{base:"column",sm:"row"},align:{base:"center",sm:"flex-start"},textAlign:{base:"center",sm:"left"},gap:{base:3,md:4},children:[(0,a.jsx)(x.M,{bg:"".concat(e.color,".100"),color:"".concat(e.color,".500"),borderRadius:"md",p:{base:3,md:3},minW:{base:"50px",md:"50px"},minH:{base:"50px",md:"50px"},display:"flex",alignItems:"center",justifyContent:"center",_groupHover:{bg:"".concat(e.color,".200"),transform:"scale(1.05)"},transition:"all 0.2s",children:(0,a.jsx)(c.J,{as:e.icon,w:{base:5,md:6},h:{base:5,md:6}})}),(0,a.jsxs)(d.g,{align:{base:"center",sm:"start"},spacing:{base:1,md:1},children:[(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:i,lineHeight:"shorter",children:_(e.title)}),(0,a.jsx)(m.x,{color:t,fontSize:{base:"sm",md:"md"},lineHeight:"1.5",children:_(e.description).split("\n")[0]})]})]})},e.id))}),(0,a.jsx)(g.X,{as:T.E.h2,variants:j,size:{base:"md",md:"lg"},textAlign:"center",mb:{base:6,md:8},color:i,px:{base:3,md:0},children:_("discover_categories")}),(0,a.jsx)(b.M,{columns:{base:1,sm:2,lg:5},spacing:{base:5,md:8},as:T.E.div,variants:w,initial:"hidden",animate:"visible",px:{base:3,md:0},width:"100%",children:y.map(e=>(0,a.jsxs)(J,{variants:j,bg:f.cardBg,p:{base:5,md:6},borderRadius:"lg",boxShadow:"md",border:"1px solid",borderColor:f.cardBorder,transition:"all 0.3s",_hover:{transform:"translateY(-5px)",boxShadow:"lg"},onClick:()=>{let i=document.getElementById(e.link);i&&i.scrollIntoView({behavior:"smooth"})},cursor:"pointer",textDecoration:"none",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",height:"100%",role:"group","aria-label":e.title,touchAction:"manipulation",children:[(0,a.jsx)(x.M,{bg:"".concat(e.color),color:"white",borderRadius:"full",p:{base:3,md:4},mb:{base:3,md:4},minW:{base:"60px",md:"70px"},minH:{base:"60px",md:"70px"},_groupHover:{transform:"scale(1.05)",boxShadow:"md"},transition:"all 0.2s",children:(0,a.jsx)(c.J,{as:e.icon,w:{base:6,md:7},h:{base:6,md:7}})}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:i,mb:{base:2,md:2},lineHeight:"shorter",children:e.title}),(0,a.jsx)(m.x,{color:t,fontSize:{base:"xs",md:"sm"},lineHeight:"1.5",children:e.description})]},e.id))}),(0,a.jsx)(b.M,{columns:{base:1,md:3},spacing:{base:5,md:8},mt:{base:10,md:16},mb:{base:6,md:8},as:T.E.div,variants:w,initial:"hidden",animate:"visible",px:{base:3,md:0},width:"100%",children:[{value:"50+",label:_("stat_tools")},{value:"10x",label:_("stat_thinking")},{value:"24/7",label:_("stat_assistance")}].map((e,i)=>(0,a.jsxs)(J,{variants:j,textAlign:"center",bg:f.cardBg,p:{base:5,md:6},borderRadius:"lg",boxShadow:"sm",border:"1px solid",borderColor:f.cardBorder,transition:"all 0.3s",_hover:{transform:"translateY(-3px)",boxShadow:"md"},children:[(0,a.jsx)(g.X,{size:{base:"xl",md:"2xl"},bgGradient:"linear(to-r, blue.400, teal.500)",bgClip:"text",mb:{base:1,md:2},children:e.value}),(0,a.jsx)(m.x,{color:t,fontSize:{base:"md",md:"lg"},fontWeight:"medium",children:e.label})]},i))})]})},D=()=>{let{t:e}=(0,M.$G)("common"),i=(0,h.ff)("white","gray.700");return(0,a.jsx)(E.Z,{id:"thinking-benefits",bg:"blue.50",children:(0,a.jsxs)(d.g,{spacing:12,width:"100%",children:[(0,a.jsx)(g.X,{id:"thinking-benefits-heading",color:"dodgerblue",textAlign:"center",size:"xl",children:e("thinking_benefits_title")}),(0,a.jsx)(b.M,{columns:{base:1,md:3},spacing:8,width:"100%",children:O.map(t=>(0,a.jsx)(J,{bg:i,p:8,borderRadius:"xl",boxShadow:"xl",backgroundColor:"#fafafa",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5},children:(0,a.jsxs)(d.g,{spacing:4,align:"flex-start",children:[(0,a.jsxs)(u.U,{children:[(0,a.jsx)(x.M,{bg:"".concat(t.color,".100"),color:"".concat(t.color,".500"),borderRadius:"md",p:3,children:(0,a.jsx)(c.J,{as:t.icon,w:6,h:6})}),(0,a.jsx)(g.X,{size:"md",color:"".concat(t.color,".500"),children:e(t.title)})]}),(0,a.jsx)("ul",{children:e(t.description).split("\n").map((e,i)=>(0,a.jsx)("li",{children:(0,a.jsx)(m.x,{color:"gray.600",fontSize:"lg",children:e})},i))})]})},t.id))})]})})},Y=e=>{let{isMobile:i,basePath:t}=e,{t:n}=(0,M.$G)("common"),[h,x]=(0,C.useState)(""),[w,j]=(0,C.useState)("all"),[y,k]=(0,C.useState)([]),v={Mindmap:"blue",Infographics:"green",Slides:"cyan",Images:"red",Text:"purple","Mindmap Generator":"blue","AI Education":"teal","Creative Thinking":"purple","Critical Thinking":"cyan","Business Insights":"green","Psychological Insights":"pink","Image Insights":"red",all:"gray"},I=(0,C.useMemo)(()=>{let e=new Set;return Object.values(U).forEach(i=>{i.forEach(i=>{i.tag&&e.add(i.tag)})}),["all",...Array.from(e)]},[]),S=(0,C.useMemo)(()=>{let e=[];return 0===y.length?Object.keys(U).forEach(i=>{let t=U[i].map(e=>({...e,mainCategory:i}));e=[...e,...t]}):y.forEach(i=>{if(U[i]){let t=U[i].map(e=>({...e,mainCategory:i}));e=[...e,...t]}}),e.filter(e=>{let i=""===h||e.title.toLowerCase().includes(h.toLowerCase())||n(e.description).toLowerCase().includes(h.toLowerCase()),t="all"===w||e.tag===w;return i&&t})},[y,h,w,n]),z=(0,C.useMemo)(()=>{if(0!==y.length)return S.reduce((e,i)=>{let t=i.tag||"Uncategorized";return e[t]||(e[t]=[]),e[t].push(i),e},{});{let e={};S.forEach(i=>{let t=i.mainCategory||"Other";e[t]||(e[t]=[]),e[t].push(i)});let i={};return Object.keys(e).forEach(t=>{i["".concat(t)]=e[t]}),i}},[S,y]);return(0,a.jsxs)(E.Z,{id:"ai_tools",pt:{base:8,md:12},children:[(0,a.jsxs)(o.x,{px:{base:4,md:0},children:[(0,a.jsx)(g.X,{id:"ai_tools",size:{base:"lg",md:"xl"},bgGradient:"linear(to-r, blue.400, fuchsia)",bgClip:"text",mb:{base:4,md:6},mt:0,lineHeight:1.2,textAlign:"center",children:n("ai_tools_heading")}),(0,a.jsx)(m.x,{fontSize:{base:"md",md:"xl"},color:"gray.600",maxW:"3xl",mx:"auto",textAlign:"center",mb:{base:6,md:8},px:{base:2,md:0},children:n("ai_tools_description")})]}),(0,a.jsxs)(o.x,{mb:{base:8,md:10},width:"100%",px:{base:4,md:0},children:[(0,a.jsxs)(s.k,{align:"center",mb:{base:4,md:6},flexWrap:"nowrap",children:[(0,a.jsxs)(g.X,{size:{base:"sm",md:"md"},color:"gray.700",whiteSpace:"nowrap",children:[(0,a.jsx)(c.J,{as:A.QJe,color:"yellow.500",mr:2}),n("popular_tools","Popular Tools")]}),(0,a.jsx)(o.x,{flex:"1",height:"1px",bg:"gray.200",ml:4})]}),(0,a.jsx)(b.M,{columns:{base:1,sm:2,lg:4},spacing:{base:4,md:6},children:[U.Mindmap.find(e=>"AI Mindmap"===e.title),U.Infographics.find(e=>"AI Infographic Generator"===e.title),U.Slides.find(e=>"AI PPT/Slides"===e.title),U.Mindmap.find(e=>"AI Brainstorming"===e.title)].map((e,i)=>e&&(0,a.jsx)(F,{...e,description:n(e.description),link:t+e.link,tag:e.tag,isFeatured:!0,isLaunched:!0},i))})]}),(0,a.jsxs)(o.x,{mb:{base:8,md:10},width:"100%",px:{base:4,md:0},children:[(0,a.jsxs)(s.k,{align:"center",mb:{base:4,md:6},flexWrap:"nowrap",children:[(0,a.jsxs)(g.X,{size:{base:"sm",md:"md"},color:"gray.700",whiteSpace:"nowrap",display:"flex",alignItems:"center",children:[(0,a.jsx)(c.J,{as:A.ef0,color:"purple.500",mr:2}),n("whats_new","What's New"),(0,a.jsx)(l.C,{ml:2,colorScheme:"purple",variant:"solid",fontSize:{base:"2xs",md:"xs"},children:"NEW"})]}),(0,a.jsx)(o.x,{flex:"1",height:"1px",bg:"gray.200",ml:4})]}),(0,a.jsx)(b.M,{columns:{base:1,sm:2,lg:4},spacing:{base:4,md:6},children:[U.Mindmap.find(e=>"AI MindKit"===e.title),U.Infographics.find(e=>"AI InsightCards"===e.title),U.Mindmap.find(e=>"AI LogicLens"===e.title),U.Images.find(e=>"AI Avatar Generator"===e.title)].map((e,i)=>e&&(0,a.jsx)(F,{...e,description:n(e.description),link:t+e.link,tag:e.tag,isFeatured:!0,isNew:!0,isLaunched:!0},i))})]}),(0,a.jsxs)(o.x,{mb:{base:8,md:10},width:"100%",px:{base:4,md:0},children:[(0,a.jsxs)(s.k,{align:"center",mb:{base:8,md:12},mt:{base:4,md:6},flexWrap:"nowrap",children:[(0,a.jsx)(o.x,{flex:"1",height:"1px",bg:"gray.200",mr:4}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:"gray.700",whiteSpace:"nowrap",children:n("more","More")}),(0,a.jsx)(o.x,{flex:"1",height:"1px",bg:"gray.200",ml:4})]}),(0,a.jsx)(s.k,{justify:"center",mb:{base:6,md:8},overflowX:"auto",pb:{base:2,md:0},mx:{base:-4,md:0},px:{base:4,md:0},css:{"-webkit-overflow-scrolling":"touch",scrollbarWidth:"thin","&::-webkit-scrollbar":{height:"4px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"4px"}},style:{justifyContent:"unset"},children:(0,a.jsx)(u.U,{spacing:{base:2,md:4},flexWrap:{base:"nowrap",md:"wrap"},justify:"center",width:{base:"max-content",md:"100%"},children:Object.keys(U).map(e=>{let i=(e=>{switch(e){case"Mindmap":default:return"blue";case"Infographics":return"green";case"Slides":return"cyan";case"Images":return"red";case"Text":return"purple"}})(e),t="Mindmap"===e?A.TdV:"Infographics"===e?A.NMh:"Slides"===e?A.lMi:"Images"===e?A.H3h:"Text"===e?A.kpq:A.CP_;return(0,a.jsx)(p.z,{size:{base:"sm",md:"md"},colorScheme:i,variant:y.includes(e)?"solid":"outline",onClick:()=>{y.includes(e)?k([]):k([e]),j("all"),x("")},minW:{base:"auto",md:"120px"},flexShrink:0,leftIcon:(0,a.jsx)(c.J,{as:t,w:{base:4,md:5},h:{base:4,md:5}}),position:"relative",px:{base:3,md:4},_after:y.includes(e)?{content:'""',position:"absolute",bottom:"-4px",left:"50%",transform:"translateX(-50%)",width:{base:"40px",md:"68px"},height:"3px",borderRadius:"full",bg:"".concat(i,".500")}:{},children:e},e)})})}),(0,a.jsxs)(s.k,{direction:{base:"column",md:"row"},justify:"space-between",align:{base:"stretch",md:"center"},mb:{base:6,md:8},gap:{base:3,md:4},children:[(0,a.jsxs)(o.x,{position:"relative",w:{base:"full",md:"300px"},children:[(0,a.jsx)(_.I,{placeholder:n("search_tools","Search tools..."),value:h,onChange:e=>x(e.target.value),pr:"40px",bg:"white",borderColor:"gray.300",_hover:{borderColor:"blue.300"},_focus:{borderColor:"blue.500",boxShadow:"0 0 0 1px blue.500"},size:{base:"md",md:"md"},fontSize:{base:"sm",md:"md"}}),(0,a.jsx)(c.J,{as:A.U41,position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",color:"gray.400",w:{base:4,md:5},h:{base:4,md:5}})]}),(0,a.jsx)(o.x,{overflowX:"auto",w:"full",mx:{base:-4,md:0},px:{base:4,md:0},pb:{base:2,md:0},css:{"-webkit-overflow-scrolling":"touch",scrollbarWidth:"thin","&::-webkit-scrollbar":{height:"4px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"4px"}},children:(0,a.jsx)(u.U,{spacing:{base:1.5,md:2},flexWrap:{base:"nowrap",md:"wrap"},width:{base:"max-content",md:"100%"},pt:1,children:I.map(e=>{let i=w===e,t=v[e]||"blue";return(0,a.jsx)(o.x,{as:"button",px:{base:2,md:3},py:{base:1.5,md:2},borderRadius:"full",bg:i?"".concat(t,".500"):"transparent",color:i?"white":"".concat(t,".600"),border:"1px solid",borderColor:i?"".concat(t,".500"):"gray.200",cursor:"pointer",onClick:()=>j(e),fontSize:{base:"xs",md:"sm"},fontWeight:"medium",flexShrink:0,_hover:{bg:i?"".concat(t,".600"):"".concat(t,".50"),transform:"translateY(-2px)",boxShadow:"sm"},transition:"all 0.2s",boxShadow:i?"md":"none",minH:{base:"28px",md:"32px"},display:"flex",alignItems:"center",justifyContent:"center",children:"all"===e?n("all_categories","All Categories"):e},e)})})})]}),(0,a.jsx)(m.x,{color:"gray.500",mb:{base:4,md:6},fontSize:{base:"xs",md:"sm"},children:0===S.length?n("no_tools_found","No tools found"):"".concat(n("showing_tools","Showing")," ").concat(S.length," ").concat(1===S.length?n("tool","tool"):n("tools","tools"))}),(0,a.jsx)(d.g,{spacing:{base:8,md:10},align:"stretch",children:Object.keys(z).length>0?Object.keys(z).map(e=>(0,a.jsxs)(o.x,{children:[(0,a.jsxs)(s.k,{align:"center",mb:{base:4,md:6},id:e,flexWrap:"nowrap",children:["Uncategorized"!==e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.x,{p:{base:1.5,md:2},borderRadius:"md",bg:"".concat(v[e]||"blue",".50"),mr:{base:2,md:3},flexShrink:0,children:(0,a.jsx)(c.J,{as:"Mindmap"===e?A.TdV:"Infographics"===e?A.NMh:"Slides"===e?A.lMi:"Images"===e?A.H3h:"Text"===e?A.kpq:"Mindmap Generator"===e?A.TdV:"AI Education"===e?A.nGB:"Creative Thinking"===e?A.iih:"Critical Thinking"===e?A.ULU:"Business Insights"===e?A.Op:"Psychological Insights"===e?A.$0H:"Image Insights"===e?A.H3h:A.CP_,color:"".concat(v[e]||"blue",".600"),w:{base:4,md:5},h:{base:4,md:5}})}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:"gray.700",fontWeight:"600",noOfLines:1,flexShrink:0,children:e}),(0,a.jsxs)(o.x,{ml:{base:2,md:3},px:{base:1.5,md:2},py:{base:.5,md:1},borderRadius:"md",bg:"".concat(v[e]||"blue",".50"),color:"".concat(v[e]||"blue",".700"),fontSize:{base:"xs",md:"sm"},fontWeight:"medium",flexShrink:0,minW:{base:"40px",md:"auto"},textAlign:"center",children:[z[e].length," ",1===z[e].length?"tool":"tools"]})]}),(0,a.jsx)(o.x,{flex:"1",height:"1px",bg:"gray.200",ml:{base:2,md:4}})]}),(0,a.jsx)(b.M,{columns:{base:1,sm:2,lg:3},spacing:{base:4,md:8},w:"full",children:z[e].map((e,i)=>(0,a.jsx)(F,{...e,description:n(e.description),link:t+e.link,tag:e.tag},i))})]},e)):(0,a.jsxs)(o.x,{textAlign:"center",py:{base:8,md:10},px:{base:4,md:6},bg:"gray.50",borderRadius:"lg",children:[(0,a.jsx)(c.J,{as:A.U41,w:{base:8,md:10},h:{base:8,md:10},color:"gray.400",mb:{base:3,md:4}}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:"gray.500",mb:{base:1,md:2},children:n("no_tools_found","No tools found")}),(0,a.jsx)(m.x,{color:"gray.500",fontSize:{base:"sm",md:"md"},children:n("try_different_search","Try a different search term or category")}),(0,a.jsx)(p.z,{mt:{base:4,md:6},colorScheme:"blue",size:{base:"sm",md:"md"},onClick:()=>{x(""),j("all"),k([])},children:n("clear_filters","Clear Filters")})]})})]}),!i&&S.length>6&&(0,a.jsx)(o.x,{position:"fixed",right:"20px",top:"50%",transform:"translateY(-50%)",zIndex:10,display:{base:"none",xl:"block"},children:(0,a.jsx)(d.g,{spacing:2,bg:"white",p:3,borderRadius:"lg",boxShadow:"lg",children:Object.keys(z).map(e=>(0,a.jsx)(f.u,{label:e,placement:"left",hasArrow:!0,bg:"".concat(v[e]||"blue",".500"),children:(0,a.jsx)(r.r,{href:"#".concat(e),_hover:{textDecoration:"none"},children:(0,a.jsx)(o.x,{p:2,borderRadius:"md",bg:"".concat(v[e]||"blue",".50"),_hover:{bg:"".concat(v[e]||"blue",".100")},transition:"all 0.2s",children:(0,a.jsx)(c.J,{as:"Mindmap"===e?A.TdV:"Infographics"===e?A.NMh:"Slides"===e?A.lMi:"Images"===e?A.H3h:"Text"===e?A.kpq:"Mindmap Generator"===e?A.TdV:"AI Education"===e?A.nGB:"Creative Thinking"===e?A.iih:"Critical Thinking"===e?A.ULU:"Business Insights"===e?A.Op:"Psychological Insights"===e?A.$0H:"Image Insights"===e?A.H3h:A.CP_,color:"".concat(v[e]||"blue",".500")})})})},e))})})]})},N=()=>{let{t:e}=(0,M.$G)("common");return(0,n.S)({base:!0,md:!1}),(0,a.jsx)(E.Z,{bg:"blue.50",id:"aiflow-section",children:(0,a.jsxs)(d.g,{spacing:8,width:"100%",children:[(0,a.jsx)(g.X,{size:"xl",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",mb:4,children:e("aiflow_and_aitools_title")}),(0,a.jsx)(m.x,{fontSize:{base:"md",md:"lg"},color:"gray.700",maxW:"3xl",textAlign:"center",mb:6,children:e("continue_exploring_ai_content")}),(0,a.jsxs)(b.M,{columns:{base:1,lg:3},spacing:8,width:"100%",maxW:"7xl",children:[(0,a.jsx)(o.x,{p:6,bg:"purple.50",borderRadius:"xl",boxShadow:"lg",position:"relative",overflow:"hidden",height:"100%",borderTop:"4px solid",borderColor:"purple.500",children:(0,a.jsxs)(d.g,{spacing:5,align:"start",height:"100%",children:[(0,a.jsxs)(u.U,{spacing:4,children:[(0,a.jsx)(x.M,{bg:"purple.100",p:2,borderRadius:"md",children:(0,a.jsx)(c.J,{as:A.CP_,w:6,h:6,color:"purple.500"})}),(0,a.jsx)(g.X,{size:"md",color:"purple.600",children:e("about_aitools_title")})]}),(0,a.jsxs)(d.g,{spacing:4,align:"start",width:"100%",children:[(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aitools_point1")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aitools_point2")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aitools_point3")})]})]})]})}),(0,a.jsx)(o.x,{p:6,bg:"white",borderRadius:"xl",boxShadow:"lg",position:"relative",overflow:"hidden",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",children:(0,a.jsxs)(d.g,{spacing:4,width:"100%",children:[(0,a.jsxs)(u.U,{spacing:4,justifyContent:"center",width:"100%",children:[(0,a.jsx)(c.J,{as:A.iih,w:6,h:6,color:"blue.500"}),(0,a.jsx)(g.X,{size:"md",color:"blue.600",children:e("what_after_generation")})]}),(0,a.jsxs)(o.x,{width:"100%",position:"relative",py:4,children:[(0,a.jsxs)(o.x,{p:4,bg:"purple.50",borderRadius:"lg",boxShadow:"md",mb:10,position:"relative",zIndex:1,children:[(0,a.jsxs)(u.U,{children:[(0,a.jsx)(c.J,{as:A.CP_,w:5,h:5,color:"purple.500"}),(0,a.jsx)(m.x,{fontWeight:"bold",color:"purple.600",children:"AI Tools"})]}),(0,a.jsx)(m.x,{fontSize:"sm",color:"gray.600",mt:1,children:e("ai_tools_generation_explanation")})]}),(0,a.jsx)(o.x,{position:"absolute",top:"30%",left:"50%",transform:"translateX(-50%)",width:"2px",height:"40%",bg:"blue.400",zIndex:0}),(0,a.jsx)(x.M,{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bg:"white",borderRadius:"full",boxShadow:"md",p:2,zIndex:1,children:(0,a.jsx)(c.J,{as:A.NWQ,w:5,h:5,color:"blue.500"})}),(0,a.jsxs)(o.x,{p:4,bg:"blue.50",borderRadius:"lg",boxShadow:"md",position:"relative",zIndex:1,children:[(0,a.jsxs)(u.U,{children:[(0,a.jsx)(c.J,{as:A.iNY,w:5,h:5,color:"blue.500"}),(0,a.jsx)(m.x,{fontWeight:"bold",color:"blue.600",children:"AIFlow"})]}),(0,a.jsx)(m.x,{fontSize:"sm",color:"gray.600",mt:1,children:e("continue_with_aiflow")})]})]}),(0,a.jsx)(r.r,{href:"https://www.funblocks.net/aiflow",isExternal:!0,mt:2,width:"100%",children:(0,a.jsx)(p.z,{colorScheme:"blue",size:"md",width:"100%",rightIcon:(0,a.jsx)(A.Wgy,{}),_hover:{transform:"translateY(-2px)",boxShadow:"md"},transition:"all 0.3s",children:e("try_aiflow_now")})})]})}),(0,a.jsx)(o.x,{p:6,bg:"blue.50",borderRadius:"xl",boxShadow:"lg",position:"relative",overflow:"hidden",height:"100%",borderTop:"4px solid",borderColor:"blue.500",children:(0,a.jsxs)(d.g,{spacing:5,align:"start",height:"100%",children:[(0,a.jsxs)(u.U,{spacing:4,children:[(0,a.jsx)(x.M,{bg:"blue.100",p:2,borderRadius:"md",children:(0,a.jsx)(c.J,{as:A.iNY,w:6,h:6,color:"blue.500"})}),(0,a.jsx)(g.X,{size:"md",color:"blue.600",children:e("about_aiflow_title")})]}),(0,a.jsxs)(d.g,{spacing:4,align:"start",width:"100%",children:[(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aiflow_point1")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aiflow_point2")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(c.J,{as:A.l_A,w:5,h:5,color:"green.500",flexShrink:0}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("about_aiflow_point3")})]})]})]})})]}),(0,a.jsx)(o.x,{maxW:"5xl",p:{base:4,md:8},bg:"white",borderRadius:"xl",boxShadow:"lg",mt:6,width:"100%",children:(0,a.jsxs)(b.M,{columns:{base:1,md:2},spacing:8,children:[(0,a.jsxs)(d.g,{spacing:5,align:"start",children:[(0,a.jsx)(g.X,{size:"md",color:"teal.600",children:e("deep_dive_capabilities")}),(0,a.jsxs)(d.g,{spacing:4,align:"start",width:"100%",children:[(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(x.M,{bg:"teal.100",borderRadius:"full",w:8,h:8,flexShrink:0,children:(0,a.jsx)(m.x,{fontWeight:"bold",color:"teal.700",children:"1"})}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("click_continue_to_explore")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(x.M,{bg:"teal.100",borderRadius:"full",w:8,h:8,flexShrink:0,children:(0,a.jsx)(m.x,{fontWeight:"bold",color:"teal.700",children:"2"})}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("access_funblocks_aiflow")})]}),(0,a.jsxs)(u.U,{spacing:3,width:"100%",children:[(0,a.jsx)(x.M,{bg:"teal.100",borderRadius:"full",w:8,h:8,flexShrink:0,children:(0,a.jsx)(m.x,{fontWeight:"bold",color:"teal.700",children:"3"})}),(0,a.jsx)(m.x,{fontSize:"md",color:"gray.700",children:e("aitools_not_covered")})]})]}),(0,a.jsx)(o.x,{p:4,bg:"blue.50",borderRadius:"md",width:"100%",mt:2,children:(0,a.jsxs)(m.x,{fontSize:"sm",fontStyle:"italic",color:"blue.700",children:[e("aitools_not_covered")," ",e("continue_with_aiflow")]})})]}),(0,a.jsxs)(o.x,{children:[(0,a.jsx)(w.E,{src:"https://www.funblocks.net/img/portfolio/thumbnails/aitools_mindmap_book_generated.png",alt:"AIFlow Exploration Interface",borderRadius:"md",boxShadow:"md",width:"100%",height:"auto",objectFit:"cover"}),(0,a.jsx)(m.x,{fontSize:"sm",color:"gray.500",mt:2,textAlign:"center",children:e("access_funblocks_aiflow")})]})]})})]})})},V=()=>{let{t:e}=(0,M.$G)("common"),[i]=(0,j.a)("(max-width: 768px)"),{basePath:t}=R()().publicRuntimeConfig,n=[{icon:A.Qkp,title:e("ai_powered_intelligence"),description:e("ai_powered_intelligence_desc"),gradient:"linear(to-r, blue.400, purple.500)"},{icon:A.ef0,title:e("mental_models_toolify"),description:e("mental_models_toolify_desc"),gradient:"linear(to-r, purple.400, pink.500)"},{icon:A.CP_,title:e("integrated_toolset"),description:e("integrated_toolset_desc"),gradient:"linear(to-r, green.400, teal.500)"},{icon:A.wN,title:e("user_friendly"),description:e("user_friendly_desc"),gradient:"linear(to-r, orange.400, red.500)"}],l=[{question:e("platform_faq_1_q"),answer:e("platform_faq_1_a")},{question:e("platform_faq_2_q"),answer:e("platform_faq_2_a")},{question:e("platform_faq_3_q"),answer:e("platform_faq_3_a")},{question:e("platform_faq_4_q"),answer:e("platform_faq_4_a")},{question:e("platform_faq_5_q"),answer:e("platform_faq_5_a")},{question:e("platform_faq_6_q"),answer:e("platform_faq_6_a")},{question:e("platform_faq_7_q"),answer:e("platform_faq_7_a")},{question:e("platform_faq_8_q"),answer:e("platform_faq_8_a")},{question:e("platform_faq_9_q"),answer:e("platform_faq_9_a")},{question:e("platform_faq_10_q"),answer:e("platform_faq_10_a")},{question:e("platform_faq_11_q"),answer:e("platform_faq_11_a")},{question:e("platform_faq_12_q"),answer:e("platform_faq_12_a")},{question:e("platform_faq_13_q"),answer:e("platform_faq_13_a")},{question:e("platform_faq_14_q"),answer:e("platform_faq_14_a")},{question:e("platform_faq_15_q"),answer:e("platform_faq_15_a")}],h=[{title:e("comparison_visual_title"),chatgpt:e("chatgpt_text_heavy"),funblocks:e("funblocks_visual_friendly"),icon:A.H3h},{title:e("comparison_exploration_title"),chatgpt:e("chatgpt_linear_chat"),funblocks:e("funblocks_multi_perspective"),icon:A.TdV},{title:e("comparison_guidance_title"),chatgpt:e("chatgpt_passive_waiting"),funblocks:e("funblocks_proactive_guide"),icon:A.ef0},{title:e("comparison_learning_title"),chatgpt:e("chatgpt_answer_only"),funblocks:e("funblocks_thinking_process"),icon:A.Qkp}];return(0,a.jsxs)(d.g,{width:"100%",position:"relative",overflowY:"hidden",gap:"4px",children:[(0,a.jsx)(q.Z,{}),(0,a.jsx)(d.g,{overflowY:"auto",height:"calc(100vh - 76px)",alignItems:"center",width:"100%",children:(0,a.jsxs)(d.g,{style:{width:"100%",gap:"0px"},children:[(0,a.jsx)(Z,{}),(0,a.jsx)(Y,{isMobile:i,basePath:t}),(0,a.jsx)(D,{}),(0,a.jsx)(E.Z,{bg:"gray.50",children:(0,a.jsxs)(d.g,{spacing:8,align:"center",children:[(0,a.jsx)(g.X,{size:"xl",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",children:e("examples_showcase_title","See Our Tools in Action")}),(0,a.jsx)(m.x,{fontSize:"xl",color:"gray.600",maxW:"3xl",textAlign:"center",mb:6,children:e("examples_showcase_desc","Explore real examples of what you can create with our AI-powered tools")}),(0,a.jsx)(b.M,{columns:{base:1,md:2},spacing:8,width:"100%",children:[{title:e("example_mindmap_title","AI Mindmap Example"),description:e("example_mindmap_desc",'Visualize complex topics with our AI Mindmap tool. This example shows a mindmap about the book "The Great Gatsby".'),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aitools_mindmap_book.png",link:"/mindmap"},{title:e("example_infographic_title","AI Infographic Example"),description:e("example_infographic_desc","Create beautiful infographics instantly. This example shows a witty and attractive infographic generated with InsightCards."),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_paradoxical.png",link:"/infographic"},{title:e("example_slides_title","AI Slides Example"),description:e("example_slides_desc","Generate professional presentations in seconds. This example shows a competitor analysis presentation."),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aislides_beyond_chatgpt.png",link:"/slides"},{title:e("example_mindsnap_title","AI MindSnap Example"),description:e("example_mindsnap_desc","Transform topics into visual mental models. This example shows a SWOT analysis."),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png",link:"/mindsnap"}].map((n,s)=>(0,a.jsxs)(o.x,{bg:"white",borderRadius:"xl",boxShadow:"xl",overflow:"hidden",transition:"all 0.3s",_hover:{transform:"translateY(-5px)",boxShadow:"2xl"},children:[(0,a.jsx)(w.E,{src:n.imageSrc,alt:n.title,width:"100%",height:i?"250px":"400px",objectFit:"contain"}),(0,a.jsxs)(o.x,{p:6,children:[(0,a.jsx)(g.X,{size:"md",mb:2,children:n.title}),(0,a.jsx)(m.x,{color:"gray.600",mb:4,children:n.description}),(0,a.jsx)(p.z,{rightIcon:(0,a.jsx)(A.Dli,{}),colorScheme:"blue",variant:"outline",size:"sm",as:r.r,target:"_blank",href:t+n.link,textDecoration:"none",children:e("example_try_tool","Try this tool")})]})]},s))})]})}),(0,a.jsx)(N,{}),(0,a.jsxs)(E.Z,{bg:"gray.50",children:[(0,a.jsx)(g.X,{mb:12,textAlign:"center",size:"xl",color:"#333",children:e("why_funblocks")}),(0,a.jsx)(b.M,{columns:{base:1,md:2},spacing:8,children:n.map((e,i)=>(0,a.jsxs)(o.x,{bg:"white",p:8,borderRadius:"lg",boxShadow:"md",_hover:{transform:"translateY(-4px)",boxShadow:"lg"},transition:"all 0.3s",children:[(0,a.jsx)(c.J,{as:e.icon,w:10,h:10,mb:4,style:{color:"dodgerblue"},bgGradient:e.gradient,bgClip:"text"}),(0,a.jsx)(g.X,{size:"md",mb:4,children:e.title}),(0,a.jsx)(m.x,{color:"gray.600",children:e.description})]},i))})]}),(0,a.jsx)(E.Z,{bg:"white",py:{base:12,md:16},children:(0,a.jsxs)(d.g,{spacing:{base:6,md:8},width:"100%",children:[(0,a.jsxs)(J,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5},textAlign:"center",maxW:"3xl",mx:"auto",px:{base:4,md:0},children:[(0,a.jsx)(g.X,{mb:{base:4,md:5},color:"#333",size:"xl",bgGradient:"linear(to-r, blue.500, purple.500)",bgClip:"text",children:e("why_not_chatgpt")}),(0,a.jsx)(m.x,{fontSize:{base:"lg",md:"xl"},color:"gray.600",maxW:"3xl",textAlign:"center",children:e("chatgpt_comparison_intro")})]}),(0,a.jsx)(b.M,{columns:{base:1,lg:2},spacing:{base:6,md:8},width:"100%",maxW:"container.xl",mx:"auto",children:h.map((e,i)=>(0,a.jsxs)(J,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.1*i},overflow:"hidden",borderRadius:"xl",boxShadow:"lg",bg:"white",border:"1px solid",borderColor:"gray.100",children:[(0,a.jsxs)(s.k,{direction:"row",alignItems:"center",bg:"purple.50",p:{base:4,md:5},borderBottom:"1px solid",borderColor:"gray.100",textAlign:"center",gap:4,children:[(0,a.jsx)(c.J,{as:e.icon,w:{base:8,md:10},h:{base:8,md:10},color:"purple.500"}),(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},color:"purple.700",fontWeight:"semibold",children:e.title})]}),(0,a.jsxs)(o.x,{p:{base:5,md:6},children:[(0,a.jsxs)(o.x,{mb:5,p:{base:4,md:5},bg:"gray.50",borderRadius:"lg",borderLeft:"4px solid",borderColor:"gray.300",children:[(0,a.jsxs)(s.k,{align:"center",mb:2,children:[(0,a.jsx)(c.J,{as:z.Cp6,color:"gray.500",mr:2}),(0,a.jsx)(m.x,{fontWeight:"bold",color:"gray.700",children:"ChatGPT"})]}),(0,a.jsx)(m.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},children:e.chatgpt})]}),(0,a.jsxs)(o.x,{p:{base:4,md:5},bg:"blue.50",borderRadius:"lg",borderLeft:"4px solid",borderColor:"blue.400",children:[(0,a.jsxs)(s.k,{align:"center",mb:2,children:[(0,a.jsx)(c.J,{as:A.ef0,color:"blue.500",mr:2}),(0,a.jsx)(m.x,{fontWeight:"bold",color:"blue.600",children:"FunBlocks AI"})]}),(0,a.jsx)(m.x,{color:"blue.700",fontSize:{base:"sm",md:"md"},children:e.funblocks})]})]})]},i))}),(0,a.jsx)(J,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.4},mt:{base:6,md:8},children:(0,a.jsx)(p.z,{as:r.r,href:"#ai_tools",size:{base:"md",md:"lg"},colorScheme:"blue",rightIcon:(0,a.jsx)(A.Wgy,{}),_hover:{transform:"translateY(-2px)"},transition:"all 0.3s",px:{base:6,md:8},py:{base:6,md:7},borderRadius:"full",boxShadow:"md",children:e("explore_tools")})})]})}),(0,a.jsx)(X.Z,{title:e("tools_comparison_title","How FunBlocks AI Compares"),description:e("tools_comparison_description","See how FunBlocks AI stacks up against other AI tools and platforms in key areas that matter for productivity and learning."),highlightColumn:"funblocks",bg:"gray.50",columns:[{key:"funblocks",label:"FunBlocks AI"},{key:"chatgpt",label:"ChatGPT"},{key:"other_tools",label:"Other AI Tools"}],features:[{name:e("comparison_feature_1","Visual Thinking Tools"),funblocks:!0,chatgpt:!1,other_tools:!1,tooltip:e("comparison_feature_1_tooltip","Specialized tools for creating mindmaps, infographics, and visual presentations")},{name:e("comparison_feature_2","Educational Frameworks"),funblocks:!0,chatgpt:!1,other_tools:!1,tooltip:e("comparison_feature_2_tooltip","Built-in educational frameworks like Bloom's Taxonomy, Marzano, and SOLO")},{name:e("comparison_feature_3","Multi-perspective Analysis"),funblocks:!0,chatgpt:!1,other_tools:!0,tooltip:e("comparison_feature_3_tooltip","Ability to analyze topics from multiple perspectives and mental models")},{name:e("comparison_feature_4","Structured Outputs"),funblocks:!0,chatgpt:!1,other_tools:!0,tooltip:e("comparison_feature_4_tooltip","Consistently structured outputs optimized for learning and retention")},{name:e("comparison_feature_5","Specialized for Learning"),funblocks:!0,chatgpt:!1,other_tools:!1,tooltip:e("comparison_feature_5_tooltip","Tools specifically designed to enhance learning and comprehension")},{name:e("comparison_feature_6","Visual Export Options"),funblocks:!0,chatgpt:!1,other_tools:!0,tooltip:e("comparison_feature_6_tooltip","Export as SVG, PNG, PDF and other visual formats")},{name:e("comparison_feature_7","Research-Backed Design"),funblocks:!0,chatgpt:!1,other_tools:!1,tooltip:e("comparison_feature_7_tooltip","Tools designed based on cognitive science and learning research")}]}),(0,a.jsx)(E.Z,{bg:"white",children:(0,a.jsxs)(d.g,{spacing:8,align:"center",children:[(0,a.jsx)(g.X,{size:"xl",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",color:"#333",children:e("llm_support_title")}),(0,a.jsx)(m.x,{fontSize:"xl",color:"gray.600",maxW:"3xl",textAlign:"center",mb:6,children:e("llm_support_desc")}),(0,a.jsx)(b.M,{columns:{base:1,md:2,lg:4},spacing:8,width:"100%",children:[{name:e("model_openai"),description:e("model_openai_desc"),icon:z.Cp6,color:"#10a37f",gradient:"linear(to-r, green.400, teal.500)",bgColor:"rgba(16, 163, 127, 0.1)"},{name:e("model_anthropic"),description:e("model_anthropic_desc"),icon:z.o0e,color:"#b15dff",gradient:"linear(to-r, purple.400, pink.500)",bgColor:"rgba(177, 93, 255, 0.1)"},{name:e("model_google"),description:e("model_google_desc"),icon:z.BbJ,color:"#1a73e8",gradient:"linear(to-r, blue.400, cyan.400)",bgColor:"rgba(26, 115, 232, 0.1)"},{name:e("model_deepseek"),description:e("model_deepseek_desc"),color:"#ff6b01",gradient:"linear(to-r, orange.400, red.400)",bgColor:"rgba(255, 107, 1, 0.1)"}].map((e,i)=>(0,a.jsx)(T.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.5,delay:.1*i},children:(0,a.jsxs)(o.x,{bg:"white",p:6,borderRadius:"xl",boxShadow:"md",_hover:{transform:"translateY(-4px)",boxShadow:"xl",borderColor:e.color},transition:"all 0.3s",borderTop:"4px solid",borderColor:e.color,position:"relative",overflow:"hidden",height:"100%",children:[(0,a.jsx)(o.x,{position:"absolute",top:"0",left:"0",right:"0",height:"100%",bg:e.bgColor,opacity:"0.5",zIndex:"0"}),(0,a.jsxs)(d.g,{spacing:4,align:"start",position:"relative",zIndex:"1",height:"100%",children:[(0,a.jsxs)(u.U,{children:[e.icon&&(0,a.jsx)(c.J,{as:e.icon,w:10,h:10,color:e.color}),(0,a.jsx)(g.X,{size:"md",fontWeight:"bold",children:e.name})]}),(0,a.jsx)(m.x,{color:"gray.600",children:e.description})]})]})},i))}),(0,a.jsx)(o.x,{mt:8,p:4,borderRadius:"lg",bg:"gray.100",borderLeft:"4px solid",borderColor:"blue.400",maxW:"3xl",children:(0,a.jsx)(m.x,{fontSize:"md",color:"blue.500",textAlign:"center",children:e("llm_support_notes")})})]})}),(0,a.jsx)(E.Z,{bg:"blue.50",children:(0,a.jsxs)(d.g,{spacing:8,align:"center",children:[(0,a.jsx)(g.X,{size:"xl",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",children:e("target_audience_title")}),(0,a.jsx)(m.x,{fontSize:"xl",color:"gray.600",maxW:"3xl",textAlign:"center",mb:8,children:e("target_audience_desc")}),(0,a.jsx)(b.M,{columns:{base:1,md:3},spacing:8,width:"100%",children:[{title:e("target_audience_students"),desc:e("target_audience_students_desc"),icon:A.n_Y},{title:e("target_audience_professionals"),desc:e("target_audience_professionals_desc"),icon:A.$yZ},{title:e("target_audience_creatives"),desc:e("target_audience_creatives_desc"),icon:A.SaA}].map((e,i)=>(0,a.jsx)(o.x,{bg:"white",p:8,borderRadius:"xl",boxShadow:"xl",_hover:{transform:"translateY(-4px)",boxShadow:"2xl"},transition:"all 0.3s",children:(0,a.jsxs)(d.g,{spacing:4,align:"start",children:[(0,a.jsx)(c.J,{as:e.icon,w:10,h:10,color:"blue.500"}),(0,a.jsx)(g.X,{size:"md",children:e.title}),(0,a.jsx)(m.x,{color:"gray.600",children:e.desc})]})},i))})]})}),(0,a.jsx)(E.Z,{bg:"gray.50",children:(0,a.jsxs)(d.g,{spacing:8,align:"center",children:[(0,a.jsx)(g.X,{size:"xl",bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",children:e("use_cases_title")}),(0,a.jsx)(m.x,{fontSize:"xl",color:"gray.600",maxW:"3xl",textAlign:"center",mb:8,children:e("use_cases_desc")}),(0,a.jsx)(b.M,{columns:{base:1,md:2,lg:3},spacing:8,width:"100%",children:[{title:e("use_case_1_title"),desc:e("use_case_1_desc"),icon:A.nGB},{title:e("use_case_2_title"),desc:e("use_case_2_desc"),icon:A.Op},{title:e("use_case_3_title"),desc:e("use_case_3_desc"),icon:A.iih},{title:e("use_case_4_title"),desc:e("use_case_4_desc"),icon:A.U41},{title:e("use_case_5_title"),desc:e("use_case_5_desc"),icon:A.q1E},{title:e("use_case_6_title"),desc:e("use_case_6_desc"),icon:A.I$}].map((e,i)=>(0,a.jsx)(o.x,{bg:"white",p:8,borderRadius:"xl",boxShadow:"xl",_hover:{transform:"translateY(-4px)",boxShadow:"2xl"},transition:"all 0.3s",children:(0,a.jsxs)(d.g,{spacing:4,align:"start",children:[(0,a.jsx)(c.J,{as:e.icon,w:10,h:10,color:"blue.500"}),(0,a.jsx)(g.X,{size:"md",children:e.title}),(0,a.jsx)(m.x,{color:"gray.600",children:e.desc})]})},i))})]})}),(0,a.jsx)(P.Z,{caseStudies:[{icon:A.nGB,title:e("case_study_1_title","Educational Institution Transformation"),industry:e("case_study_1_industry","Higher Education"),challenge:e("case_study_1_challenge","A leading university struggled with helping students visualize complex concepts across multiple disciplines, resulting in lower comprehension and retention rates."),solution:e("case_study_1_solution","Implemented a comprehensive suite of FunBlocks AI tools across departments: MarzanoBrain and BloomBrain for creating structured learning materials, Slides for generating interactive presentations, and Brainstorming, Critical Thinking, and Creative Thinking tools to develop students' cognitive abilities."),results:e("case_study_1_results","Significant improvement in student comprehension and retention rates. Students demonstrated enhanced critical thinking skills and ability to connect concepts across disciplines. Faculty reported more engaging classroom discussions and higher quality student work."),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png"},{icon:A.iih,title:e("case_study_2_title","Innovation Thinking Enhancement"),industry:e("case_study_2_industry","Product Design & Marketing"),challenge:e("case_study_2_challenge","A multinational company struggled with fostering innovative thinking among their product design and marketing teams, resulting in predictable solutions and declining market differentiation."),solution:e("case_study_2_solution","Integrated FunBlocks AI Brainstorming, MindKit, MindSnap, OKR Assistant, Task Planner and other FunBlocks AI tools into their ideation process. Teams used these tools to explore multiple perspectives, challenge assumptions, and visualize connections between seemingly unrelated concepts."),results:e("case_study_2_results","Teams developed more innovative product designs and marketing campaigns that resonated with customers. The company reported increased creative output, more diverse solution sets, and improved cross-team collaboration on complex projects."),imageSrc:"https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_eisenhower_matrix.png"}],description:e("case_studies_description","See how organizations across different sectors have leveraged FunBlocks AI tools to solve real challenges and achieve measurable results."),appname:"FunBlocks AI",onClick:()=>document.getElementById("ai_tools").scrollIntoView({behavior:"smooth"})}),(0,a.jsx)(G.Z,{title:e("research_title","Research-Backed Approach"),description:e("research_description","Our tools are built on solid scientific foundations and proven cognitive principles to maximize learning and productivity."),researchAreas:[{title:e("research_area_1_title","Cognitive Load Theory"),description:e("research_area_1_description","Our visual tools reduce cognitive load by organizing information spatially, allowing users to process complex concepts more efficiently while minimizing mental effort."),icon:A.Qkp,color:"blue",citations:[{text:"Sweller, J., van Merri\xebnboer, J. J. G., & Paas, F. (2019). Cognitive Architecture and Instructional Design: 20 Years Later. Educational Psychology Review, 31(2), 261-292.",url:"https://doi.org/10.1007/s10648-019-09465-5"},{text:"Mayer, R. E. (2014). The Cambridge Handbook of Multimedia Learning (2nd ed.). Cambridge University Press.",url:"https://doi.org/10.1017/CBO9781139547369"}]},{title:e("research_area_2_title","Visual Learning Efficacy"),description:e("research_area_2_description","Research shows visual learning can improve understanding by up to 400% and retention by 38% compared to text-only learning, making complex information more accessible and memorable."),icon:A.H3h,color:"green",citations:[{text:"Medina, J. (2014). Brain Rules: 12 Principles for Surviving and Thriving at Work, Home, and School (2nd ed.). Pear Press.",url:"https://brainrules.net/brain-rules/"},{text:"Paivio, A. (2014). Mind and Its Evolution: A Dual Coding Theoretical Approach. Psychology Press.",url:"https://doi.org/10.4324/9781315785233"},{text:"Mayer, R. E., & Moreno, R. (2003). Nine Ways to Reduce Cognitive Load in Multimedia Learning. Educational Psychologist, 38(1), 43-52.",url:"https://doi.org/10.1207/S15326985EP3801_6"}]},{title:e("research_area_3_title","Mental Models & Frameworks"),description:e("research_area_3_description","Our tools leverage established mental models and educational frameworks that help structure thinking, improve problem-solving capabilities, and enhance conceptual understanding across disciplines."),icon:B.YgO,color:"orange",citations:[{text:"Johnson-Laird, P. N. (2010). Mental models and human reasoning. Proceedings of the National Academy of Sciences, 107(43), 18243-18250.",url:"https://doi.org/10.1073/pnas.1012933107"},{text:"Gentner, D., & Stevens, A. L. (Eds.). (2014). Mental Models. Psychology Press.",url:"https://doi.org/10.4324/9781315802725"},{text:"Krathwohl, D. R. (2002). A Revision of Bloom's Taxonomy: An Overview. Theory Into Practice, 41(4), 212-218.",url:"https://doi.org/10.1207/s15430421tip4104_2"}]},{title:e("research_area_4_title","AI-Enhanced Learning"),description:e("research_area_4_description","Studies show that AI-assisted learning tools can personalize the educational experience, provide adaptive feedback, and improve outcomes across diverse learning styles and contexts."),icon:A.iNY,color:"purple",citations:[{text:"UNESCO. (2021). AI and education: Guidance for policy-makers. UNESCO Digital Library.",url:"https://unesdoc.unesco.org/ark:/48223/pf0000376709"},{text:"Holmes, W., Bialik, M., & Fadel, C. (2019). Artificial Intelligence in Education: Promises and Implications for Teaching and Learning. Center for Curriculum Redesign.",url:"https://curriculumredesign.org/our-work/artificial-intelligence-in-education/"},{text:"Zawacki-Richter, O., Mar\xedn, V. I., Bond, M., & Gouverneur, F. (2019). Systematic review of research on artificial intelligence applications in higher education – where are the educators? International Journal of Educational Technology in Higher Education, 16, 39.",url:"https://doi.org/10.1186/s41239-019-0171-0"}]}],bg:"blue.50"}),(0,a.jsx)(H.Z,{appname:"FunBlocks AI",rating:"4.8",users:"20,000+",testimonials:[{content:e("testimonial_1_content","MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!"),author:"Emily Johnson",role:e("testimonial_1_role","Medical Student"),organization:"Stanford University",rating:5,image:"https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:e("testimonial_2_content","As a product manager, I need to communicate complex ideas to different teams. The AI Infographic Generator has become my secret weapon for creating visually stunning concept maps and comparison charts in minutes instead of hours."),author:"Michael Chen",role:e("testimonial_2_role","Product Manager"),organization:"Salesforce",rating:5,image:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"},{content:e("testimonial_3_content","I've tried many brainstorming tools, but FunBlocks AI Brainstorming Assistant is in a league of its own. It doesn't just generate ideas - it helps structure and refine them in ways I wouldn't have considered. It's like having a creative thinking partner available 24/7."),author:"Sarah Williams",role:e("testimonial_3_role","Creative Director"),organization:"Design Studio NYC",rating:4,image:"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"}]}),(0,a.jsx)(E.Z,{bg:"gray.50",children:(0,a.jsxs)(d.g,{spacing:{base:6,md:8},align:"center",px:{base:3,md:0},children:[(0,a.jsx)(g.X,{size:{base:"lg",md:"xl"},bgGradient:"linear(to-r, blue.400, purple.500)",bgClip:"text",textAlign:"center",lineHeight:{base:"1.3",md:"1.2"},children:e("common_questions")}),(0,a.jsx)(m.x,{fontSize:{base:"md",md:"xl"},color:"gray.600",maxW:"3xl",textAlign:"center",mb:{base:4,md:6},px:{base:2,md:0},lineHeight:"1.6",children:e("faq_description","Find answers to commonly asked questions about FunBlocks AI tools and how they can help you.")}),(0,a.jsxs)(b.M,{columns:{base:1,md:2},spacing:{base:6,md:8},width:"100%",maxW:"5xl",children:[(0,a.jsxs)(o.x,{width:"100%",children:[(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},mb:{base:3,md:4},color:"blue.600",px:{base:1,md:0},children:e("faq_category_general","About FunBlocks AI")}),(0,a.jsx)(y.U,{allowMultiple:!0,children:[l[0],l[1],l[2],l[8],l[9]].map((e,i)=>(0,a.jsxs)(k.Q,{mb:{base:3,md:4},border:"none",bg:"white",borderRadius:"lg",boxShadow:"md",_hover:{boxShadow:"lg"},transition:"all 0.3s",children:[(0,a.jsxs)(v.K,{pt:{base:3,md:3},pb:{base:3,md:3},px:{base:3,md:4},_hover:{bg:"transparent"},borderRadius:"lg",role:"group",children:[(0,a.jsx)(o.x,{flex:"1",textAlign:"left",children:(0,a.jsx)(g.X,{size:{base:"xs",md:"sm"},color:"blue.600",fontWeight:"semibold",lineHeight:"1.4",_groupHover:{color:"blue.700"},children:e.question})}),(0,a.jsx)(I.X,{color:"blue.500",_groupHover:{transform:"rotate(180deg)"},transition:"transform 0.2s"})]}),(0,a.jsx)(S.H,{pb:{base:4,md:6},px:{base:3,md:6},pt:{base:1,md:2},children:(0,a.jsx)(m.x,{color:"gray.600",whiteSpace:"pre-wrap",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:e.answer})})]},i))})]}),(0,a.jsxs)(o.x,{width:"100%",children:[(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},mb:{base:3,md:4},color:"purple.600",px:{base:1,md:0},children:e("faq_category_features","Tools & Features")}),(0,a.jsx)(y.U,{allowMultiple:!0,children:[l[3],l[4],l[5],l[6],l[7]].map((e,i)=>(0,a.jsxs)(k.Q,{mb:{base:3,md:4},border:"none",bg:"white",borderRadius:"lg",boxShadow:"md",_hover:{boxShadow:"lg"},transition:"all 0.3s",children:[(0,a.jsxs)(v.K,{pt:{base:3,md:3},pb:{base:3,md:3},px:{base:3,md:4},_hover:{bg:"transparent"},borderRadius:"lg",role:"group",children:[(0,a.jsx)(o.x,{flex:"1",textAlign:"left",children:(0,a.jsx)(g.X,{size:{base:"xs",md:"sm"},color:"purple.600",fontWeight:"semibold",lineHeight:"1.4",_groupHover:{color:"purple.700"},children:e.question})}),(0,a.jsx)(I.X,{color:"purple.500",_groupHover:{transform:"rotate(180deg)"},transition:"transform 0.2s"})]}),(0,a.jsx)(S.H,{pb:{base:4,md:6},px:{base:3,md:6},pt:{base:1,md:2},children:(0,a.jsx)(m.x,{color:"gray.600",whiteSpace:"pre-wrap",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:e.answer})})]},i))})]})]}),(0,a.jsxs)(b.M,{columns:{base:1,md:2},spacing:{base:6,md:8},width:"100%",maxW:"5xl",mt:{base:4,md:8},children:[(0,a.jsxs)(o.x,{width:"100%",children:[(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},mb:{base:3,md:4},color:"green.600",px:{base:1,md:0},children:e("faq_category_usage","Use Cases & Applications")}),(0,a.jsx)(y.U,{allowMultiple:!0,children:[l[10],l[11],l[12],l[13],l[14]].map((e,i)=>(0,a.jsxs)(k.Q,{mb:{base:3,md:4},border:"none",bg:"white",borderRadius:"lg",boxShadow:"md",_hover:{boxShadow:"lg"},transition:"all 0.3s",children:[(0,a.jsxs)(v.K,{pt:{base:3,md:3},pb:{base:3,md:3},px:{base:3,md:4},_hover:{bg:"transparent"},borderRadius:"lg",role:"group",children:[(0,a.jsx)(o.x,{flex:"1",textAlign:"left",children:(0,a.jsx)(g.X,{size:{base:"xs",md:"sm"},color:"green.600",fontWeight:"semibold",lineHeight:"1.4",_groupHover:{color:"green.700"},children:e.question})}),(0,a.jsx)(I.X,{color:"green.500",_groupHover:{transform:"rotate(180deg)"},transition:"transform 0.2s"})]}),(0,a.jsx)(S.H,{pb:{base:4,md:6},px:{base:3,md:6},pt:{base:1,md:2},children:(0,a.jsx)(m.x,{color:"gray.600",whiteSpace:"pre-wrap",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:e.answer})})]},i))})]}),(0,a.jsxs)(o.x,{width:"100%",children:[(0,a.jsx)(g.X,{size:{base:"sm",md:"md"},mb:{base:3,md:4},color:"orange.600",px:{base:1,md:0},children:e("faq_category_technical","Technical & Support")}),(0,a.jsxs)(d.g,{spacing:{base:3,md:4},align:"start",bg:"orange.50",p:{base:4,md:6},borderRadius:"lg",borderLeft:"4px solid",borderColor:"orange.400",children:[(0,a.jsx)(g.X,{size:{base:"xs",md:"sm"},color:"orange.600",lineHeight:"1.4",children:e("faq_need_more_help","Need More Help?")}),(0,a.jsx)(m.x,{color:"gray.600",fontSize:{base:"sm",md:"md"},lineHeight:"1.6",children:e("faq_support_text","If you have questions not covered here, our support team is ready to help. You can also check our detailed documentation for more information.")}),(0,a.jsxs)(s.k,{gap:{base:3,md:4},flexWrap:{base:"wrap",sm:"nowrap"},width:"100%",children:[(0,a.jsx)(p.z,{leftIcon:(0,a.jsx)(A.MXt,{}),colorScheme:"orange",variant:"outline",size:{base:"sm",md:"md"},as:r.r,href:"https://discord.gg/XtdZFBy4uR",isExternal:!0,width:{base:"100%",sm:"auto"},children:e("contact_support","Contact Support")}),(0,a.jsx)(p.z,{leftIcon:(0,a.jsx)(A.Mp$,{}),colorScheme:"blue",variant:"outline",size:{base:"sm",md:"md"},as:r.r,href:"https://www.funblocks.net/aiflow",isExternal:!0,width:{base:"100%",sm:"auto"},children:e("view_documentation","View Documentation")})]})]})]})]})]})}),(0,a.jsx)(W.Z,{})]})})]})};i.Z=V}}]);