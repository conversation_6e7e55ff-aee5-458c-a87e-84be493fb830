{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/api/getSvg": "pages/api/getSvg.js", "/api/saveSvg": "pages/api/saveSvg.js", "/art": "pages/art.js", "/avatar": "pages/avatar.js", "/bias": "pages/bias.js", "/bloom": "pages/bloom.js", "/brainstorming": "pages/brainstorming.js", "/businessmodel": "pages/businessmodel.js", "/counselor": "pages/counselor.js", "/collections/[...slug]": "pages/collections/[...slug].js", "/critical-thinking": "pages/critical-thinking.js", "/decision": "pages/decision.js", "/dok": "pages/dok.js", "/dok-assessment": "pages/dok-assessment.js", "/erase": "pages/erase.js", "/dreamlens": "pages/dreamlens.js", "/graphics": "pages/graphics.js", "/feynman": "pages/feynman.js", "/horoscope": "pages/horoscope.js", "/": "pages/index.js", "/infographic": "pages/infographic.js", "/layered-explanation": "pages/layered-explanation.js", "/insightcards": "pages/insightcards.js", "/lesson-plans": "pages/lesson-plans.js", "/image-editor": "pages/image-editor.js", "/marzano": "pages/marzano.js", "/mindmap": "pages/mindmap.js", "/mindkit": "pages/mindkit.js", "/mindkit/[mental_model]": "pages/mindkit/[mental_model].js", "/movie": "pages/movie.js", "/mindsnap/[mental_model]": "pages/mindsnap/[mental_model].js", "/mindsnap": "pages/mindsnap.js", "/my": "pages/my.js", "/_document": "pages/_document.js", "/okr": "pages/okr.js", "/one-page-slide": "pages/one-page-slide.js", "/planner": "pages/planner.js", "/photo": "pages/photo.js", "/poetic": "pages/poetic.js", "/reading": "pages/reading.js", "/refine-question": "pages/refine-question.js", "/prompt-optimizer": "pages/prompt-optimizer.js", "/reflection": "pages/reflection.js", "/sketch": "pages/sketch.js", "/slides": "pages/slides.js", "/solo": "pages/solo.js", "/teaching-slides": "pages/teaching-slides.js", "/startupmentor": "pages/startupmentor.js", "/test-mental-model": "pages/test-mental-model.js", "/youtube": "pages/youtube.js", "/share/[...slug]": "pages/share/[...slug].js", "/en/404": "pages/en/404.html", "/zh/404": "pages/zh/404.html", "/en/500": "pages/en/500.html", "/zh/500": "pages/zh/500.html"}