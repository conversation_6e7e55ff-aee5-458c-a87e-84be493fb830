"use strict";
exports.id = 4743;
exports.ids = [4743];
exports.modules = {

/***/ 3598:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "k": () => (/* binding */ GoogleLogin)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6999);
/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2210);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7597);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1951);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1301);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_6__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_icons_fa__WEBPACK_IMPORTED_MODULE_5__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_icons_fa__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);








const GoogleLoginComponent = ({ inviteCode , showButton  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)("common");
    const { logon  } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__/* .useAuth */ .a)();
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();
    const login = (0,_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__.useGoogleLogin)({
        onSuccess: async (tokenResponse)=>{
            try {
                const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_7__/* .apiUrl */ .J}/users/oauth-sign-in`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        tokenInfo: tokenResponse,
                        inviteCode
                    }),
                    credentials: "include"
                });
                if (response.ok) {
                    const result = await response.json();
                    console.log(result, result?.data);
                    logon(result?.data);
                    toast({
                        title: t("login_success"),
                        status: "success",
                        duration: 3000,
                        isClosable: true
                    });
                } else {
                    console.error("Failed to login");
                    toast({
                        title: t("login_failed"),
                        status: "error",
                        duration: 3000,
                        isClosable: true
                    });
                }
            } catch (error) {
                console.error("Error login:", error);
                toast({
                    title: t("login_error"),
                    description: error.message,
                    status: "error",
                    duration: 3000,
                    isClosable: true
                });
            }
        }
    });
    (0,_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__.useGoogleOneTapLogin)({
        onSuccess: async (credentialResponse)=>{
            try {
                const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_7__/* .apiUrl */ .J}/users/oauth-sign-in-credential`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        credential: credentialResponse.credential
                    }),
                    credentials: "include"
                });
                if (response.ok) {
                    const result = await response.json();
                    console.log(result, result?.data);
                    logon(result?.data);
                    toast({
                        title: t("login_success"),
                        status: "success",
                        duration: 3000,
                        isClosable: true
                    });
                } else {
                    console.error("Failed to login");
                    toast({
                        title: t("login_failed"),
                        status: "error",
                        duration: 3000,
                        isClosable: true
                    });
                }
            } catch (error) {
                console.error("Error login:", error);
                toast({
                    title: t("login_error"),
                    description: error.message,
                    status: "error",
                    duration: 3000,
                    isClosable: true
                });
            }
        },
        onError: ()=>{
            console.log("Login Failed");
        }
    });
    let loginButton = showButton ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
        width: "100%",
        onClick: login,
        justifyContent: "flex-start",
        spacing: 4,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaGoogle, {
                style: {
                    padding: 4,
                    marginRight: 6
                },
                size: 32,
                color: "dodgerblue"
            }),
            " ",
            t("login_with_google")
        ]
    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});
    return loginButton;
};
const GoogleLogin = ({ inviteCode , showButton  })=>{
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__.GoogleOAuthProvider, {
        clientId: "988058218123-enpfsi0n6fo9jqa2aqfr6s37t16loth8.apps.googleusercontent.com",
        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(GoogleLoginComponent, {
            inviteCode: inviteCode,
            showButton: showButton
        })
    });
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 9515:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2210);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);
_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];



const LocaleSelector = ()=>{
    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();
    const changeLanguage = (e)=>{
        const locale = e.target.value;
        router.push(router.pathname, router.asPath, {
            locale
        });
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Select, {
        onChange: changeLanguage,
        value: router.locale,
        size: "sm",
        width: "auto",
        variant: "filled",
        borderRadius: "full",
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("option", {
                value: "en",
                children: "English"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("option", {
                value: "zh",
                children: "中文"
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LocaleSelector);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;