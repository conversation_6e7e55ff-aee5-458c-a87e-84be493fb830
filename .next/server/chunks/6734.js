"use strict";
exports.id = 6734;
exports.ids = [6734];
exports.modules = {

/***/ 8072:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3620);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _ArtifactDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(5655);
/* harmony import */ var _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1082);
/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5744);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(7597);
/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1951);
/* harmony import */ var _Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(283);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(7426);
/* harmony import */ var _styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(3665);
/* harmony import */ var _styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(3770);
/* harmony import */ var _styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var _styled_icons_fluentui_system_regular__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(7636);
/* harmony import */ var _styled_icons_fluentui_system_regular__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_fluentui_system_regular__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var _ShareModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(1692);
/* harmony import */ var _utils_svgUtils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(9399);
/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(3135);
/* harmony import */ var _styled_icons_bootstrap_Magic__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(7817);
/* harmony import */ var _styled_icons_bootstrap_Magic__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap_Magic__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var _styled_icons_material_Lightbulb__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(8953);
/* harmony import */ var _styled_icons_material_Lightbulb__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material_Lightbulb__WEBPACK_IMPORTED_MODULE_17__);
/* harmony import */ var _UserInputModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(5157);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _ArtifactDisplay__WEBPACK_IMPORTED_MODULE_5__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__, _Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_9__, _ShareModal__WEBPACK_IMPORTED_MODULE_14__, react_markdown__WEBPACK_IMPORTED_MODULE_15__, _UserInputModal__WEBPACK_IMPORTED_MODULE_18__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _ArtifactDisplay__WEBPACK_IMPORTED_MODULE_5__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__, _Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_9__, _ShareModal__WEBPACK_IMPORTED_MODULE_14__, react_markdown__WEBPACK_IMPORTED_MODULE_15__, _UserInputModal__WEBPACK_IMPORTED_MODULE_18__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);














// import { Clipboard, ClipboardCheck } from '@styled-icons/bootstrap';







const ArtifactCard = ({ data , setData , onDelete , showDeleteButton =false , app , mode , onAIAction  })=>{
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast)();
    const { openShareModal  } = (0,_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__/* .useShareModal */ .K)();
    const svgRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)("common");
    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data?.isLiked || false);
    const { user  } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__/* .useAuth */ .a)();
    const [doc, setDoc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // 添加加载状态
    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useBreakpointValue)({
        base: true,
        md: false
    });
    const [imgGenerated, setImgGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const [userInputModalVisible, setUserInputModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [aiActionLoading, setAiActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // AI action 加载状态
    const handleDelete = async ()=>{
        try {
            const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_19__/* .apiUrl */ .J}/ai/updateArtifact`, {
                method: "POST",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    _id: data._id,
                    app,
                    mode,
                    data: {
                        folder: 1
                    }
                })
            });
            if (response.ok) {
                onDelete(data._id);
                toast({
                    title: "data deleted",
                    status: "success",
                    duration: 3000,
                    isClosable: true
                });
            } else {
                throw new Error("Failed to delete card");
            }
        } catch (error) {
            console.error("Error deleting card:", error);
            toast({
                title: "Error deleting card",
                status: "error",
                duration: 3000,
                isClosable: true
            });
        }
    };
    const handleLike = async ()=>{
        try {
            const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_19__/* .apiUrl */ .J}/ai/likeArtifact`, {
                method: "POST",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    _id: data._id,
                    isLiked: !isLiked
                })
            });
            if (response.ok) {
                setIsLiked(!isLiked);
                toast({
                    title: isLiked ? "Removed from favorites" : "Added to favorites",
                    status: "success",
                    duration: 2000,
                    isClosable: true
                });
            } else {
                throw new Error("Failed to update like status");
            }
        } catch (error) {
            console.error("Error updating like status:", error);
            toast({
                title: "Error updating like status",
                status: "error",
                duration: 3000,
                isClosable: true
            });
        }
    };
    // AI 菜单项点击处理方法
    const handleAIAction = async (action, userInput = "")=>{
        if (action === "improve_codes" && !userInput) {
            // 如果是优化操作且没有用户输入，显示输入弹窗
            setUserInputModalVisible(true);
            return;
        }
        if (onAIAction) {
            setAiActionLoading(true); // 开始加载
            try {
                await onAIAction(action, data, userInput);
            } catch (error) {
                console.error("AI action error:", error);
            } finally{
                setAiActionLoading(false); // 结束加载
            }
        }
    };
    const handleSaveSVG = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        if (data?.type != "svg") return;
        const svgContent = (0,_utils_svgUtils__WEBPACK_IMPORTED_MODULE_20__/* .preprocessSvg */ .w)(data.content);
        const blob = new Blob([
            svgContent
        ], {
            type: "image/svg+xml"
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = "artifact.svg";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, [
        data
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        setIsLiked(data?.isLiked);
    }, [
        data
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (data.type === "flow") {
            setDoc(data);
        }
    }, [
        data
    ]);
    if (data?.err) {
        return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
            spacing: 3,
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                    children: data.err === "exceed_msg_limit" && t("upgrade_to_vip_msg") || data.err
                }),
                data.err === "exceed_msg_limit" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {
                    "aria-label": t("upgrade_to_vip"),
                    colorScheme: "blue",
                    size: "sm",
                    onClick: ()=>{
                        window.open("https://app.funblocks.net/#/aiplans", "_blank");
                    },
                    children: t("upgrade_to_vip")
                })
            ]
        });
    }
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
        borderWidth: 1,
        borderRadius: "lg",
        overflow: "hidden",
        children: [
            data && (mode === "imageInsights" || [
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.graphics */ .IF.graphics,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.onePageSlides */ .IF.onePageSlides,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.infographic */ .IF.infographic,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.mindsnap */ .IF.mindsnap,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.insightcards */ .IF.insightcards
            ].includes(app)) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_ArtifactDisplay__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                // src={card.content}
                // id={card._id}
                app: app,
                artifact: data,
                svgRef: svgRef
            }),
            data && (data.type === "flow" || [
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.mindmap */ .IF.mindmap,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.reading */ .IF.reading,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.movie */ .IF.movie,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.feynman */ .IF.feynman,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.bloom */ .IF.bloom,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.solo */ .IF.solo,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.dok */ .IF.dok,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.marzano */ .IF.marzano,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.layeredExplanation */ .IF.layeredExplanation,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.criticalThinking */ .IF.criticalThinking,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.reflection */ .IF.reflection,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.refineQuestion */ .IF.refineQuestion,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.bias */ .IF.bias,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.brainstorming */ .IF.brainstorming,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.mindkit */ .IF.mindkit,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.businessmodel */ .IF.businessmodel,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.startupmentor */ .IF.startupmentor,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.okr */ .IF.okr,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.decision */ .IF.decision,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.planner */ .IF.planner,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.youtube */ .IF.youtube,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.counselor */ .IF.counselor,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.dreamlens */ .IF.dreamlens,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.horoscope */ .IF.horoscope,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.art */ .IF.art,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.photo */ .IF.photo,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.erase */ .IF.erase,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.avatar */ .IF.avatar,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.imageEditor */ .IF.imageEditor,
                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.sketch */ .IF.sketch
            ].includes(app)) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                style: {
                    width: "100%",
                    height: isMobile ? imgGenerated ? undefined : 300 : 600
                },
                ref: svgRef,
                align: "left",
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                    app: app,
                    mode: mode,
                    doc: doc,
                    imgGenerated: imgGenerated,
                    setImgGenerated: setImgGenerated,
                    content: data.content,
                    mimeContents: data.mimeContents,
                    context: data.context,
                    ai_action: data.action,
                    onDocSaved: (doc)=>{
                        setDoc(doc);
                        doc && setData((d)=>{
                            return {
                                ...d,
                                _id: doc._id
                            };
                        });
                    }
                })
            }),
            (app === _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.slides */ .IF.slides || data.type === "slides") && data?.hid && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                ref: svgRef,
                style: {
                    width: data?.hid ? "100%" : 1,
                    height: data?.hid ? 540 : 1
                },
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("iframe", {
                    className: "nodrag",
                    id: "slides-frame",
                    style: {
                        width: "100%",
                        height: "100%"
                    },
                    // ref={iframeRef}
                    src: `https://service.funblocks.net/present.html?theme=sky&hid=${data.hid || ""}`,
                    title: "FunBlocks AI Slides"
                })
            }),
            data?.type === "markdown" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
                p: 4,
                textAlign: "left",
                ref: svgRef,
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_markdown__WEBPACK_IMPORTED_MODULE_15__["default"], {
                    children: data.content
                })
            }),
            data?.err && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                children: data.err
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Flex, {
                justifyContent: "space-between",
                p: 1,
                bg: "gray.100",
                width: "100%",
                alignItems: "center",
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                        spacing: 1,
                        fontSize: "sm",
                        color: "gray.500",
                        marginLeft: 1,
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                style: {
                                    maxWidth: "300px",
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis"
                                },
                                children: data?.promptLabel || data?.title || ""
                            }),
                            doc?.type === "flow" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("to_aiflow_tips"),
                                "aria-label": "Share tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {
                                    "aria-label": t("to_aiflow_tips"),
                                    color: "dodgerblue",
                                    size: "sm",
                                    onClick: ()=>window.open("https://app.funblocks.net/#/aiflow?hid=" + doc.hid, "_blank"),
                                    children: t("to_aiflow")
                                })
                            }),
                            data?.type === "slides" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_11__.PlayBtn, {
                                    size: 24
                                }),
                                "aria-label": "presentation",
                                color: "gray.500",
                                size: "sm",
                                onClick: ()=>window.open(`https://service.funblocks.net/present.html?theme=sky&hid=${data.hid}`, "_blank")
                            }),
                            [
                                "slides",
                                "markdown"
                            ].includes(data?.type) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_12__.Edit, {
                                    size: 20
                                }),
                                "aria-label": "edit",
                                color: "gray.500",
                                size: "sm",
                                onClick: ()=>{
                                    if (data?.type === "slides") {
                                        window.open(`https://app.funblocks.net/#/slidesEditor?hid=${data.hid}`, "_blank");
                                    } else if (data?.type === "markdown") {
                                        window.open(`https://app.funblocks.net/#/editor?hid=${data.hid}`, "_blank");
                                    }
                                }
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                        spacing: 1,
                        children: [
                            onAIAction && [
                                "mermaid",
                                "svg"
                            ].includes(data.type) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("ai_actions"),
                                "aria-label": "AI Actions tooltip",
                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Menu, {
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuButton, {
                                            as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton,
                                            icon: aiActionLoading ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {
                                                size: "sm"
                                            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap_Magic__WEBPACK_IMPORTED_MODULE_16__.Magic, {
                                                size: 20
                                            }),
                                            "aria-label": t("ai_actions"),
                                            color: "dodgerblue",
                                            size: "sm",
                                            variant: "ghost",
                                            isLoading: aiActionLoading,
                                            disabled: aiActionLoading
                                        }),
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuList, {
                                            children: [
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {
                                                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material_Lightbulb__WEBPACK_IMPORTED_MODULE_17__.Lightbulb, {
                                                        size: 16,
                                                        color: "dodgerblue"
                                                    }),
                                                    onClick: ()=>handleAIAction("fix_codes_bug"),
                                                    isDisabled: aiActionLoading,
                                                    children: t("fix_codes_bug")
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.MenuItem, {
                                                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap_Magic__WEBPACK_IMPORTED_MODULE_16__.Magic, {
                                                        size: 16,
                                                        color: "dodgerblue"
                                                    }),
                                                    onClick: ()=>handleAIAction("improve_codes"),
                                                    isDisabled: aiActionLoading,
                                                    children: t("improve_codes")
                                                })
                                            ]
                                        })
                                    ]
                                })
                            }),
                            ![
                                _utils_constants__WEBPACK_IMPORTED_MODULE_10__/* .APP_TYPE.mindmap */ .IF.mindmap
                            ].includes(app) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: isLiked ? t("unlike") : t(user?._id === data?.userId ? "share_to_showcase" : "like"),
                                "aria-label": "Like tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: isLiked ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Favorite, {
                                        size: 20
                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.FavoriteBorder, {
                                        size: 20
                                    }),
                                    "aria-label": isLiked ? t("unlike") : t("like"),
                                    color: isLiked ? "red.500" : "gray.500",
                                    size: "sm",
                                    onClick: handleLike
                                })
                            }),
                            data?.type === "svg" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("save_svg"),
                                placement: "bottom",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Save, {
                                        size: 20
                                    }),
                                    "aria-label": t("save_svg"),
                                    color: "gray.500",
                                    size: "sm",
                                    onClick: ()=>handleSaveSVG()
                                })
                            }),
                            ![
                                "slides"
                            ].includes(data?.type) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("download"),
                                "aria-label": "Download tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: isLoading === "download" ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {
                                        size: "sm"
                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Download, {
                                        size: 20
                                    }),
                                    "aria-label": t("download"),
                                    color: "gray.500",
                                    size: "sm",
                                    onClick: async ()=>{
                                        setIsLoading("download"); // 开始加载
                                        try {
                                            await (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_7__/* .handleDownload */ .gp)(svgRef, toast);
                                        } finally{
                                            setIsLoading(null); // 结束加载
                                        }
                                    },
                                    isLoading: isLoading === "download"
                                })
                            }),
                            data?.content && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t(copied ? "copied" : "copy"),
                                "aria-label": "Share tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: copied ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_fluentui_system_regular__WEBPACK_IMPORTED_MODULE_13__.ClipboardCheckmark, {
                                        color: "green",
                                        size: 20
                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_fluentui_system_regular__WEBPACK_IMPORTED_MODULE_13__.Clipboard, {
                                        size: 20
                                    }),
                                    "aria-label": t("share"),
                                    color: "gray.500",
                                    size: "sm",
                                    onClick: ()=>{
                                        navigator.clipboard.writeText(data.content);
                                        setCopied(true);
                                        setTimeout(()=>setCopied(false), 2000);
                                    }
                                })
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("share"),
                                "aria-label": "Share tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: isLoading === "share" ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Spinner, {
                                        size: "sm"
                                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Share, {
                                        size: 20
                                    }),
                                    "aria-label": t("share"),
                                    color: "gray.500",
                                    size: "sm",
                                    onClick: async ()=>{
                                        setIsLoading("share"); // 开始加载
                                        try {
                                            await (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_7__/* .handleShare */ .Bf)(app, data?._id, data?.title, svgRef, openShareModal, toast);
                                        } finally{
                                            setIsLoading(null); // 结束加载
                                        }
                                    },
                                    isLoading: isLoading === "share"
                                })
                            }),
                            showDeleteButton && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                label: t("delete"),
                                "aria-label": "Delete tooltip",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                    icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Delete, {
                                        size: 20
                                    }),
                                    "aria-label": t("delete"),
                                    color: "gray.500",
                                    size: "sm",
                                    onClick: handleDelete
                                })
                            })
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_UserInputModal__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
                visible: userInputModalVisible,
                title: t("improve_codes"),
                placeholder: t("please_enter_your_requirements"),
                onConfirm: (userInput)=>{
                    setUserInputModalVisible(false);
                    handleAIAction("improve_codes", userInput);
                },
                onCancel: ()=>setUserInputModalVisible(false)
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ArtifactCard);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 6734:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4009);
/* harmony import */ var _emotion_styled__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(4115);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7597);
/* harmony import */ var _ArtifactCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(8072);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7426);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_7__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__, _emotion_styled__WEBPACK_IMPORTED_MODULE_4__, _ArtifactCard__WEBPACK_IMPORTED_MODULE_5__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__, _emotion_styled__WEBPACK_IMPORTED_MODULE_4__, _ArtifactCard__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);









// 修改 WaterfallGrid 组件以使用 CSS columns
const WaterfallGrid = (0,_emotion_styled__WEBPACK_IMPORTED_MODULE_4__["default"])(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box)`
  column-count: 2;
  column-gap: 16px;
  
  @media (max-width: 768px) {
    column-count: 1;
  }
`;
// 修改 CardWrapper 以确保卡片不会被分割到多列
const CardWrapper = (0,_emotion_styled__WEBPACK_IMPORTED_MODULE_4__["default"])(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box)`
  break-inside: avoid;
  margin-bottom: 16px;
`;
// 添加一个新的 styled 组件用于展示列表
const ListContainer = (0,_emotion_styled__WEBPACK_IMPORTED_MODULE_4__["default"])(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack)`
  width: 100%;
  align-items: stretch;
  gap: 36px;
`;
const ShowcaseContainer = (0,_emotion_styled__WEBPACK_IMPORTED_MODULE_4__["default"])(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box)`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;
const ShowcaseItem = (0,_emotion_styled__WEBPACK_IMPORTED_MODULE_4__["default"])(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box)`
  padding: 5px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f7fafc;
    transform: translateY(-1px);
  }
`;
const Artifacts = ({ app ="" , mode , collection , initial_items =[]  })=>{
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_7___default()().publicRuntimeConfig;
    const [cards, setCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initial_items);
    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!!initial_items?.length ? 1 : 0);
    const pageSize = app === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.graphics */ .IF.graphics && 10 || collection === "collection" && 500 || !app && 10 || 20;
    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!!initial_items?.length ? initial_items.length === pageSize : true);
    const [ref, inView] = (0,react_intersection_observer__WEBPACK_IMPORTED_MODULE_3__.useInView)({
        threshold: 0
    });
    const fetchCards = async ()=>{
        try {
            const service = app === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.graphics */ .IF.graphics && "aiinsights" || "";
            const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_8__/* .apiUrl */ .J}/ai/${collection === "collection" ? "showcase" : collection}_artifacts?app=${app}&mode=${mode || ""}&service=${service}&pageNum=${page}&pageSize=${pageSize}`, {
                credentials: "include"
            });
            const data = await response.json();
            if (data?.data?.length) {
                setCards((prevCards)=>[
                        ...prevCards,
                        ...data?.data || []
                    ]);
                setPage((prevPage)=>prevPage + 1);
            }
            setHasMore(data?.data?.length == pageSize);
        } catch (error) {
            console.error("Error fetching cards:", error);
        }
    };
    const handleDelete = (cardId)=>{
        setCards((prevCards)=>prevCards.filter((card)=>card._id !== cardId));
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (inView && hasMore && (app === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.graphics */ .IF.graphics || page === 0)) {
            fetchCards();
        }
    }, [
        inView
    ]);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
        width: "100%",
        maxWidth: "900px",
        children: [
            app === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.graphics */ .IF.graphics ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(WaterfallGrid, {
                children: cards.map((card)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(CardWrapper, {
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_ArtifactCard__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                            data: card,
                            app: app,
                            onDelete: handleDelete,
                            showDeleteButton: collection === "my"
                        })
                    }, card._id))
            }) : collection === "my" ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ListContainer, {
                children: cards.map((card)=>{
                    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_ArtifactCard__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
                        data: card,
                        app: card.app,
                        mode: card.mode,
                        onDelete: handleDelete,
                        showDeleteButton: true
                    }, card._id);
                })
            }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ShowcaseContainer, {
                children: cards.map((card)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ShowcaseItem, {
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                            href: `${basePath}/share/${card.app}/${card._id}/${card.title?.replaceAll(" ", "-")}`,
                            target: "_blank",
                            children: card.title || "未命名作品"
                        })
                    }, card._id))
            }),
            // hasMore && (
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
                ref: ref,
                pt: 4,
                pb: 4,
                children: [
                    ![
                        collection
                    ].includes("showcase") && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {
                        onClick: fetchCards,
                        isLoading: inView && (app === _utils_constants__WEBPACK_IMPORTED_MODULE_6__/* .APP_TYPE.graphics */ .IF.graphics || page === 0),
                        children: "Load More"
                    }),
                    [
                        collection
                    ].includes("showcase") && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Link, {
                        color: "dodgerblue",
                        href: `${basePath}/collections/${app}`,
                        isExternal: true,
                        children: "More"
                    })
                ]
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Artifacts);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5157:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2210);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__]);
_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];




const UserInputModal = ({ visible , title , placeholder , onConfirm , onCancel  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)("common");
    const [userInput, setUserInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const textInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (visible) {
            setUserInput("");
            // 延迟聚焦，确保模态框已完全渲染
            setTimeout(()=>{
                if (textInputRef.current) {
                    textInputRef.current.focus();
                }
            }, 100);
        }
    }, [
        visible
    ]);
    const handleConfirm = ()=>{
        if (userInput.trim()) {
            onConfirm(userInput.trim());
        }
        handleClose();
    };
    const handleClose = ()=>{
        setUserInput("");
        onCancel();
    };
    const handleKeyDown = (e)=>{
        if (e.key === "Escape") {
            handleClose();
        } else if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
            handleConfirm();
        }
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {
        isOpen: visible,
        onClose: handleClose,
        size: "lg",
        isCentered: true,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {}),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {
                        children: title || t("user_input")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {
                            spacing: 4,
                            align: "stretch",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {
                                    ref: textInputRef,
                                    value: userInput,
                                    onChange: (e)=>setUserInput(e.target.value),
                                    placeholder: placeholder || t("please_enter_your_requirements"),
                                    rows: 6,
                                    resize: "vertical",
                                    onKeyDown: handleKeyDown
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {
                                    fontSize: "sm",
                                    color: "gray.500",
                                    children: t("input_hint")
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalFooter, {
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
                                variant: "outline",
                                mr: 3,
                                onClick: handleClose,
                                children: t("cancel")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
                                colorScheme: "blue",
                                onClick: handleConfirm,
                                isDisabled: !userInput.trim(),
                                children: t("confirm")
                            })
                        ]
                    })
                ]
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserInputModal);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;