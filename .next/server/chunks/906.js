"use strict";
exports.id = 906;
exports.ids = [906];
exports.modules = {

/***/ 906:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "C": () => (/* binding */ aiTools),
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2210);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1301);
/* harmony import */ var react_icons_si__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9034);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8534);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6197);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _common_Section__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(1306);
/* harmony import */ var react_icons_md__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(6905);
/* harmony import */ var _common_Footer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(9030);
/* harmony import */ var _common_Testimonials__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(966);
/* harmony import */ var _common_CaseStudies__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(980);
/* harmony import */ var _common_ComparisonTable__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(7157);
/* harmony import */ var _common_ResearchBacked__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(3706);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, react_icons_si__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_7__, _common_Section__WEBPACK_IMPORTED_MODULE_9__, react_icons_md__WEBPACK_IMPORTED_MODULE_10__, _common_Footer__WEBPACK_IMPORTED_MODULE_11__, _common_Testimonials__WEBPACK_IMPORTED_MODULE_12__, _common_CaseStudies__WEBPACK_IMPORTED_MODULE_13__, _common_ComparisonTable__WEBPACK_IMPORTED_MODULE_14__, _common_ResearchBacked__WEBPACK_IMPORTED_MODULE_15__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, react_icons_fa__WEBPACK_IMPORTED_MODULE_3__, react_icons_si__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_7__, _common_Section__WEBPACK_IMPORTED_MODULE_9__, react_icons_md__WEBPACK_IMPORTED_MODULE_10__, _common_Footer__WEBPACK_IMPORTED_MODULE_11__, _common_Testimonials__WEBPACK_IMPORTED_MODULE_12__, _common_CaseStudies__WEBPACK_IMPORTED_MODULE_13__, _common_ComparisonTable__WEBPACK_IMPORTED_MODULE_14__, _common_ResearchBacked__WEBPACK_IMPORTED_MODULE_15__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);
















const aiTools = {
    Mindmap: [
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap,
            title: "AI Mindmap",
            description: "ai_mindmap_desc",
            link: "/mindmap",
            tag: "Mindmap Generator",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLayerGroup,
            title: "AI MindLadder",
            description: "ai_layeredexplanation_desc",
            link: "/layered-explanation",
            tag: "AI Education",
            gradient: "linear(to-r, blue.400, purple.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChalkboardTeacher,
            title: "AI MarzanoBrain",
            description: "ai_marzano_desc",
            link: "/marzano",
            tag: "AI Education",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChalkboardTeacher,
            title: "AI BloomBrain",
            description: "ai_bloom_desc",
            link: "/bloom",
            tag: "AI Education",
            gradient: "linear(to-r, red.400, yellow.500)",
            bgColor: "rgba(255, 204, 204, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChalkboardTeacher,
            title: "AI SOLOBrain",
            description: "ai_solo_desc",
            link: "/solo",
            tag: "AI Education",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChalkboardTeacher,
            title: "AI DOKBrain",
            description: "ai_dok_desc",
            link: "/dok",
            tag: "AI Education",
            gradient: "linear(to-r, cyan.400, blue.400)",
            bgColor: "rgba(149, 235, 245, 0.2)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_md__WEBPACK_IMPORTED_MODULE_10__.MdAssessment,
            title: "AI DOK Assessment",
            description: "ai_dok_assessment_desc",
            link: "/dok-assessment",
            tag: "AI Education",
            gradient: "linear(to-r, purple.400, blue.500)",
            bgColor: "rgba(196, 181, 253, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserTie,
            title: "AI Feynman",
            description: "ai_feynman_desc",
            link: "/feynman",
            tag: "AI Education",
            gradient: "linear(to-r, orange.400, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
            title: "AI Brainstorming",
            description: "ai_brainstorming_desc",
            link: "/brainstorming",
            tag: "Creative Thinking",
            gradient: "linear(to-r, orange.400, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegCompass,
            title: "AI MindKit",
            description: "ai_mindkit_desc",
            link: "/mindkit",
            tag: "Creative Thinking",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaYoutube,
            title: "AI Youtube Summarizer",
            description: "ai_youtube_desc",
            link: "/youtube",
            tag: "Mindmap Generator",
            gradient: "linear(to-r, red.400, yellow.500)",
            bgColor: "rgba(255, 204, 204, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullseye,
            title: "AI Critical Analysis",
            description: "ai_criticalthinking_desc",
            link: "/critical-thinking",
            tag: "Critical Thinking",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaQuestionCircle,
            title: "AI Question Craft",
            description: "ai_refinequestion_desc",
            link: "/refine-question",
            tag: "Critical Thinking",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullseye,
            title: "AI LogicLens",
            description: "ai_bias_desc",
            link: "/bias",
            tag: "Critical Thinking",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_md__WEBPACK_IMPORTED_MODULE_10__.MdPsychology,
            title: "AI Reflection",
            description: "ai_reflection_desc",
            link: "/reflection",
            tag: "Critical Thinking",
            gradient: "linear(to-r, orange.500, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBalanceScale,
            title: "AI Decision Analyzer",
            description: "ai_decision_desc",
            link: "/decision",
            tag: "Critical Thinking",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullseye,
            title: "AI OKR Assistant",
            description: "ai_okr_desc",
            link: "/okr",
            tag: "Business Insights",
            gradient: "linear(to-r, orange.500, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserTie,
            title: "AI Startup Mentor",
            description: "ai_startupmentor_desc",
            link: "/startupmentor",
            tag: "Business Insights",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaAnchor,
            title: "AI Business Model Analyzer",
            description: "ai_businessmodel_desc",
            link: "/businessmodel",
            tag: "Business Insights",
            gradient: "linear(to-r, cyan.400, blue.400)",
            bgColor: "rgba(149, 235, 245, 0.2)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTasks,
            title: "AI Task Planner",
            description: "ai_planner_desc",
            link: "/planner",
            tag: "Business Insights",
            gradient: "linear(to-r, yellow.400, orange.500)",
            bgColor: "rgba(255, 236, 179, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart,
            title: "AI Counselor",
            description: "ai_counselor_desc",
            link: "/counselor",
            tag: "Psychological Insights",
            gradient: "linear(to-r, cyan.400, blue.400)",
            bgColor: "rgba(169, 235, 245, 0.2)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaMoon,
            title: "AI DreamLens",
            description: "ai_dreamlens_desc",
            link: "/dreamlens",
            tag: "Psychological Insights",
            gradient: "linear(to-r, orange.500, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaStar,
            title: "AI Horoscope",
            description: "ai_horoscope_desc",
            link: "/horoscope",
            tag: "Psychological Insights",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage,
            title: "AI Art Insight",
            description: "ai_art_desc",
            link: "/art",
            tag: "Image Insights",
            gradient: "linear(to-r, orange.500, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCamera,
            title: "AI Photo Coach",
            description: "ai_photo_desc",
            link: "/photo",
            tag: "Image Insights",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaFeather,
            title: "AI Poetic Lens",
            description: "ai_poetic_desc",
            link: "/poetic",
            tag: "Image Insights",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBook,
            title: "AI Reading Map",
            description: "ai_reading_desc",
            link: "/reading",
            tag: "Mindmap Generator",
            gradient: "linear(to-r, cyan.400, blue.400)",
            bgColor: "rgba(149, 235, 245, 0.2)",
            isLaunched: true
        },
        {
            category: "Mindmap",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaFilm,
            title: "AI CineMap",
            description: "ai_movie_desc",
            link: "/movie",
            tag: "Mindmap Generator",
            gradient: "linear(to-r, red.400, yellow.500)",
            bgColor: "rgba(255, 204, 204, 0.3)",
            isLaunched: true
        }
    ],
    Infographics: [
        {
            category: "Infographics",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartBar,
            title: "AI Graphics",
            description: "ai_graphics_desc",
            link: "/graphics",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Infographics",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegObjectGroup,
            title: "AI Infographic Generator",
            description: "ai_infographic_desc",
            link: "/infographic",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        },
        {
            category: "Infographics",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegCompass,
            title: "AI MindSnap",
            description: "ai_mindsnap_desc",
            link: "/mindsnap",
            gradient: "linear(to-r, orange.400, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Infographics",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegLightbulb,
            title: "AI InsightCards",
            description: "ai_insightcards_desc",
            link: "/insightcards",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(210, 214, 255, 0.3)",
            isLaunched: true
        }
    ],
    Slides: [
        {
            category: "Slides",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDesktop,
            title: "AI PPT/Slides",
            description: "ai_slides_desc",
            link: "/slides",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        },
        {
            category: "Slides",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
            title: "AI SlideGenius",
            description: "ai_onepageslide_desc",
            link: "/one-page-slide",
            gradient: "linear(to-r, orange.400, red.500)",
            bgColor: "rgba(254, 235, 200, 0.3)",
            isLaunched: true
        },
        {
            category: "Slides",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAstronaut,
            title: "AI EduSlides",
            tag: "AI Education",
            description: "ai_teachingslides_desc",
            link: "/teaching-slides",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        }
    ],
    Images: [
        {
            category: "Images",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaPaintBrush,
            title: "AI Sketch",
            description: "ai_sketch_desc",
            link: "/sketch",
            tag: "Creative Drawing",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(237, 242, 247, 0.3)",
            isLaunched: true
        },
        {
            category: "Images",
            icon: react_icons_md__WEBPACK_IMPORTED_MODULE_10__.MdEdit,
            title: "AI Image Editor",
            description: "ai_imageeditor_desc",
            link: "/image-editor",
            gradient: "linear(to-r, purple.400, pink.500)",
            bgColor: "rgba(237, 213, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Images",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAstronaut,
            title: "AI Avatar Generator",
            description: "ai_avatar_desc",
            link: "/avatar",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Images",
            icon: react_icons_md__WEBPACK_IMPORTED_MODULE_10__.MdAutoFixHigh,
            title: "AI Watermark Remover",
            description: "ai_erase_desc",
            link: "/erase",
            gradient: "linear(to-r, green.400, teal.500)",
            bgColor: "rgba(209, 250, 229, 0.3)",
            isLaunched: true
        }
    ],
    Text: [
        {
            category: "Text",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAstronaut,
            title: "AI Prompt Optimizer",
            description: "ai_promptoptimizer_desc",
            link: "/prompt-optimizer",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Text",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAstronaut,
            title: "AI Lesson Plans",
            description: "ai_lessonplans_desc",
            tag: "AI Education",
            link: "/lesson-plans",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        },
        {
            category: "Text",
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAstronaut,
            title: "AI DOK Assessment",
            description: "ai_dokassessment_desc",
            tag: "AI Education",
            link: "/dok-assessment",
            gradient: "linear(to-r, cyan.400, blue.500)",
            bgColor: "rgba(179, 245, 255, 0.3)",
            isLaunched: true
        }
    ]
};
const MotionBox = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box);
const ToolCard = ({ icon , title , description , link , isLaunched , tag , category , isFeatured , isNew  })=>{
    // Determine color scheme based on tag - using more subtle colors
    const getColorScheme = (tag)=>{
        switch(tag){
            case "Mindmap Generator":
                return {
                    color: "blue",
                    shade: "300"
                };
            case "AI Education":
                return {
                    color: "teal",
                    shade: "400"
                };
            case "Creative Thinking":
                return {
                    color: "purple",
                    shade: "500"
                };
            case "Critical Thinking":
                return {
                    color: "cyan",
                    shade: "600"
                };
            case "Business Insights":
                return {
                    color: "green",
                    shade: "600"
                };
            case "Psychological Insights":
                return {
                    color: "pink",
                    shade: "500"
                };
            case "Image Insights":
                return {
                    color: "red",
                    shade: "300"
                };
            default:
                if (category === "Slides") {
                    return {
                        color: "blue",
                        shade: "600"
                    };
                } else if (category === "Infographics") {
                    return {
                        color: "green",
                        shade: "400"
                    };
                }
                return {
                    color: "blue",
                    shade: "500"
                };
        }
    };
    const { color , shade  } = getColorScheme(tag);
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useBreakpointValue)({
        base: true,
        md: false
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(MotionBox, {
        as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
        href: isLaunched && link,
        target: "_blank",
        rel: "noopener noreferrer",
        whileHover: {
            y: -5
        },
        p: 0,
        borderRadius: "lg",
        boxShadow: "md",
        bg: "white",
        position: "relative",
        overflow: "hidden",
        _hover: {
            textDecoration: "none",
            transform: "scale(1.02)",
            boxShadow: "lg"
        },
        transition: "all 0.3s",
        border: "1px solid",
        borderColor: "gray.100",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        role: "group",
        "aria-label": title,
        touchAction: "manipulation",
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                h: "4px",
                bg: `${color}.${shade}`,
                w: "100%",
                opacity: 0.8
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                position: "absolute",
                top: 2,
                right: 2,
                flexDirection: "column",
                alignItems: "flex-end",
                gap: 1,
                zIndex: "1",
                children: tag && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                    bg: `${color}.50`,
                    color: `${color}.${shade}`,
                    borderRadius: "full",
                    px: 2,
                    py: 0.5,
                    fontSize: {
                        base: "2xs",
                        md: "xs"
                    },
                    fontWeight: "medium",
                    children: tag
                })
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                p: {
                    base: 4,
                    md: 5
                },
                pt: {
                    base: 3,
                    md: 4
                },
                display: "flex",
                flexDirection: "column",
                height: "100%",
                minH: {
                    base: "auto",
                    md: "180px"
                },
                flex: "1",
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                        spacing: {
                            base: 2,
                            md: 4
                        },
                        align: "start",
                        flex: "1",
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                width: "100%",
                                direction: {
                                    base: "row",
                                    sm: "row"
                                },
                                align: {
                                    base: "center",
                                    sm: "center"
                                },
                                gap: {
                                    base: 3,
                                    sm: 3
                                },
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                        p: {
                                            base: 2,
                                            md: 2.5
                                        },
                                        borderRadius: "md",
                                        bg: `${color}.50`,
                                        minWidth: {
                                            base: "36px",
                                            md: "auto"
                                        },
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                            as: icon,
                                            w: {
                                                base: 5,
                                                md: 6
                                            },
                                            h: {
                                                base: 5,
                                                md: 6
                                            },
                                            color: `${color}.${shade}`
                                        })
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: {
                                            base: "sm",
                                            md: "md"
                                        },
                                        color: "gray.700",
                                        noOfLines: 2,
                                        lineHeight: "shorter",
                                        children: title
                                    })
                                ]
                            }),
                            description && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                color: "gray.600",
                                fontSize: {
                                    base: "sm",
                                    md: "md"
                                },
                                noOfLines: 3,
                                lineHeight: "1.5",
                                mt: {
                                    base: 1,
                                    md: 2
                                },
                                children: description
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                        rightIcon: isLaunched ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {}) : undefined,
                        variant: "outline",
                        colorScheme: isLaunched ? color : "gray",
                        size: {
                            base: "md",
                            md: "md"
                        },
                        disabled: !isLaunched,
                        mt: {
                            base: 3,
                            md: 4
                        },
                        mb: {
                            base: 0,
                            md: 0
                        },
                        width: "full",
                        height: {
                            base: "40px",
                            md: "40px"
                        },
                        _hover: {
                            transform: "translateX(4px)",
                            bg: `${color}.50`
                        },
                        transition: "all 0.2s",
                        fontSize: {
                            base: "sm",
                            md: "md"
                        },
                        fontWeight: "medium",
                        _groupHover: {
                            bg: `${color}.50`,
                            borderColor: `${color}.400`
                        },
                        children: isLaunched ? t("try_for_free", "Try for Free Now") : t("coming_soon", "Coming soon ...")
                    })
                ]
            })
        ]
    });
};
// Benefits data
const benefits = [
    {
        id: "deep-thinking",
        title: "deep_thinking",
        description: "deep_thinking_desc",
        icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
        color: "blue"
    },
    {
        id: "boosted-creativity",
        title: "boosted_creativity",
        description: "boosted_creativity_desc",
        icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb,
        color: "orange"
    },
    {
        id: "enhanced-productivity",
        title: "enhanced_productivity",
        description: "enhanced_productivity_desc",
        icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRocket,
        color: "green"
    }
];
// Create motion components for animations
const MotionFlex = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex);
const HeroSection = ()=>{
    // Theme colors
    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("gray.50", "gray.900");
    const headingColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("gray.800", "white");
    const subTextColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("gray.600", "gray.300");
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useBreakpointValue)({
        base: true,
        md: false
    });
    const isSmallMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useBreakpointValue)({
        base: true,
        sm: false
    });
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    // Enhanced color scheme
    const colors = {
        mindMap: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("blue.500", "red.400"),
        slides: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("cyan.500", "purple.400"),
        infographics: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("green.500", "green.400"),
        images: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("red.500", "orange.400"),
        text: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("purple.500", "orange.400"),
        ai: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("blue.500", "blue.400"),
        highlight: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("teal.500", "teal.300"),
        cardBg: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("white", "gray.800"),
        cardBorder: (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("gray.200", "gray.700")
    };
    // Animation variants - optimized for mobile
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.4,
                when: "beforeChildren",
                staggerChildren: isMobile ? 0.1 : 0.2
            }
        }
    };
    const itemVariants = {
        hidden: {
            y: 15,
            opacity: 0
        },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: isMobile ? 0.3 : 0.5
            }
        }
    };
    // Category data
    const categories = [
        {
            id: "mindmap",
            title: t("category_mindmap_title"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap,
            color: colors.mindMap,
            link: "Mindmap",
            description: t("category_mindmap_desc")
        },
        {
            id: "infographics",
            title: t("category_infographics_title"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartBar,
            color: colors.infographics,
            link: "Infographics",
            description: t("category_infographics_desc")
        },
        {
            id: "slides",
            title: t("category_slides_title"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDesktop,
            color: colors.slides,
            link: "Slides",
            description: t("category_slides_desc")
        },
        {
            id: "images",
            title: t("category_images_title"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage,
            color: colors.images,
            link: "Images",
            description: t("category_images_desc")
        },
        {
            id: "text",
            title: t("category_text_title"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaNewspaper,
            color: colors.text,
            link: "Text",
            description: t("category_text_desc")
        }
    ];
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
        bg: bgColor,
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                spacing: {
                    base: 4,
                    md: 5
                },
                mb: {
                    base: 8,
                    md: 12
                },
                as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                variants: containerVariants,
                initial: "hidden",
                animate: "visible",
                maxW: "4xl",
                mx: "auto",
                textAlign: "center",
                px: {
                    base: 3,
                    md: 0
                },
                width: "100%",
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                        as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                        variants: itemVariants,
                        display: "flex",
                        justifyContent: "center",
                        width: "100%",
                        gap: 4,
                        flexWrap: {
                            base: "nowrap",
                            sm: "wrap"
                        },
                        alignItems: "center",
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                href: "https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_source=badge-funblocks-ai-brainstorming",
                                target: "_blank",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                                    src: "https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=963998&theme=light&period=daily&t=1747700452719",
                                    alt: "FunBlocks AI Brainstorming - AI-Powered Brainstorming to Ignite Unlimited Creativity | Product Hunt",
                                    style: {
                                        width: "250px",
                                        height: "54px"
                                    },
                                    width: "250",
                                    height: "54"
                                })
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("a", {
                                href: "https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_source=badge-funblocks-ai-brainstorming",
                                target: "_blank",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                                    src: "https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=963998&theme=light&period=weekly&topic_id=204&t=1747700452719",
                                    alt: "FunBlocks AI Brainstorming - AI-Powered Brainstorming to Ignite Unlimited Creativity | Product Hunt",
                                    style: {
                                        width: "250px",
                                        height: "54px"
                                    },
                                    width: "250",
                                    height: "54"
                                })
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                        variants: itemVariants,
                        mb: {
                            base: 1,
                            md: 2
                        },
                        direction: {
                            base: "column",
                            sm: "row"
                        },
                        gap: {
                            base: 2,
                            sm: 2
                        },
                        justify: "center",
                        align: "center",
                        width: "100%",
                        flexWrap: {
                            base: "nowrap",
                            sm: "wrap"
                        },
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                                colorScheme: "teal",
                                fontSize: {
                                    base: "2xs",
                                    sm: "xs",
                                    md: "sm"
                                },
                                px: {
                                    base: 2,
                                    md: 3
                                },
                                py: {
                                    base: 1,
                                    md: 1
                                },
                                borderRadius: "full",
                                textTransform: "uppercase",
                                fontWeight: "medium",
                                children: t("hero_badge_1")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                                colorScheme: "teal",
                                fontSize: {
                                    base: "2xs",
                                    sm: "xs",
                                    md: "sm"
                                },
                                px: {
                                    base: 2,
                                    md: 3
                                },
                                py: {
                                    base: 1,
                                    md: 1
                                },
                                borderRadius: "full",
                                textTransform: "uppercase",
                                fontWeight: "medium",
                                children: t("hero_badge_2")
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                        as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1,
                        variants: itemVariants,
                        size: {
                            base: "lg",
                            sm: "xl",
                            md: "2xl"
                        },
                        color: headingColor,
                        lineHeight: {
                            base: "1.3",
                            md: "1.2"
                        },
                        fontWeight: "bold",
                        mb: {
                            base: 3,
                            md: 4
                        },
                        px: {
                            base: 2,
                            md: 0
                        },
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                as: "span",
                                color: "blue.500",
                                children: t("hero_heading_1")
                            }),
                            " ",
                            t("hero_heading_2"),
                            " ",
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                as: "span",
                                bgGradient: "linear(to-r, blue.400, teal.500)",
                                bgClip: "text",
                                children: t("hero_heading_3")
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                        as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p,
                        variants: itemVariants,
                        fontSize: {
                            base: "sm",
                            sm: "md",
                            md: "xl"
                        },
                        color: subTextColor,
                        maxW: "3xl",
                        mb: {
                            base: 4,
                            md: 6
                        },
                        px: {
                            base: 2,
                            md: 0
                        },
                        lineHeight: {
                            base: "1.6",
                            md: "1.7"
                        },
                        children: [
                            t("platform_description_1"),
                            " ",
                            t("hero_description")
                        ]
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                        variants: itemVariants,
                        mt: {
                            base: 1,
                            md: 2
                        },
                        direction: {
                            base: "column",
                            sm: "row"
                        },
                        width: {
                            base: "100%",
                            sm: "auto"
                        },
                        justify: "center",
                        align: "center",
                        gap: {
                            base: 3,
                            sm: 4
                        },
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                href: "#ai_tools",
                                size: {
                                    base: "md",
                                    md: "lg"
                                },
                                colorScheme: "blue",
                                rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowRight, {}),
                                _hover: {
                                    transform: "translateY(-2px)"
                                },
                                transition: "all 0.3s",
                                px: {
                                    base: 5,
                                    md: 8
                                },
                                py: {
                                    base: 6,
                                    md: 6
                                },
                                borderRadius: "full",
                                width: {
                                    base: "100%",
                                    sm: "auto"
                                },
                                fontSize: {
                                    base: "sm",
                                    md: "md"
                                },
                                fontWeight: "medium",
                                height: {
                                    base: "48px",
                                    md: "auto"
                                },
                                children: t("explore_tools")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                href: "#thinking-benefits-heading",
                                size: {
                                    base: "md",
                                    md: "lg"
                                },
                                variant: "outline",
                                colorScheme: "blue",
                                _hover: {
                                    transform: "translateY(-2px)"
                                },
                                transition: "all 0.3s",
                                px: {
                                    base: 5,
                                    md: 8
                                },
                                py: {
                                    base: 6,
                                    md: 6
                                },
                                borderRadius: "full",
                                width: {
                                    base: "100%",
                                    sm: "auto"
                                },
                                fontSize: {
                                    base: "sm",
                                    md: "md"
                                },
                                fontWeight: "medium",
                                height: {
                                    base: "48px",
                                    md: "auto"
                                },
                                children: t("learn_more")
                            })
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                columns: {
                    base: 1,
                    md: 3
                },
                spacing: {
                    base: 5,
                    md: 8
                },
                mb: {
                    base: 10,
                    md: 16
                },
                as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                variants: containerVariants,
                initial: "hidden",
                animate: "visible",
                px: {
                    base: 3,
                    md: 0
                },
                width: "100%",
                children: benefits.map((benefit, idx)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(MotionBox, {
                        variants: itemVariants,
                        bg: colors.cardBg,
                        p: {
                            base: 5,
                            md: 6
                        },
                        borderRadius: "lg",
                        boxShadow: "md",
                        border: "1px solid",
                        borderColor: colors.cardBorder,
                        transition: "all 0.3s",
                        _hover: {
                            transform: "translateY(-5px)",
                            boxShadow: "lg"
                        },
                        cursor: "pointer",
                        onClick: ()=>{
                            const element = document.getElementById("thinking-benefits-heading");
                            if (element) {
                                element.scrollIntoView({
                                    behavior: "smooth"
                                });
                            }
                        },
                        role: "group",
                        "aria-label": t(benefit.title),
                        touchAction: "manipulation",
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                            direction: {
                                base: "column",
                                sm: "row"
                            },
                            align: {
                                base: "center",
                                sm: "flex-start"
                            },
                            textAlign: {
                                base: "center",
                                sm: "left"
                            },
                            gap: {
                                base: 3,
                                md: 4
                            },
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                    bg: `${benefit.color}.100`,
                                    color: `${benefit.color}.500`,
                                    borderRadius: "md",
                                    p: {
                                        base: 3,
                                        md: 3
                                    },
                                    minW: {
                                        base: "50px",
                                        md: "50px"
                                    },
                                    minH: {
                                        base: "50px",
                                        md: "50px"
                                    },
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    _groupHover: {
                                        bg: `${benefit.color}.200`,
                                        transform: "scale(1.05)"
                                    },
                                    transition: "all 0.2s",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: benefit.icon,
                                        w: {
                                            base: 5,
                                            md: 6
                                        },
                                        h: {
                                            base: 5,
                                            md: 6
                                        }
                                    })
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                    align: {
                                        base: "center",
                                        sm: "start"
                                    },
                                    spacing: {
                                        base: 1,
                                        md: 1
                                    },
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                            size: {
                                                base: "sm",
                                                md: "md"
                                            },
                                            color: headingColor,
                                            lineHeight: "shorter",
                                            children: t(benefit.title)
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                            color: subTextColor,
                                            fontSize: {
                                                base: "sm",
                                                md: "md"
                                            },
                                            lineHeight: "1.5",
                                            children: t(benefit.description).split("\n")[0]
                                        })
                                    ]
                                })
                            ]
                        })
                    }, benefit.id))
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2,
                variants: itemVariants,
                size: {
                    base: "md",
                    md: "lg"
                },
                textAlign: "center",
                mb: {
                    base: 6,
                    md: 8
                },
                color: headingColor,
                px: {
                    base: 3,
                    md: 0
                },
                children: t("discover_categories")
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                columns: {
                    base: 1,
                    sm: 2,
                    lg: 5
                },
                spacing: {
                    base: 5,
                    md: 8
                },
                as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                variants: containerVariants,
                initial: "hidden",
                animate: "visible",
                px: {
                    base: 3,
                    md: 0
                },
                width: "100%",
                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(MotionBox, {
                        variants: itemVariants,
                        bg: colors.cardBg,
                        p: {
                            base: 5,
                            md: 6
                        },
                        borderRadius: "lg",
                        boxShadow: "md",
                        border: "1px solid",
                        borderColor: colors.cardBorder,
                        transition: "all 0.3s",
                        _hover: {
                            transform: "translateY(-5px)",
                            boxShadow: "lg"
                        },
                        onClick: ()=>{
                            const element = document.getElementById(category.link);
                            if (element) {
                                element.scrollIntoView({
                                    behavior: "smooth"
                                });
                            }
                        },
                        cursor: "pointer",
                        textDecoration: "none",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        textAlign: "center",
                        height: "100%",
                        role: "group",
                        "aria-label": category.title,
                        touchAction: "manipulation",
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                bg: `${category.color}`,
                                color: "white",
                                borderRadius: "full",
                                p: {
                                    base: 3,
                                    md: 4
                                },
                                mb: {
                                    base: 3,
                                    md: 4
                                },
                                minW: {
                                    base: "60px",
                                    md: "70px"
                                },
                                minH: {
                                    base: "60px",
                                    md: "70px"
                                },
                                _groupHover: {
                                    transform: "scale(1.05)",
                                    boxShadow: "md"
                                },
                                transition: "all 0.2s",
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                    as: category.icon,
                                    w: {
                                        base: 6,
                                        md: 7
                                    },
                                    h: {
                                        base: 6,
                                        md: 7
                                    }
                                })
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                color: headingColor,
                                mb: {
                                    base: 2,
                                    md: 2
                                },
                                lineHeight: "shorter",
                                children: category.title
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                color: subTextColor,
                                fontSize: {
                                    base: "xs",
                                    md: "sm"
                                },
                                lineHeight: "1.5",
                                children: category.description
                            })
                        ]
                    }, category.id))
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                columns: {
                    base: 1,
                    md: 3
                },
                spacing: {
                    base: 5,
                    md: 8
                },
                mt: {
                    base: 10,
                    md: 16
                },
                mb: {
                    base: 6,
                    md: 8
                },
                as: framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div,
                variants: containerVariants,
                initial: "hidden",
                animate: "visible",
                px: {
                    base: 3,
                    md: 0
                },
                width: "100%",
                children: [
                    {
                        value: "50+",
                        label: t("stat_tools")
                    },
                    {
                        value: "10x",
                        label: t("stat_thinking")
                    },
                    {
                        value: "24/7",
                        label: t("stat_assistance")
                    }
                ].map((stat, idx)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(MotionBox, {
                        variants: itemVariants,
                        textAlign: "center",
                        bg: colors.cardBg,
                        p: {
                            base: 5,
                            md: 6
                        },
                        borderRadius: "lg",
                        boxShadow: "sm",
                        border: "1px solid",
                        borderColor: colors.cardBorder,
                        transition: "all 0.3s",
                        _hover: {
                            transform: "translateY(-3px)",
                            boxShadow: "md"
                        },
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "xl",
                                    md: "2xl"
                                },
                                bgGradient: "linear(to-r, blue.400, teal.500)",
                                bgClip: "text",
                                mb: {
                                    base: 1,
                                    md: 2
                                },
                                children: stat.value
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                color: subTextColor,
                                fontSize: {
                                    base: "md",
                                    md: "lg"
                                },
                                fontWeight: "medium",
                                children: stat.label
                            })
                        ]
                    }, idx))
            })
        ]
    });
};
const ThinkingSection = ()=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    const cardBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useColorModeValue)("white", "gray.700");
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
        id: "thinking-benefits",
        bg: "blue.50",
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
            spacing: 12,
            width: "100%",
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                    id: "thinking-benefits-heading",
                    color: "dodgerblue",
                    textAlign: "center",
                    size: "xl",
                    children: t("thinking_benefits_title")
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                    columns: {
                        base: 1,
                        md: 3
                    },
                    spacing: 8,
                    width: "100%",
                    children: benefits.map((card)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(MotionBox, {
                            bg: cardBg,
                            p: 8,
                            // pt={4}
                            borderRadius: "xl",
                            boxShadow: "xl",
                            backgroundColor: "#fafafa",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            whileInView: {
                                opacity: 1,
                                y: 0
                            },
                            viewport: {
                                once: true
                            },
                            transition: {
                                duration: 0.5
                            },
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 4,
                                align: "flex-start",
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                bg: `${card.color}.100`,
                                                color: `${card.color}.500`,
                                                borderRadius: "md",
                                                p: 3,
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                    as: card.icon,
                                                    w: 6,
                                                    h: 6
                                                })
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                size: "md",
                                                color: `${card.color}.500`,
                                                children: t(card.title)
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("ul", {
                                        children: t(card.description).split("\n").map((text, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("li", {
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                    color: "gray.600",
                                                    fontSize: "lg",
                                                    children: text
                                                })
                                            }, index))
                                    })
                                ]
                            })
                        }, card.id))
                })
            ]
        })
    });
};
const AIToolsSection = ({ isMobile , basePath  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)("");
    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)("all");
    const [activeCategories, setActiveCategories] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);
    // Color scheme for categories - more subtle colors
    const categoryColors = {
        // Main categories
        "Mindmap": "blue",
        "Infographics": "green",
        "Slides": "cyan",
        "Images": "red",
        "Text": "purple",
        // Sub-categories (tags)
        "Mindmap Generator": "blue",
        "AI Education": "teal",
        "Creative Thinking": "purple",
        "Critical Thinking": "cyan",
        "Business Insights": "green",
        "Psychological Insights": "pink",
        "Image Insights": "red",
        "all": "gray"
    };
    // Get all unique tags across all tool categories
    const allTags = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{
        const tags = new Set();
        Object.values(aiTools).forEach((toolCategory)=>{
            toolCategory.forEach((tool)=>{
                if (tool.tag) tags.add(tool.tag);
            });
        });
        return [
            "all",
            ...Array.from(tags)
        ];
    }, []);
    // Filter tools based on search query and active category
    const filteredTools = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{
        // Get tools from all selected categories, or all categories if none selected
        let tools = [];
        if (activeCategories.length === 0) {
            // If no categories selected, show all tools with their category info
            Object.keys(aiTools).forEach((category)=>{
                // Make sure each tool has its main category property
                const toolsWithCategory = aiTools[category].map((tool)=>({
                        ...tool,
                        mainCategory: category // Add main category for grouping
                    }));
                tools = [
                    ...tools,
                    ...toolsWithCategory
                ];
            });
        } else {
            // Get tools from selected categories
            activeCategories.forEach((category)=>{
                if (aiTools[category]) {
                    // Add the category to each tool for reference
                    const toolsWithCategory = aiTools[category].map((tool)=>({
                            ...tool,
                            mainCategory: category
                        }));
                    tools = [
                        ...tools,
                        ...toolsWithCategory
                    ];
                }
            });
        }
        return tools.filter((tool)=>{
            const matchesSearch = searchQuery === "" || tool.title.toLowerCase().includes(searchQuery.toLowerCase()) || t(tool.description).toLowerCase().includes(searchQuery.toLowerCase());
            const matchesCategory = activeCategory === "all" || tool.tag === activeCategory;
            return matchesSearch && matchesCategory;
        });
    }, [
        activeCategories,
        searchQuery,
        activeCategory,
        t
    ]);
    // Group filtered tools by tag or by main category when no category is selected
    const groupedFilteredTools = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{
        if (activeCategories.length === 0) {
            // When no category is selected, group by main category first, then by tag
            const groupedByMainCategory = {};
            // First group by main category
            filteredTools.forEach((tool)=>{
                const mainCategory = tool.mainCategory || "Other";
                if (!groupedByMainCategory[mainCategory]) {
                    groupedByMainCategory[mainCategory] = [];
                }
                groupedByMainCategory[mainCategory].push(tool);
            });
            // Then within each main category, group by tag
            const result = {};
            Object.keys(groupedByMainCategory).forEach((mainCategory)=>{
                const categoryKey = `${mainCategory}`;
                result[categoryKey] = groupedByMainCategory[mainCategory];
            });
            return result;
        } else {
            // When a category is selected, group by tag as before
            return filteredTools.reduce((acc, tool)=>{
                const tag = tool.tag || "Uncategorized";
                if (!acc[tag]) {
                    acc[tag] = [];
                }
                acc[tag].push(tool);
                return acc;
            }, {});
        }
    }, [
        filteredTools,
        activeCategories
    ]);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
        id: "ai_tools",
        pt: {
            base: 8,
            md: 12
        },
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                px: {
                    base: 4,
                    md: 0
                },
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                        id: "ai_tools",
                        size: {
                            base: "lg",
                            md: "xl"
                        },
                        bgGradient: "linear(to-r, blue.400, fuchsia)",
                        bgClip: "text",
                        mb: {
                            base: 4,
                            md: 6
                        },
                        mt: 0,
                        lineHeight: 1.2,
                        textAlign: "center",
                        children: t("ai_tools_heading")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                        fontSize: {
                            base: "md",
                            md: "xl"
                        },
                        color: "gray.600",
                        maxW: "3xl",
                        mx: "auto",
                        textAlign: "center",
                        mb: {
                            base: 6,
                            md: 8
                        },
                        px: {
                            base: 2,
                            md: 0
                        },
                        children: t("ai_tools_description")
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                mb: {
                    base: 8,
                    md: 10
                },
                width: "100%",
                px: {
                    base: 4,
                    md: 0
                },
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        align: "center",
                        mb: {
                            base: 4,
                            md: 6
                        },
                        flexWrap: "nowrap",
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                color: "gray.700",
                                whiteSpace: "nowrap",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaStar,
                                        color: "yellow.500",
                                        mr: 2
                                    }),
                                    t("popular_tools", "Popular Tools")
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                flex: "1",
                                height: "1px",
                                bg: "gray.200",
                                ml: 4
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                        columns: {
                            base: 1,
                            sm: 2,
                            lg: 4
                        },
                        spacing: {
                            base: 4,
                            md: 6
                        },
                        children: [
                            aiTools.Mindmap.find((tool)=>tool.title === "AI Mindmap"),
                            aiTools.Infographics.find((tool)=>tool.title === "AI Infographic Generator"),
                            aiTools.Slides.find((tool)=>tool.title === "AI PPT/Slides"),
                            aiTools.Mindmap.find((tool)=>tool.title === "AI Brainstorming")
                        ].map((tool, index)=>tool && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ToolCard, {
                                ...tool,
                                description: t(tool.description),
                                link: basePath + tool.link,
                                tag: tool.tag,
                                isFeatured: true,
                                isLaunched: true
                            }, index))
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                mb: {
                    base: 8,
                    md: 10
                },
                width: "100%",
                px: {
                    base: 4,
                    md: 0
                },
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        align: "center",
                        mb: {
                            base: 4,
                            md: 6
                        },
                        flexWrap: "nowrap",
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                color: "gray.700",
                                whiteSpace: "nowrap",
                                display: "flex",
                                alignItems: "center",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRocket,
                                        color: "purple.500",
                                        mr: 2
                                    }),
                                    t("whats_new", "What's New"),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {
                                        ml: 2,
                                        colorScheme: "purple",
                                        variant: "solid",
                                        fontSize: {
                                            base: "2xs",
                                            md: "xs"
                                        },
                                        children: "NEW"
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                flex: "1",
                                height: "1px",
                                bg: "gray.200",
                                ml: 4
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                        columns: {
                            base: 1,
                            sm: 2,
                            lg: 4
                        },
                        spacing: {
                            base: 4,
                            md: 6
                        },
                        children: [
                            aiTools.Mindmap.find((tool)=>tool.title === "AI MindKit"),
                            aiTools.Infographics.find((tool)=>tool.title === "AI InsightCards"),
                            aiTools.Mindmap.find((tool)=>tool.title === "AI LogicLens"),
                            aiTools.Images.find((tool)=>tool.title === "AI Avatar Generator")
                        ].map((tool, index)=>tool && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ToolCard, {
                                ...tool,
                                description: t(tool.description),
                                link: basePath + tool.link,
                                tag: tool.tag,
                                isFeatured: true,
                                isNew: true,
                                isLaunched: true
                            }, index))
                    })
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                mb: {
                    base: 8,
                    md: 10
                },
                width: "100%",
                px: {
                    base: 4,
                    md: 0
                },
                children: [
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        align: "center",
                        mb: {
                            base: 8,
                            md: 12
                        },
                        mt: {
                            base: 4,
                            md: 6
                        },
                        flexWrap: "nowrap",
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                flex: "1",
                                height: "1px",
                                bg: "gray.200",
                                mr: 4
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                size: {
                                    base: "sm",
                                    md: "md"
                                },
                                color: "gray.700",
                                whiteSpace: "nowrap",
                                children: t("more", "More")
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                flex: "1",
                                height: "1px",
                                bg: "gray.200",
                                ml: 4
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        justify: "center",
                        mb: {
                            base: 6,
                            md: 8
                        },
                        overflowX: "auto",
                        pb: {
                            base: 2,
                            md: 0
                        },
                        mx: {
                            base: -4,
                            md: 0
                        },
                        px: {
                            base: 4,
                            md: 0
                        },
                        css: {
                            "-webkit-overflow-scrolling": "touch",
                            scrollbarWidth: "thin",
                            "&::-webkit-scrollbar": {
                                height: "4px"
                            },
                            "&::-webkit-scrollbar-thumb": {
                                backgroundColor: "rgba(0,0,0,0.2)",
                                borderRadius: "4px"
                            }
                        },
                        style: {
                            justifyContent: "unset"
                        },
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                            spacing: {
                                base: 2,
                                md: 4
                            },
                            flexWrap: {
                                base: "nowrap",
                                md: "wrap"
                            },
                            justify: "center",
                            width: {
                                base: "max-content",
                                md: "100%"
                            },
                            children: Object.keys(aiTools).map((category)=>{
                                // Define category-specific colors and icons
                                const getCategoryColor = (cat)=>{
                                    switch(cat){
                                        case "Mindmap":
                                            return "blue";
                                        case "Infographics":
                                            return "green";
                                        case "Slides":
                                            return "cyan";
                                        case "Images":
                                            return "red";
                                        case "Text":
                                            return "purple";
                                        default:
                                            return "blue";
                                    }
                                };
                                const categoryColor = getCategoryColor(category);
                                const categoryIcon = category === "Mindmap" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap : category === "Infographics" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartBar : category === "Slides" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDesktop : category === "Images" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage : category === "Text" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaNewspaper : react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools;
                                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                    size: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    colorScheme: categoryColor,
                                    variant: activeCategories.includes(category) ? "solid" : "outline",
                                    onClick: ()=>{
                                        // Toggle category selection (single selection only)
                                        if (activeCategories.includes(category)) {
                                            // Remove category if already selected (deselect)
                                            setActiveCategories([]);
                                        } else {
                                            // Select only this category
                                            setActiveCategories([
                                                category
                                            ]);
                                        }
                                        setActiveCategory("all");
                                        setSearchQuery("");
                                    },
                                    minW: {
                                        base: "auto",
                                        md: "120px"
                                    },
                                    flexShrink: 0,
                                    leftIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: categoryIcon,
                                        w: {
                                            base: 4,
                                            md: 5
                                        },
                                        h: {
                                            base: 4,
                                            md: 5
                                        }
                                    }),
                                    position: "relative",
                                    px: {
                                        base: 3,
                                        md: 4
                                    },
                                    _after: activeCategories.includes(category) ? {
                                        content: '""',
                                        position: "absolute",
                                        bottom: "-4px",
                                        left: "50%",
                                        transform: "translateX(-50%)",
                                        width: {
                                            base: "40px",
                                            md: "68px"
                                        },
                                        height: "3px",
                                        borderRadius: "full",
                                        bg: `${categoryColor}.500`
                                    } : {},
                                    children: category
                                }, category);
                            })
                        })
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                        direction: {
                            base: "column",
                            md: "row"
                        },
                        justify: "space-between",
                        align: {
                            base: "stretch",
                            md: "center"
                        },
                        mb: {
                            base: 6,
                            md: 8
                        },
                        gap: {
                            base: 3,
                            md: 4
                        },
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                position: "relative",
                                w: {
                                    base: "full",
                                    md: "300px"
                                },
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {
                                        placeholder: t("search_tools", "Search tools..."),
                                        value: searchQuery,
                                        onChange: (e)=>setSearchQuery(e.target.value),
                                        pr: "40px",
                                        bg: "white",
                                        borderColor: "gray.300",
                                        _hover: {
                                            borderColor: "blue.300"
                                        },
                                        _focus: {
                                            borderColor: "blue.500",
                                            boxShadow: "0 0 0 1px blue.500"
                                        },
                                        size: {
                                            base: "md",
                                            md: "md"
                                        },
                                        fontSize: {
                                            base: "sm",
                                            md: "md"
                                        }
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSearch,
                                        position: "absolute",
                                        right: "12px",
                                        top: "50%",
                                        transform: "translateY(-50%)",
                                        color: "gray.400",
                                        w: {
                                            base: 4,
                                            md: 5
                                        },
                                        h: {
                                            base: 4,
                                            md: 5
                                        }
                                    })
                                ]
                            }),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                overflowX: "auto",
                                w: "full",
                                mx: {
                                    base: -4,
                                    md: 0
                                },
                                px: {
                                    base: 4,
                                    md: 0
                                },
                                pb: {
                                    base: 2,
                                    md: 0
                                },
                                css: {
                                    "-webkit-overflow-scrolling": "touch",
                                    scrollbarWidth: "thin",
                                    "&::-webkit-scrollbar": {
                                        height: "4px"
                                    },
                                    "&::-webkit-scrollbar-thumb": {
                                        backgroundColor: "rgba(0,0,0,0.2)",
                                        borderRadius: "4px"
                                    }
                                },
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                    spacing: {
                                        base: 1.5,
                                        md: 2
                                    },
                                    flexWrap: {
                                        base: "nowrap",
                                        md: "wrap"
                                    },
                                    width: {
                                        base: "max-content",
                                        md: "100%"
                                    },
                                    pt: 1,
                                    children: allTags.map((tag)=>{
                                        const isActive = activeCategory === tag;
                                        const tagColor = categoryColors[tag] || "blue";
                                        return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                            as: "button",
                                            px: {
                                                base: 2,
                                                md: 3
                                            },
                                            py: {
                                                base: 1.5,
                                                md: 2
                                            },
                                            borderRadius: "full",
                                            bg: isActive ? `${tagColor}.500` : "transparent",
                                            color: isActive ? "white" : `${tagColor}.600`,
                                            border: "1px solid",
                                            borderColor: isActive ? `${tagColor}.500` : "gray.200",
                                            cursor: "pointer",
                                            onClick: ()=>setActiveCategory(tag),
                                            fontSize: {
                                                base: "xs",
                                                md: "sm"
                                            },
                                            fontWeight: "medium",
                                            flexShrink: 0,
                                            _hover: {
                                                bg: isActive ? `${tagColor}.600` : `${tagColor}.50`,
                                                transform: "translateY(-2px)",
                                                boxShadow: "sm"
                                            },
                                            transition: "all 0.2s",
                                            boxShadow: isActive ? "md" : "none",
                                            minH: {
                                                base: "28px",
                                                md: "32px"
                                            },
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            children: tag === "all" ? t("all_categories", "All Categories") : tag
                                        }, tag);
                                    })
                                })
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                        color: "gray.500",
                        mb: {
                            base: 4,
                            md: 6
                        },
                        fontSize: {
                            base: "xs",
                            md: "sm"
                        },
                        children: filteredTools.length === 0 ? t("no_tools_found", "No tools found") : `${t("showing_tools", "Showing")} ${filteredTools.length} ${filteredTools.length === 1 ? t("tool", "tool") : t("tools", "tools")}`
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                        spacing: {
                            base: 8,
                            md: 10
                        },
                        align: "stretch",
                        children: Object.keys(groupedFilteredTools).length > 0 ? Object.keys(groupedFilteredTools).map((tag)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                        align: "center",
                                        mb: {
                                            base: 4,
                                            md: 6
                                        },
                                        id: tag,
                                        flexWrap: "nowrap",
                                        children: [
                                            tag !== "Uncategorized" && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                        p: {
                                                            base: 1.5,
                                                            md: 2
                                                        },
                                                        borderRadius: "md",
                                                        bg: `${categoryColors[tag] || "blue"}.50`,
                                                        mr: {
                                                            base: 2,
                                                            md: 3
                                                        },
                                                        flexShrink: 0,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                            as: // Check if tag is a main category
                                                            tag === "Mindmap" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap : tag === "Infographics" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartBar : tag === "Slides" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDesktop : tag === "Images" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage : tag === "Text" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaNewspaper : // Otherwise use tag icons
                                                            tag === "Mindmap Generator" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap : tag === "AI Education" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGraduationCap : tag === "Creative Thinking" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb : tag === "Critical Thinking" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullseye : tag === "Business Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartLine : tag === "Psychological Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart : tag === "Image Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage : react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools,
                                                            color: `${categoryColors[tag] || "blue"}.600`,
                                                            w: {
                                                                base: 4,
                                                                md: 5
                                                            },
                                                            h: {
                                                                base: 4,
                                                                md: 5
                                                            }
                                                        })
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                        size: {
                                                            base: "sm",
                                                            md: "md"
                                                        },
                                                        color: "gray.700",
                                                        fontWeight: "600",
                                                        noOfLines: 1,
                                                        flexShrink: 0,
                                                        children: tag
                                                    }),
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                        ml: {
                                                            base: 2,
                                                            md: 3
                                                        },
                                                        px: {
                                                            base: 1.5,
                                                            md: 2
                                                        },
                                                        py: {
                                                            base: 0.5,
                                                            md: 1
                                                        },
                                                        borderRadius: "md",
                                                        bg: `${categoryColors[tag] || "blue"}.50`,
                                                        color: `${categoryColors[tag] || "blue"}.700`,
                                                        fontSize: {
                                                            base: "xs",
                                                            md: "sm"
                                                        },
                                                        fontWeight: "medium",
                                                        flexShrink: 0,
                                                        minW: {
                                                            base: "40px",
                                                            md: "auto"
                                                        },
                                                        textAlign: "center",
                                                        children: [
                                                            groupedFilteredTools[tag].length,
                                                            " ",
                                                            groupedFilteredTools[tag].length === 1 ? "tool" : "tools"
                                                        ]
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                flex: "1",
                                                height: "1px",
                                                bg: "gray.200",
                                                ml: {
                                                    base: 2,
                                                    md: 4
                                                }
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            sm: 2,
                                            lg: 3
                                        },
                                        spacing: {
                                            base: 4,
                                            md: 8
                                        },
                                        w: "full",
                                        children: groupedFilteredTools[tag].map((tool, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ToolCard, {
                                                ...tool,
                                                description: t(tool.description),
                                                link: basePath + tool.link,
                                                tag: tool.tag
                                            }, index))
                                    })
                                ]
                            }, tag)) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            textAlign: "center",
                            py: {
                                base: 8,
                                md: 10
                            },
                            px: {
                                base: 4,
                                md: 6
                            },
                            bg: "gray.50",
                            borderRadius: "lg",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSearch,
                                    w: {
                                        base: 8,
                                        md: 10
                                    },
                                    h: {
                                        base: 8,
                                        md: 10
                                    },
                                    color: "gray.400",
                                    mb: {
                                        base: 3,
                                        md: 4
                                    }
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                    size: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    color: "gray.500",
                                    mb: {
                                        base: 1,
                                        md: 2
                                    },
                                    children: t("no_tools_found", "No tools found")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                    color: "gray.500",
                                    fontSize: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    children: t("try_different_search", "Try a different search term or category")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                    mt: {
                                        base: 4,
                                        md: 6
                                    },
                                    colorScheme: "blue",
                                    size: {
                                        base: "sm",
                                        md: "md"
                                    },
                                    onClick: ()=>{
                                        setSearchQuery("");
                                        setActiveCategory("all");
                                        setActiveCategories([]);
                                    },
                                    children: t("clear_filters", "Clear Filters")
                                })
                            ]
                        })
                    })
                ]
            }),
            !isMobile && filteredTools.length > 6 && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                position: "fixed",
                right: "20px",
                top: "50%",
                transform: "translateY(-50%)",
                zIndex: 10,
                display: {
                    base: "none",
                    xl: "block"
                },
                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                    spacing: 2,
                    bg: "white",
                    p: 3,
                    borderRadius: "lg",
                    boxShadow: "lg",
                    children: Object.keys(groupedFilteredTools).map((tag)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {
                            label: tag,
                            placement: "left",
                            hasArrow: true,
                            bg: `${categoryColors[tag] || "blue"}.500`,
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {
                                href: `#${tag}`,
                                _hover: {
                                    textDecoration: "none"
                                },
                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                    p: 2,
                                    borderRadius: "md",
                                    bg: `${categoryColors[tag] || "blue"}.50`,
                                    _hover: {
                                        bg: `${categoryColors[tag] || "blue"}.100`
                                    },
                                    transition: "all 0.2s",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                        as: // Check if tag is a main category
                                        tag === "Mindmap" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap : tag === "Infographics" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartBar : tag === "Slides" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDesktop : tag === "Images" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage : tag === "Text" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaNewspaper : // Otherwise use tag icons
                                        tag === "Mindmap Generator" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap : tag === "AI Education" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGraduationCap : tag === "Creative Thinking" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb : tag === "Critical Thinking" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullseye : tag === "Business Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartLine : tag === "Psychological Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart : tag === "Image Insights" ? react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage : react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools,
                                        color: `${categoryColors[tag] || "blue"}.500`
                                    })
                                })
                            })
                        }, tag))
                })
            })
        ]
    });
};
const AIFlowSection = ()=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useBreakpointValue)({
        base: true,
        md: false
    });
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
        bg: "blue.50",
        id: "aiflow-section",
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
            spacing: 8,
            width: "100%",
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                    size: "xl",
                    bgGradient: "linear(to-r, blue.400, purple.500)",
                    bgClip: "text",
                    textAlign: "center",
                    mb: 4,
                    children: t("aiflow_and_aitools_title")
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                    fontSize: {
                        base: "md",
                        md: "lg"
                    },
                    color: "gray.700",
                    maxW: "3xl",
                    textAlign: "center",
                    mb: 6,
                    children: t("continue_exploring_ai_content")
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                    columns: {
                        base: 1,
                        lg: 3
                    },
                    spacing: 8,
                    width: "100%",
                    maxW: "7xl",
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            p: 6,
                            bg: "purple.50",
                            borderRadius: "xl",
                            boxShadow: "lg",
                            position: "relative",
                            overflow: "hidden",
                            height: "100%",
                            borderTop: "4px solid",
                            borderColor: "purple.500",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 5,
                                align: "start",
                                height: "100%",
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                        spacing: 4,
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                bg: "purple.100",
                                                p: 2,
                                                borderRadius: "md",
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools,
                                                    w: 6,
                                                    h: 6,
                                                    color: "purple.500"
                                                })
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                size: "md",
                                                color: "purple.600",
                                                children: t("about_aitools_title")
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                        spacing: 4,
                                        align: "start",
                                        width: "100%",
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aitools_point1")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aitools_point2")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aitools_point3")
                                                    })
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            p: 6,
                            bg: "white",
                            borderRadius: "xl",
                            boxShadow: "lg",
                            position: "relative",
                            overflow: "hidden",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            justifyContent: "center",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 4,
                                width: "100%",
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                        spacing: 4,
                                        justifyContent: "center",
                                        width: "100%",
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb,
                                                w: 6,
                                                h: 6,
                                                color: "blue.500"
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                size: "md",
                                                color: "blue.600",
                                                children: t("what_after_generation")
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                        width: "100%",
                                        position: "relative",
                                        py: 4,
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                p: 4,
                                                bg: "purple.50",
                                                borderRadius: "lg",
                                                boxShadow: "md",
                                                mb: 10,
                                                position: "relative",
                                                zIndex: 1,
                                                children: [
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                        children: [
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools,
                                                                w: 5,
                                                                h: 5,
                                                                color: "purple.500"
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                fontWeight: "bold",
                                                                color: "purple.600",
                                                                children: "AI Tools"
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "sm",
                                                        color: "gray.600",
                                                        mt: 1,
                                                        children: t("ai_tools_generation_explanation")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                position: "absolute",
                                                top: "30%",
                                                left: "50%",
                                                transform: "translateX(-50%)",
                                                width: "2px",
                                                height: "40%",
                                                bg: "blue.400",
                                                zIndex: 0
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                position: "absolute",
                                                top: "50%",
                                                left: "50%",
                                                transform: "translate(-50%, -50%)",
                                                bg: "white",
                                                borderRadius: "full",
                                                boxShadow: "md",
                                                p: 2,
                                                zIndex: 1,
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowDown,
                                                    w: 5,
                                                    h: 5,
                                                    color: "blue.500"
                                                })
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                p: 4,
                                                bg: "blue.50",
                                                borderRadius: "lg",
                                                boxShadow: "md",
                                                position: "relative",
                                                zIndex: 1,
                                                children: [
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                        children: [
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRobot,
                                                                w: 5,
                                                                h: 5,
                                                                color: "blue.500"
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                fontWeight: "bold",
                                                                color: "blue.600",
                                                                children: "AIFlow"
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "sm",
                                                        color: "gray.600",
                                                        mt: 1,
                                                        children: t("continue_with_aiflow")
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {
                                        href: "https://www.funblocks.net/aiflow",
                                        isExternal: true,
                                        mt: 2,
                                        width: "100%",
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                            colorScheme: "blue",
                                            size: "md",
                                            width: "100%",
                                            rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowCircleRight, {}),
                                            _hover: {
                                                transform: "translateY(-2px)",
                                                boxShadow: "md"
                                            },
                                            transition: "all 0.3s",
                                            children: t("try_aiflow_now")
                                        })
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                            p: 6,
                            bg: "blue.50",
                            borderRadius: "xl",
                            boxShadow: "lg",
                            position: "relative",
                            overflow: "hidden",
                            height: "100%",
                            borderTop: "4px solid",
                            borderColor: "blue.500",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 5,
                                align: "start",
                                height: "100%",
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                        spacing: 4,
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                bg: "blue.100",
                                                p: 2,
                                                borderRadius: "md",
                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                    as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRobot,
                                                    w: 6,
                                                    h: 6,
                                                    color: "blue.500"
                                                })
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                size: "md",
                                                color: "blue.600",
                                                children: t("about_aiflow_title")
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                        spacing: 4,
                                        align: "start",
                                        width: "100%",
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aiflow_point1")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aiflow_point2")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                        as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaCheck,
                                                        w: 5,
                                                        h: 5,
                                                        color: "green.500",
                                                        flexShrink: 0
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("about_aiflow_point3")
                                                    })
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    ]
                }),
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                    maxW: "5xl",
                    p: {
                        base: 4,
                        md: 8
                    },
                    bg: "white",
                    borderRadius: "xl",
                    boxShadow: "lg",
                    mt: 6,
                    width: "100%",
                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                        columns: {
                            base: 1,
                            md: 2
                        },
                        spacing: 8,
                        children: [
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 5,
                                align: "start",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: "md",
                                        color: "teal.600",
                                        children: t("deep_dive_capabilities")
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                        spacing: 4,
                                        align: "start",
                                        width: "100%",
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                        bg: "teal.100",
                                                        borderRadius: "full",
                                                        w: 8,
                                                        h: 8,
                                                        flexShrink: 0,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                            fontWeight: "bold",
                                                            color: "teal.700",
                                                            children: "1"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("click_continue_to_explore")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                        bg: "teal.100",
                                                        borderRadius: "full",
                                                        w: 8,
                                                        h: 8,
                                                        flexShrink: 0,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                            fontWeight: "bold",
                                                            color: "teal.700",
                                                            children: "2"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("access_funblocks_aiflow")
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                spacing: 3,
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Center, {
                                                        bg: "teal.100",
                                                        borderRadius: "full",
                                                        w: 8,
                                                        h: 8,
                                                        flexShrink: 0,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                            fontWeight: "bold",
                                                            color: "teal.700",
                                                            children: "3"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                        fontSize: "md",
                                                        color: "gray.700",
                                                        children: t("aitools_not_covered")
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                        p: 4,
                                        bg: "blue.50",
                                        borderRadius: "md",
                                        width: "100%",
                                        mt: 2,
                                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                            fontSize: "sm",
                                            fontStyle: "italic",
                                            color: "blue.700",
                                            children: [
                                                t("aitools_not_covered"),
                                                " ",
                                                t("continue_with_aiflow")
                                            ]
                                        })
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Image, {
                                        src: "https://www.funblocks.net/img/portfolio/thumbnails/aitools_mindmap_book_generated.png",
                                        alt: "AIFlow Exploration Interface",
                                        borderRadius: "md",
                                        boxShadow: "md",
                                        width: "100%",
                                        height: "auto",
                                        objectFit: "cover"
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: "sm",
                                        color: "gray.500",
                                        mt: 2,
                                        textAlign: "center",
                                        children: t("access_funblocks_aiflow")
                                    })
                                ]
                            })
                        ]
                    })
                })
            ]
        })
    });
};
const MainFrame = ()=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)("common");
    const [isMobile] = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useMediaQuery)("(max-width: 768px)");
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_8___default()().publicRuntimeConfig;
    const features = [
        {
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
            title: t("ai_powered_intelligence"),
            description: t("ai_powered_intelligence_desc"),
            gradient: "linear(to-r, blue.400, purple.500)"
        },
        {
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRocket,
            title: t("mental_models_toolify"),
            description: t("mental_models_toolify_desc"),
            gradient: "linear(to-r, purple.400, pink.500)"
        },
        {
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTools,
            title: t("integrated_toolset"),
            description: t("integrated_toolset_desc"),
            gradient: "linear(to-r, green.400, teal.500)"
        },
        {
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserFriends,
            title: t("user_friendly"),
            description: t("user_friendly_desc"),
            gradient: "linear(to-r, orange.400, red.500)"
        }
    ];
    const faqs = [
        {
            question: t("platform_faq_1_q"),
            answer: t("platform_faq_1_a")
        },
        {
            question: t("platform_faq_2_q"),
            answer: t("platform_faq_2_a")
        },
        {
            question: t("platform_faq_3_q"),
            answer: t("platform_faq_3_a")
        },
        {
            question: t("platform_faq_4_q"),
            answer: t("platform_faq_4_a")
        },
        {
            question: t("platform_faq_5_q"),
            answer: t("platform_faq_5_a")
        },
        {
            question: t("platform_faq_6_q"),
            answer: t("platform_faq_6_a")
        },
        {
            question: t("platform_faq_7_q"),
            answer: t("platform_faq_7_a")
        },
        {
            question: t("platform_faq_8_q"),
            answer: t("platform_faq_8_a")
        },
        {
            question: t("platform_faq_9_q"),
            answer: t("platform_faq_9_a")
        },
        {
            question: t("platform_faq_10_q"),
            answer: t("platform_faq_10_a")
        },
        {
            question: t("platform_faq_11_q"),
            answer: t("platform_faq_11_a")
        },
        {
            question: t("platform_faq_12_q"),
            answer: t("platform_faq_12_a")
        },
        {
            question: t("platform_faq_13_q"),
            answer: t("platform_faq_13_a")
        },
        {
            question: t("platform_faq_14_q"),
            answer: t("platform_faq_14_a")
        },
        {
            question: t("platform_faq_15_q"),
            answer: t("platform_faq_15_a")
        }
    ];
    const comparisons = [
        {
            title: t("comparison_visual_title"),
            chatgpt: t("chatgpt_text_heavy"),
            funblocks: t("funblocks_visual_friendly"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage
        },
        {
            title: t("comparison_exploration_title"),
            chatgpt: t("chatgpt_linear_chat"),
            funblocks: t("funblocks_multi_perspective"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSitemap
        },
        {
            title: t("comparison_guidance_title"),
            chatgpt: t("chatgpt_passive_waiting"),
            funblocks: t("funblocks_proactive_guide"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRocket
        },
        {
            title: t("comparison_learning_title"),
            chatgpt: t("chatgpt_answer_only"),
            funblocks: t("funblocks_thinking_process"),
            icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain
        }
    ];
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
        width: "100%",
        position: "relative",
        overflowY: "hidden",
        gap: "4px",
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_Header__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {}),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                overflowY: "auto",
                height: `calc(100vh - 76px)`,
                alignItems: "center",
                width: "100%",
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                    style: {
                        width: "100%",
                        gap: "0px"
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(HeroSection, {}),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AIToolsSection, {
                            isMobile: isMobile,
                            basePath: basePath
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ThinkingSection, {}),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "gray.50",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 8,
                                align: "center",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: "xl",
                                        bgGradient: "linear(to-r, blue.400, purple.500)",
                                        bgClip: "text",
                                        textAlign: "center",
                                        children: t("examples_showcase_title", "See Our Tools in Action")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: "xl",
                                        color: "gray.600",
                                        maxW: "3xl",
                                        textAlign: "center",
                                        mb: 6,
                                        children: t("examples_showcase_desc", "Explore real examples of what you can create with our AI-powered tools")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 2
                                        },
                                        spacing: 8,
                                        width: "100%",
                                        children: [
                                            {
                                                title: t("example_mindmap_title", "AI Mindmap Example"),
                                                description: t("example_mindmap_desc", 'Visualize complex topics with our AI Mindmap tool. This example shows a mindmap about the book "The Great Gatsby".'),
                                                imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindmap_book.png",
                                                link: "/mindmap"
                                            },
                                            {
                                                title: t("example_infographic_title", "AI Infographic Example"),
                                                description: t("example_infographic_desc", "Create beautiful infographics instantly. This example shows a witty and attractive infographic generated with InsightCards."),
                                                imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_paradoxical.png",
                                                link: "/infographic"
                                            },
                                            {
                                                title: t("example_slides_title", "AI Slides Example"),
                                                description: t("example_slides_desc", "Generate professional presentations in seconds. This example shows a competitor analysis presentation."),
                                                imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aislides_beyond_chatgpt.png",
                                                link: "/slides"
                                            },
                                            {
                                                title: t("example_mindsnap_title", "AI MindSnap Example"),
                                                description: t("example_mindsnap_desc", "Transform topics into visual mental models. This example shows a SWOT analysis."),
                                                imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png",
                                                link: "/mindsnap"
                                            }
                                        ].map((example, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                bg: "white",
                                                borderRadius: "xl",
                                                boxShadow: "xl",
                                                overflow: "hidden",
                                                transition: "all 0.3s",
                                                _hover: {
                                                    transform: "translateY(-5px)",
                                                    boxShadow: "2xl"
                                                },
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Image, {
                                                        src: example.imageSrc,
                                                        alt: example.title,
                                                        width: "100%",
                                                        height: isMobile ? "250px" : "400px",
                                                        objectFit: "contain"
                                                    }),
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                        p: 6,
                                                        children: [
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                size: "md",
                                                                mb: 2,
                                                                children: example.title
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                color: "gray.600",
                                                                mb: 4,
                                                                children: example.description
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                                                rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChevronRight, {}),
                                                                colorScheme: "blue",
                                                                variant: "outline",
                                                                size: "sm",
                                                                as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                                                target: "_blank",
                                                                href: basePath + example.link,
                                                                textDecoration: "none",
                                                                children: t("example_try_tool", "Try this tool")
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, index))
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AIFlowSection, {}),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "gray.50",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                    mb: 12,
                                    textAlign: "center",
                                    size: "xl",
                                    color: "#333",
                                    children: t("why_funblocks")
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                    columns: {
                                        base: 1,
                                        md: 2
                                    },
                                    spacing: 8,
                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                            bg: "white",
                                            p: 8,
                                            borderRadius: "lg",
                                            boxShadow: "md",
                                            _hover: {
                                                transform: "translateY(-4px)",
                                                boxShadow: "lg"
                                            },
                                            transition: "all 0.3s",
                                            children: [
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                    as: feature.icon,
                                                    w: 10,
                                                    h: 10,
                                                    mb: 4,
                                                    style: {
                                                        color: "dodgerblue"
                                                    },
                                                    bgGradient: feature.gradient,
                                                    bgClip: "text"
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                    size: "md",
                                                    mb: 4,
                                                    children: feature.title
                                                }),
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                    color: "gray.600",
                                                    children: feature.description
                                                })
                                            ]
                                        }, index))
                                })
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "white",
                            py: {
                                base: 12,
                                md: 16
                            },
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: {
                                    base: 6,
                                    md: 8
                                },
                                width: "100%",
                                children: [
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(MotionBox, {
                                        initial: {
                                            opacity: 0,
                                            y: 20
                                        },
                                        whileInView: {
                                            opacity: 1,
                                            y: 0
                                        },
                                        viewport: {
                                            once: true
                                        },
                                        transition: {
                                            duration: 0.5
                                        },
                                        textAlign: "center",
                                        maxW: "3xl",
                                        mx: "auto",
                                        px: {
                                            base: 4,
                                            md: 0
                                        },
                                        children: [
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                mb: {
                                                    base: 4,
                                                    md: 5
                                                },
                                                color: "#333",
                                                size: "xl",
                                                bgGradient: "linear(to-r, blue.500, purple.500)",
                                                bgClip: "text",
                                                children: t("why_not_chatgpt")
                                            }),
                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                fontSize: {
                                                    base: "lg",
                                                    md: "xl"
                                                },
                                                color: "gray.600",
                                                maxW: "3xl",
                                                textAlign: "center",
                                                children: t("chatgpt_comparison_intro")
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            lg: 2
                                        },
                                        spacing: {
                                            base: 6,
                                            md: 8
                                        },
                                        width: "100%",
                                        maxW: "container.xl",
                                        mx: "auto",
                                        children: comparisons.map((item, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(MotionBox, {
                                                initial: {
                                                    opacity: 0,
                                                    y: 20
                                                },
                                                whileInView: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                viewport: {
                                                    once: true
                                                },
                                                transition: {
                                                    duration: 0.5,
                                                    delay: index * 0.1
                                                },
                                                overflow: "hidden",
                                                borderRadius: "xl",
                                                boxShadow: "lg",
                                                bg: "white",
                                                border: "1px solid",
                                                borderColor: "gray.100",
                                                children: [
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                                        direction: "row",
                                                        alignItems: "center",
                                                        bg: "purple.50",
                                                        p: {
                                                            base: 4,
                                                            md: 5
                                                        },
                                                        borderBottom: "1px solid",
                                                        borderColor: "gray.100",
                                                        textAlign: "center",
                                                        gap: 4,
                                                        children: [
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                as: item.icon,
                                                                w: {
                                                                    base: 8,
                                                                    md: 10
                                                                },
                                                                h: {
                                                                    base: 8,
                                                                    md: 10
                                                                },
                                                                color: "purple.500"
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                size: {
                                                                    base: "sm",
                                                                    md: "md"
                                                                },
                                                                color: "purple.700",
                                                                fontWeight: "semibold",
                                                                children: item.title
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                        p: {
                                                            base: 5,
                                                            md: 6
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                                mb: 5,
                                                                p: {
                                                                    base: 4,
                                                                    md: 5
                                                                },
                                                                bg: "gray.50",
                                                                borderRadius: "lg",
                                                                borderLeft: "4px solid",
                                                                borderColor: "gray.300",
                                                                children: [
                                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                                                        align: "center",
                                                                        mb: 2,
                                                                        children: [
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                                as: react_icons_si__WEBPACK_IMPORTED_MODULE_4__.SiOpenai,
                                                                                color: "gray.500",
                                                                                mr: 2
                                                                            }),
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                                fontWeight: "bold",
                                                                                color: "gray.700",
                                                                                children: "ChatGPT"
                                                                            })
                                                                        ]
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                        color: "gray.600",
                                                                        fontSize: {
                                                                            base: "sm",
                                                                            md: "md"
                                                                        },
                                                                        children: item.chatgpt
                                                                    })
                                                                ]
                                                            }),
                                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                                p: {
                                                                    base: 4,
                                                                    md: 5
                                                                },
                                                                bg: "blue.50",
                                                                borderRadius: "lg",
                                                                borderLeft: "4px solid",
                                                                borderColor: "blue.400",
                                                                children: [
                                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                                                        align: "center",
                                                                        mb: 2,
                                                                        children: [
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                                as: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRocket,
                                                                                color: "blue.500",
                                                                                mr: 2
                                                                            }),
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                                fontWeight: "bold",
                                                                                color: "blue.600",
                                                                                children: "FunBlocks AI"
                                                                            })
                                                                        ]
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                        color: "blue.700",
                                                                        fontSize: {
                                                                            base: "sm",
                                                                            md: "md"
                                                                        },
                                                                        children: item.funblocks
                                                                    })
                                                                ]
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, index))
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(MotionBox, {
                                        initial: {
                                            opacity: 0,
                                            y: 20
                                        },
                                        whileInView: {
                                            opacity: 1,
                                            y: 0
                                        },
                                        viewport: {
                                            once: true
                                        },
                                        transition: {
                                            duration: 0.5,
                                            delay: 0.4
                                        },
                                        mt: {
                                            base: 6,
                                            md: 8
                                        },
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                            as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                            href: "#ai_tools",
                                            size: {
                                                base: "md",
                                                md: "lg"
                                            },
                                            colorScheme: "blue",
                                            rightIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaArrowCircleRight, {}),
                                            _hover: {
                                                transform: "translateY(-2px)"
                                            },
                                            transition: "all 0.3s",
                                            px: {
                                                base: 6,
                                                md: 8
                                            },
                                            py: {
                                                base: 6,
                                                md: 7
                                            },
                                            borderRadius: "full",
                                            boxShadow: "md",
                                            children: t("explore_tools")
                                        })
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_ComparisonTable__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
                            title: t("tools_comparison_title", "How FunBlocks AI Compares"),
                            description: t("tools_comparison_description", "See how FunBlocks AI stacks up against other AI tools and platforms in key areas that matter for productivity and learning."),
                            highlightColumn: "funblocks",
                            bg: "gray.50",
                            columns: [
                                {
                                    key: "funblocks",
                                    label: "FunBlocks AI"
                                },
                                {
                                    key: "chatgpt",
                                    label: "ChatGPT"
                                },
                                {
                                    key: "other_tools",
                                    label: "Other AI Tools"
                                }
                            ],
                            features: [
                                {
                                    name: t("comparison_feature_1", "Visual Thinking Tools"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: false,
                                    tooltip: t("comparison_feature_1_tooltip", "Specialized tools for creating mindmaps, infographics, and visual presentations")
                                },
                                {
                                    name: t("comparison_feature_2", "Educational Frameworks"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: false,
                                    tooltip: t("comparison_feature_2_tooltip", "Built-in educational frameworks like Bloom's Taxonomy, Marzano, and SOLO")
                                },
                                {
                                    name: t("comparison_feature_3", "Multi-perspective Analysis"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: true,
                                    tooltip: t("comparison_feature_3_tooltip", "Ability to analyze topics from multiple perspectives and mental models")
                                },
                                {
                                    name: t("comparison_feature_4", "Structured Outputs"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: true,
                                    tooltip: t("comparison_feature_4_tooltip", "Consistently structured outputs optimized for learning and retention")
                                },
                                {
                                    name: t("comparison_feature_5", "Specialized for Learning"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: false,
                                    tooltip: t("comparison_feature_5_tooltip", "Tools specifically designed to enhance learning and comprehension")
                                },
                                {
                                    name: t("comparison_feature_6", "Visual Export Options"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: true,
                                    tooltip: t("comparison_feature_6_tooltip", "Export as SVG, PNG, PDF and other visual formats")
                                },
                                {
                                    name: t("comparison_feature_7", "Research-Backed Design"),
                                    funblocks: true,
                                    chatgpt: false,
                                    other_tools: false,
                                    tooltip: t("comparison_feature_7_tooltip", "Tools designed based on cognitive science and learning research")
                                }
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "white",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 8,
                                align: "center",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: "xl",
                                        bgGradient: "linear(to-r, blue.400, purple.500)",
                                        bgClip: "text",
                                        textAlign: "center",
                                        color: "#333",
                                        children: t("llm_support_title")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: "xl",
                                        color: "gray.600",
                                        maxW: "3xl",
                                        textAlign: "center",
                                        mb: 6,
                                        children: t("llm_support_desc")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 2,
                                            lg: 4
                                        },
                                        spacing: 8,
                                        width: "100%",
                                        children: [
                                            {
                                                name: t("model_openai"),
                                                description: t("model_openai_desc"),
                                                icon: react_icons_si__WEBPACK_IMPORTED_MODULE_4__.SiOpenai,
                                                color: "#10a37f",
                                                gradient: "linear(to-r, green.400, teal.500)",
                                                bgColor: "rgba(16, 163, 127, 0.1)"
                                            },
                                            {
                                                name: t("model_anthropic"),
                                                description: t("model_anthropic_desc"),
                                                icon: react_icons_si__WEBPACK_IMPORTED_MODULE_4__.SiAnthropic,
                                                color: "#b15dff",
                                                gradient: "linear(to-r, purple.400, pink.500)",
                                                bgColor: "rgba(177, 93, 255, 0.1)"
                                            },
                                            {
                                                name: t("model_google"),
                                                description: t("model_google_desc"),
                                                icon: react_icons_si__WEBPACK_IMPORTED_MODULE_4__.SiGooglegemini,
                                                color: "#1a73e8",
                                                gradient: "linear(to-r, blue.400, cyan.400)",
                                                bgColor: "rgba(26, 115, 232, 0.1)"
                                            },
                                            {
                                                name: t("model_deepseek"),
                                                description: t("model_deepseek_desc"),
                                                // icon: DeepseekIcon,
                                                color: "#ff6b01",
                                                gradient: "linear(to-r, orange.400, red.400)",
                                                bgColor: "rgba(255, 107, 1, 0.1)"
                                            }
                                        ].map((model, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {
                                                initial: {
                                                    opacity: 0,
                                                    y: 20
                                                },
                                                whileInView: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                viewport: {
                                                    once: true
                                                },
                                                transition: {
                                                    duration: 0.5,
                                                    delay: index * 0.1
                                                },
                                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                    bg: "white",
                                                    p: 6,
                                                    borderRadius: "xl",
                                                    boxShadow: "md",
                                                    _hover: {
                                                        transform: "translateY(-4px)",
                                                        boxShadow: "xl",
                                                        borderColor: model.color
                                                    },
                                                    transition: "all 0.3s",
                                                    borderTop: "4px solid",
                                                    borderColor: model.color,
                                                    position: "relative",
                                                    overflow: "hidden",
                                                    height: "100%",
                                                    children: [
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                            position: "absolute",
                                                            top: "0",
                                                            left: "0",
                                                            right: "0",
                                                            height: "100%",
                                                            bg: model.bgColor,
                                                            opacity: "0.5",
                                                            zIndex: "0"
                                                        }),
                                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                                            spacing: 4,
                                                            align: "start",
                                                            position: "relative",
                                                            zIndex: "1",
                                                            height: "100%",
                                                            children: [
                                                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {
                                                                    children: [
                                                                        model.icon && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                                            as: model.icon,
                                                                            w: 10,
                                                                            h: 10,
                                                                            color: model.color
                                                                        }),
                                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                            size: "md",
                                                                            fontWeight: "bold",
                                                                            children: model.name
                                                                        })
                                                                    ]
                                                                }),
                                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                    color: "gray.600",
                                                                    children: model.description
                                                                })
                                                            ]
                                                        })
                                                    ]
                                                })
                                            }, index))
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                        mt: 8,
                                        p: 4,
                                        borderRadius: "lg",
                                        bg: "gray.100",
                                        borderLeft: "4px solid",
                                        borderColor: "blue.400",
                                        maxW: "3xl",
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                            fontSize: "md",
                                            color: "blue.500",
                                            textAlign: "center",
                                            children: t("llm_support_notes")
                                        })
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "blue.50",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 8,
                                align: "center",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: "xl",
                                        bgGradient: "linear(to-r, blue.400, purple.500)",
                                        bgClip: "text",
                                        textAlign: "center",
                                        children: t("target_audience_title")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: "xl",
                                        color: "gray.600",
                                        maxW: "3xl",
                                        textAlign: "center",
                                        mb: 8,
                                        children: t("target_audience_desc")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 3
                                        },
                                        spacing: 8,
                                        width: "100%",
                                        children: [
                                            {
                                                title: t("target_audience_students"),
                                                desc: t("target_audience_students_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserGraduate
                                            },
                                            {
                                                title: t("target_audience_professionals"),
                                                desc: t("target_audience_professionals_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserTie
                                            },
                                            {
                                                title: t("target_audience_creatives"),
                                                desc: t("target_audience_creatives_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaPalette
                                            }
                                        ].map((item, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                bg: "white",
                                                p: 8,
                                                borderRadius: "xl",
                                                boxShadow: "xl",
                                                _hover: {
                                                    transform: "translateY(-4px)",
                                                    boxShadow: "2xl"
                                                },
                                                transition: "all 0.3s",
                                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                                    spacing: 4,
                                                    align: "start",
                                                    children: [
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                            as: item.icon,
                                                            w: 10,
                                                            h: 10,
                                                            color: "blue.500"
                                                        }),
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                            size: "md",
                                                            children: item.title
                                                        }),
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                            color: "gray.600",
                                                            children: item.desc
                                                        })
                                                    ]
                                                })
                                            }, index))
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "gray.50",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: 8,
                                align: "center",
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: "xl",
                                        bgGradient: "linear(to-r, blue.400, purple.500)",
                                        bgClip: "text",
                                        textAlign: "center",
                                        children: t("use_cases_title")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: "xl",
                                        color: "gray.600",
                                        maxW: "3xl",
                                        textAlign: "center",
                                        mb: 8,
                                        children: t("use_cases_desc")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 2,
                                            lg: 3
                                        },
                                        spacing: 8,
                                        width: "100%",
                                        children: [
                                            {
                                                title: t("use_case_1_title"),
                                                desc: t("use_case_1_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGraduationCap
                                            },
                                            {
                                                title: t("use_case_2_title"),
                                                desc: t("use_case_2_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaChartLine
                                            },
                                            {
                                                title: t("use_case_3_title"),
                                                desc: t("use_case_3_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb
                                            },
                                            {
                                                title: t("use_case_4_title"),
                                                desc: t("use_case_4_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaSearch
                                            },
                                            {
                                                title: t("use_case_5_title"),
                                                desc: t("use_case_5_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUserAlt
                                            },
                                            {
                                                title: t("use_case_6_title"),
                                                desc: t("use_case_6_desc"),
                                                icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUsers
                                            }
                                        ].map((item, index)=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                bg: "white",
                                                p: 8,
                                                borderRadius: "xl",
                                                boxShadow: "xl",
                                                _hover: {
                                                    transform: "translateY(-4px)",
                                                    boxShadow: "2xl"
                                                },
                                                transition: "all 0.3s",
                                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                                    spacing: 4,
                                                    align: "start",
                                                    children: [
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {
                                                            as: item.icon,
                                                            w: 10,
                                                            h: 10,
                                                            color: "blue.500"
                                                        }),
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                            size: "md",
                                                            children: item.title
                                                        }),
                                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                            color: "gray.600",
                                                            children: item.desc
                                                        })
                                                    ]
                                                })
                                            }, index))
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_CaseStudies__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                            caseStudies: [
                                {
                                    icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGraduationCap,
                                    title: t("case_study_1_title", "Educational Institution Transformation"),
                                    industry: t("case_study_1_industry", "Higher Education"),
                                    challenge: t("case_study_1_challenge", "A leading university struggled with helping students visualize complex concepts across multiple disciplines, resulting in lower comprehension and retention rates."),
                                    solution: t("case_study_1_solution", "Implemented a comprehensive suite of FunBlocks AI tools across departments: MarzanoBrain and BloomBrain for creating structured learning materials, Slides for generating interactive presentations, and Brainstorming, Critical Thinking, and Creative Thinking tools to develop students' cognitive abilities."),
                                    results: t("case_study_1_results", "Significant improvement in student comprehension and retention rates. Students demonstrated enhanced critical thinking skills and ability to connect concepts across disciplines. Faculty reported more engaging classroom discussions and higher quality student work."),
                                    imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png"
                                },
                                {
                                    icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLightbulb,
                                    title: t("case_study_2_title", "Innovation Thinking Enhancement"),
                                    industry: t("case_study_2_industry", "Product Design & Marketing"),
                                    challenge: t("case_study_2_challenge", "A multinational company struggled with fostering innovative thinking among their product design and marketing teams, resulting in predictable solutions and declining market differentiation."),
                                    solution: t("case_study_2_solution", "Integrated FunBlocks AI Brainstorming, MindKit, MindSnap, OKR Assistant, Task Planner and other FunBlocks AI tools into their ideation process. Teams used these tools to explore multiple perspectives, challenge assumptions, and visualize connections between seemingly unrelated concepts."),
                                    results: t("case_study_2_results", "Teams developed more innovative product designs and marketing campaigns that resonated with customers. The company reported increased creative output, more diverse solution sets, and improved cross-team collaboration on complex projects."),
                                    imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_eisenhower_matrix.png"
                                }
                            ],
                            description: t("case_studies_description", "See how organizations across different sectors have leveraged FunBlocks AI tools to solve real challenges and achieve measurable results."),
                            appname: "FunBlocks AI",
                            onClick: ()=>document.getElementById("ai_tools").scrollIntoView({
                                    behavior: "smooth"
                                })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_ResearchBacked__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
                            title: t("research_title", "Research-Backed Approach"),
                            description: t("research_description", "Our tools are built on solid scientific foundations and proven cognitive principles to maximize learning and productivity."),
                            researchAreas: [
                                {
                                    title: t("research_area_1_title", "Cognitive Load Theory"),
                                    description: t("research_area_1_description", "Our visual tools reduce cognitive load by organizing information spatially, allowing users to process complex concepts more efficiently while minimizing mental effort."),
                                    icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBrain,
                                    color: "blue",
                                    citations: [
                                        {
                                            text: "Sweller, J., van Merri\xebnboer, J. J. G., & Paas, F. (2019). Cognitive Architecture and Instructional Design: 20 Years Later. Educational Psychology Review, 31(2), 261-292.",
                                            url: "https://doi.org/10.1007/s10648-019-09465-5"
                                        },
                                        {
                                            text: "Mayer, R. E. (2014). The Cambridge Handbook of Multimedia Learning (2nd ed.). Cambridge University Press.",
                                            url: "https://doi.org/10.1017/CBO9781139547369"
                                        }
                                    ]
                                },
                                {
                                    title: t("research_area_2_title", "Visual Learning Efficacy"),
                                    description: t("research_area_2_description", "Research shows visual learning can improve understanding by up to 400% and retention by 38% compared to text-only learning, making complex information more accessible and memorable."),
                                    icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaImage,
                                    color: "green",
                                    citations: [
                                        {
                                            text: "Medina, J. (2014). Brain Rules: 12 Principles for Surviving and Thriving at Work, Home, and School (2nd ed.). Pear Press.",
                                            url: "https://brainrules.net/brain-rules/"
                                        },
                                        {
                                            text: "Paivio, A. (2014). Mind and Its Evolution: A Dual Coding Theoretical Approach. Psychology Press.",
                                            url: "https://doi.org/10.4324/9781315785233"
                                        },
                                        {
                                            text: "Mayer, R. E., & Moreno, R. (2003). Nine Ways to Reduce Cognitive Load in Multimedia Learning. Educational Psychologist, 38(1), 43-52.",
                                            url: "https://doi.org/10.1207/S15326985EP3801_6"
                                        }
                                    ]
                                },
                                {
                                    title: t("research_area_3_title", "Mental Models & Frameworks"),
                                    description: t("research_area_3_description", "Our tools leverage established mental models and educational frameworks that help structure thinking, improve problem-solving capabilities, and enhance conceptual understanding across disciplines."),
                                    icon: react_icons_md__WEBPACK_IMPORTED_MODULE_10__.MdInsights,
                                    color: "orange",
                                    citations: [
                                        {
                                            text: "Johnson-Laird, P. N. (2010). Mental models and human reasoning. Proceedings of the National Academy of Sciences, 107(43), 18243-18250.",
                                            url: "https://doi.org/10.1073/pnas.1012933107"
                                        },
                                        {
                                            text: "Gentner, D., & Stevens, A. L. (Eds.). (2014). Mental Models. Psychology Press.",
                                            url: "https://doi.org/10.4324/9781315802725"
                                        },
                                        {
                                            text: "Krathwohl, D. R. (2002). A Revision of Bloom's Taxonomy: An Overview. Theory Into Practice, 41(4), 212-218.",
                                            url: "https://doi.org/10.1207/s15430421tip4104_2"
                                        }
                                    ]
                                },
                                {
                                    title: t("research_area_4_title", "AI-Enhanced Learning"),
                                    description: t("research_area_4_description", "Studies show that AI-assisted learning tools can personalize the educational experience, provide adaptive feedback, and improve outcomes across diverse learning styles and contexts."),
                                    icon: react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRobot,
                                    color: "purple",
                                    citations: [
                                        {
                                            text: "UNESCO. (2021). AI and education: Guidance for policy-makers. UNESCO Digital Library.",
                                            url: "https://unesdoc.unesco.org/ark:/48223/pf0000376709"
                                        },
                                        {
                                            text: "Holmes, W., Bialik, M., & Fadel, C. (2019). Artificial Intelligence in Education: Promises and Implications for Teaching and Learning. Center for Curriculum Redesign.",
                                            url: "https://curriculumredesign.org/our-work/artificial-intelligence-in-education/"
                                        },
                                        {
                                            text: "Zawacki-Richter, O., Mar\xedn, V. I., Bond, M., & Gouverneur, F. (2019). Systematic review of research on artificial intelligence applications in higher education – where are the educators? International Journal of Educational Technology in Higher Education, 16, 39.",
                                            url: "https://doi.org/10.1186/s41239-019-0171-0"
                                        }
                                    ]
                                }
                            ],
                            bg: "blue.50"
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Testimonials__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
                            appname: "FunBlocks AI",
                            rating: "4.8",
                            users: "20,000+",
                            testimonials: [
                                {
                                    content: t("testimonial_1_content", "MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!"),
                                    author: "Emily Johnson",
                                    role: t("testimonial_1_role", "Medical Student"),
                                    organization: "Stanford University",
                                    rating: 5,
                                    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                                },
                                {
                                    content: t("testimonial_2_content", "As a product manager, I need to communicate complex ideas to different teams. The AI Infographic Generator has become my secret weapon for creating visually stunning concept maps and comparison charts in minutes instead of hours."),
                                    author: "Michael Chen",
                                    role: t("testimonial_2_role", "Product Manager"),
                                    organization: "Salesforce",
                                    rating: 5,
                                    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                                },
                                {
                                    content: t("testimonial_3_content", "I've tried many brainstorming tools, but FunBlocks AI Brainstorming Assistant is in a league of its own. It doesn't just generate ideas - it helps structure and refine them in ways I wouldn't have considered. It's like having a creative thinking partner available 24/7."),
                                    author: "Sarah Williams",
                                    role: t("testimonial_3_role", "Creative Director"),
                                    organization: "Design Studio NYC",
                                    rating: 4,
                                    image: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                                }
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Section__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                            bg: "gray.50",
                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                spacing: {
                                    base: 6,
                                    md: 8
                                },
                                align: "center",
                                px: {
                                    base: 3,
                                    md: 0
                                },
                                children: [
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                        size: {
                                            base: "lg",
                                            md: "xl"
                                        },
                                        bgGradient: "linear(to-r, blue.400, purple.500)",
                                        bgClip: "text",
                                        textAlign: "center",
                                        lineHeight: {
                                            base: "1.3",
                                            md: "1.2"
                                        },
                                        children: t("common_questions")
                                    }),
                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                        fontSize: {
                                            base: "md",
                                            md: "xl"
                                        },
                                        color: "gray.600",
                                        maxW: "3xl",
                                        textAlign: "center",
                                        mb: {
                                            base: 4,
                                            md: 6
                                        },
                                        px: {
                                            base: 2,
                                            md: 0
                                        },
                                        lineHeight: "1.6",
                                        children: t("faq_description", "Find answers to commonly asked questions about FunBlocks AI tools and how they can help you.")
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 2
                                        },
                                        spacing: {
                                            base: 6,
                                            md: 8
                                        },
                                        width: "100%",
                                        maxW: "5xl",
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                        size: {
                                                            base: "sm",
                                                            md: "md"
                                                        },
                                                        mb: {
                                                            base: 3,
                                                            md: 4
                                                        },
                                                        color: "blue.600",
                                                        px: {
                                                            base: 1,
                                                            md: 0
                                                        },
                                                        children: t("faq_category_general", "About FunBlocks AI")
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {
                                                        allowMultiple: true,
                                                        children: [
                                                            faqs[0],
                                                            faqs[1],
                                                            faqs[2],
                                                            faqs[8],
                                                            faqs[9]
                                                        ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
                                                                mb: {
                                                                    base: 3,
                                                                    md: 4
                                                                },
                                                                border: "none",
                                                                bg: "white",
                                                                borderRadius: "lg",
                                                                boxShadow: "md",
                                                                _hover: {
                                                                    boxShadow: "lg"
                                                                },
                                                                transition: "all 0.3s",
                                                                children: [
                                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionButton, {
                                                                        pt: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        pb: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 4
                                                                        },
                                                                        _hover: {
                                                                            bg: "transparent"
                                                                        },
                                                                        borderRadius: "lg",
                                                                        role: "group",
                                                                        children: [
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                                                flex: "1",
                                                                                textAlign: "left",
                                                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                                    size: {
                                                                                        base: "xs",
                                                                                        md: "sm"
                                                                                    },
                                                                                    color: "blue.600",
                                                                                    fontWeight: "semibold",
                                                                                    lineHeight: "1.4",
                                                                                    _groupHover: {
                                                                                        color: "blue.700"
                                                                                    },
                                                                                    children: faq.question
                                                                                })
                                                                            }),
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionIcon, {
                                                                                color: "blue.500",
                                                                                _groupHover: {
                                                                                    transform: "rotate(180deg)"
                                                                                },
                                                                                transition: "transform 0.2s"
                                                                            })
                                                                        ]
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionPanel, {
                                                                        pb: {
                                                                            base: 4,
                                                                            md: 6
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 6
                                                                        },
                                                                        pt: {
                                                                            base: 1,
                                                                            md: 2
                                                                        },
                                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                            color: "gray.600",
                                                                            whiteSpace: "pre-wrap",
                                                                            fontSize: {
                                                                                base: "sm",
                                                                                md: "md"
                                                                            },
                                                                            lineHeight: "1.6",
                                                                            children: faq.answer
                                                                        })
                                                                    })
                                                                ]
                                                            }, index))
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                        size: {
                                                            base: "sm",
                                                            md: "md"
                                                        },
                                                        mb: {
                                                            base: 3,
                                                            md: 4
                                                        },
                                                        color: "purple.600",
                                                        px: {
                                                            base: 1,
                                                            md: 0
                                                        },
                                                        children: t("faq_category_features", "Tools & Features")
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {
                                                        allowMultiple: true,
                                                        children: [
                                                            faqs[3],
                                                            faqs[4],
                                                            faqs[5],
                                                            faqs[6],
                                                            faqs[7]
                                                        ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
                                                                mb: {
                                                                    base: 3,
                                                                    md: 4
                                                                },
                                                                border: "none",
                                                                bg: "white",
                                                                borderRadius: "lg",
                                                                boxShadow: "md",
                                                                _hover: {
                                                                    boxShadow: "lg"
                                                                },
                                                                transition: "all 0.3s",
                                                                children: [
                                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionButton, {
                                                                        pt: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        pb: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 4
                                                                        },
                                                                        _hover: {
                                                                            bg: "transparent"
                                                                        },
                                                                        borderRadius: "lg",
                                                                        role: "group",
                                                                        children: [
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                                                flex: "1",
                                                                                textAlign: "left",
                                                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                                    size: {
                                                                                        base: "xs",
                                                                                        md: "sm"
                                                                                    },
                                                                                    color: "purple.600",
                                                                                    fontWeight: "semibold",
                                                                                    lineHeight: "1.4",
                                                                                    _groupHover: {
                                                                                        color: "purple.700"
                                                                                    },
                                                                                    children: faq.question
                                                                                })
                                                                            }),
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionIcon, {
                                                                                color: "purple.500",
                                                                                _groupHover: {
                                                                                    transform: "rotate(180deg)"
                                                                                },
                                                                                transition: "transform 0.2s"
                                                                            })
                                                                        ]
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionPanel, {
                                                                        pb: {
                                                                            base: 4,
                                                                            md: 6
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 6
                                                                        },
                                                                        pt: {
                                                                            base: 1,
                                                                            md: 2
                                                                        },
                                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                            color: "gray.600",
                                                                            whiteSpace: "pre-wrap",
                                                                            fontSize: {
                                                                                base: "sm",
                                                                                md: "md"
                                                                            },
                                                                            lineHeight: "1.6",
                                                                            children: faq.answer
                                                                        })
                                                                    })
                                                                ]
                                                            }, index))
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {
                                        columns: {
                                            base: 1,
                                            md: 2
                                        },
                                        spacing: {
                                            base: 6,
                                            md: 8
                                        },
                                        width: "100%",
                                        maxW: "5xl",
                                        mt: {
                                            base: 4,
                                            md: 8
                                        },
                                        children: [
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                        size: {
                                                            base: "sm",
                                                            md: "md"
                                                        },
                                                        mb: {
                                                            base: 3,
                                                            md: 4
                                                        },
                                                        color: "green.600",
                                                        px: {
                                                            base: 1,
                                                            md: 0
                                                        },
                                                        children: t("faq_category_usage", "Use Cases & Applications")
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {
                                                        allowMultiple: true,
                                                        children: [
                                                            faqs[10],
                                                            faqs[11],
                                                            faqs[12],
                                                            faqs[13],
                                                            faqs[14]
                                                        ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionItem, {
                                                                mb: {
                                                                    base: 3,
                                                                    md: 4
                                                                },
                                                                border: "none",
                                                                bg: "white",
                                                                borderRadius: "lg",
                                                                boxShadow: "md",
                                                                _hover: {
                                                                    boxShadow: "lg"
                                                                },
                                                                transition: "all 0.3s",
                                                                children: [
                                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionButton, {
                                                                        pt: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        pb: {
                                                                            base: 3,
                                                                            md: 3
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 4
                                                                        },
                                                                        _hover: {
                                                                            bg: "transparent"
                                                                        },
                                                                        borderRadius: "lg",
                                                                        role: "group",
                                                                        children: [
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                                                flex: "1",
                                                                                textAlign: "left",
                                                                                children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                                    size: {
                                                                                        base: "xs",
                                                                                        md: "sm"
                                                                                    },
                                                                                    color: "green.600",
                                                                                    fontWeight: "semibold",
                                                                                    lineHeight: "1.4",
                                                                                    _groupHover: {
                                                                                        color: "green.700"
                                                                                    },
                                                                                    children: faq.question
                                                                                })
                                                                            }),
                                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionIcon, {
                                                                                color: "green.500",
                                                                                _groupHover: {
                                                                                    transform: "rotate(180deg)"
                                                                                },
                                                                                transition: "transform 0.2s"
                                                                            })
                                                                        ]
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.AccordionPanel, {
                                                                        pb: {
                                                                            base: 4,
                                                                            md: 6
                                                                        },
                                                                        px: {
                                                                            base: 3,
                                                                            md: 6
                                                                        },
                                                                        pt: {
                                                                            base: 1,
                                                                            md: 2
                                                                        },
                                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                            color: "gray.600",
                                                                            whiteSpace: "pre-wrap",
                                                                            fontSize: {
                                                                                base: "sm",
                                                                                md: "md"
                                                                            },
                                                                            lineHeight: "1.6",
                                                                            children: faq.answer
                                                                        })
                                                                    })
                                                                ]
                                                            }, index))
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {
                                                width: "100%",
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                        size: {
                                                            base: "sm",
                                                            md: "md"
                                                        },
                                                        mb: {
                                                            base: 3,
                                                            md: 4
                                                        },
                                                        color: "orange.600",
                                                        px: {
                                                            base: 1,
                                                            md: 0
                                                        },
                                                        children: t("faq_category_technical", "Technical & Support")
                                                    }),
                                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {
                                                        spacing: {
                                                            base: 3,
                                                            md: 4
                                                        },
                                                        align: "start",
                                                        bg: "orange.50",
                                                        p: {
                                                            base: 4,
                                                            md: 6
                                                        },
                                                        borderRadius: "lg",
                                                        borderLeft: "4px solid",
                                                        borderColor: "orange.400",
                                                        children: [
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {
                                                                size: {
                                                                    base: "xs",
                                                                    md: "sm"
                                                                },
                                                                color: "orange.600",
                                                                lineHeight: "1.4",
                                                                children: t("faq_need_more_help", "Need More Help?")
                                                            }),
                                                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {
                                                                color: "gray.600",
                                                                fontSize: {
                                                                    base: "sm",
                                                                    md: "md"
                                                                },
                                                                lineHeight: "1.6",
                                                                children: t("faq_support_text", "If you have questions not covered here, our support team is ready to help. You can also check our detailed documentation for more information.")
                                                            }),
                                                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {
                                                                gap: {
                                                                    base: 3,
                                                                    md: 4
                                                                },
                                                                flexWrap: {
                                                                    base: "wrap",
                                                                    sm: "nowrap"
                                                                },
                                                                width: "100%",
                                                                children: [
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                                                        leftIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaQuestionCircle, {}),
                                                                        colorScheme: "orange",
                                                                        variant: "outline",
                                                                        size: {
                                                                            base: "sm",
                                                                            md: "md"
                                                                        },
                                                                        as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                                                        href: "https://discord.gg/XtdZFBy4uR",
                                                                        isExternal: true,
                                                                        width: {
                                                                            base: "100%",
                                                                            sm: "auto"
                                                                        },
                                                                        children: t("contact_support", "Contact Support")
                                                                    }),
                                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {
                                                                        leftIcon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBook, {}),
                                                                        colorScheme: "blue",
                                                                        variant: "outline",
                                                                        size: {
                                                                            base: "sm",
                                                                            md: "md"
                                                                        },
                                                                        as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link,
                                                                        href: "https://www.funblocks.net/aiflow",
                                                                        isExternal: true,
                                                                        width: {
                                                                            base: "100%",
                                                                            sm: "auto"
                                                                        },
                                                                        children: t("view_documentation", "View Documentation")
                                                                    })
                                                                ]
                                                            })
                                                        ]
                                                    })
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_common_Footer__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {})
                    ]
                })
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainFrame);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ })

};
;