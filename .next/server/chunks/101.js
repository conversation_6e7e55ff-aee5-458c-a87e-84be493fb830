exports.id = 101;
exports.ids = [101];
exports.modules = {

/***/ 5655:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3620);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _utils_svgUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(9399);
/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(3135);
/* harmony import */ var _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1082);
/* harmony import */ var _MermaidRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6736);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7426);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_markdown__WEBPACK_IMPORTED_MODULE_5__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__, _MermaidRenderer__WEBPACK_IMPORTED_MODULE_7__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_markdown__WEBPACK_IMPORTED_MODULE_5__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__, _MermaidRenderer__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);










const ArtifactDisplay = ({ artifact , svgRef =(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) , shadow , app  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)("common");
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast)();
    const [svgContent, setSvgContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const fullscreenContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const [isSvgRendered, setIsSvgRendered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const { openShareModal  } = (0,_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_6__/* .useShareModal */ .K)();
    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);
    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        x: 0,
        y: 0
    });
    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        x: 0,
        y: 0
    });
    const [isMobile] = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)("(max-width: 768px)");
    const handleFullscreen = ()=>{
        if (!isFullscreen) {
            const elem = fullscreenContainerRef.current;
            if (elem.requestFullscreen) {
                elem.requestFullscreen();
            } else if (elem.webkitRequestFullscreen) {
                elem.webkitRequestFullscreen();
            } else if (elem.msRequestFullscreen) {
                elem.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
        setIsFullscreen(!isFullscreen);
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const handleFullscreenChange = ()=>{
            const isEnteringFullscreen = !!document.fullscreenElement;
            setIsFullscreen(isEnteringFullscreen);
            setScale(1);
            setPosition({
                x: 0,
                y: 0
            });
            const svgElement = svgRef.current.querySelector("svg");
            if (!svgElement) return;
            // 重置变换
            svgElement.style.transform = "translate(0px, 0px) scale(1)";
            if (isEnteringFullscreen) {
                svgElement.parentElement.style.display = "flex";
                svgElement.parentElement.style.justifyContent = "center";
                svgElement.parentElement.style.alignItems = "center";
                svgElement.parentElement.style.width = "100%";
                svgElement.parentElement.style.height = "100vh";
                svgRef.current.style.width = "100%";
            } else {
                // 退出全屏时重置所有样式
                svgElement.style.width = "100%";
                svgElement.style.height = "100%";
                svgElement.parentElement.style.margin = "0";
                svgElement.parentElement.style.width = "100%";
                svgElement.parentElement.style.height = "-webkit-fill-available";
                svgRef.current.style.height = "100%";
                // 重新调用调整高度的函数
                adjustSvgContainerHeight();
            }
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return ()=>{
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, [
        svgRef
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (artifact?.type === "svg" && artifact?.content) {
            try {
                setSvgContent((0,_utils_svgUtils__WEBPACK_IMPORTED_MODULE_9__/* .preprocessSvg */ .w)(artifact.content));
                setError(null);
            } catch (err) {
                console.error("Error setting initial SVG:", err);
                setError("无法加载SVG内容");
            }
        }
    }, [
        artifact
    ]);
    const adjustSvgContainerHeight = ()=>{
        const svgElement = svgRef?.current?.querySelector("svg");
        if (svgElement) {
            if (isFullscreen) {
                svgRef.current.style.height = "100%";
                return;
            }
            const svgDimensions = (0,_utils_svgUtils__WEBPACK_IMPORTED_MODULE_9__/* .getSvgDimensions */ .j)(svgContent);
            const viewBox = svgElement.getAttribute("viewBox");
            const parentMaxWidth = parseInt(svgRef.current.parentElement.style.maxWidth?.replace("px", "")) || 0;
            const maxWidth = Math.min(window.innerWidth, parentMaxWidth);
            console.log("max width............", maxWidth, window.innerWidth, parentMaxWidth);
            let width = svgDimensions?.width || viewBox.split(" ")[2];
            let height = svgDimensions?.height || viewBox.split(" ")[3];
            if (width) {
                let aspectRatio = height / width;
                if (width > maxWidth * 0.9) {
                    width = maxWidth * 0.9;
                    svgRef.current.style.width = `${width}px`;
                }
                height = width * aspectRatio;
                svgRef.current.style.height = `${height}px`;
            } else {
                svgRef.current.style.height = "fit-content";
            }
            svgElement.style.width = "-webkit-fill-available";
        // console.log('svgRef.current.style.height..............', svgRef.current.style.height, svgDimensions, viewBox);
        }
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (!svgRef?.current?.querySelector("svg")) return;
        adjustSvgContainerHeight();
    }, [
        isFullscreen
    ]);
    const onSvgRendered = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        adjustSvgContainerHeight();
        setIsSvgRendered(true);
    // 在这里可以添加其他需要在 SVG 渲染后执行的操作
    }, []);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (svgContent) {
            const checkSvgRendered = ()=>{
                const svgElement = svgRef.current?.querySelector("svg");
                if (svgElement) {
                    onSvgRendered();
                } else {
                    requestAnimationFrame(checkSvgRendered);
                }
            };
            checkSvgRendered();
        }
    }, [
        svgContent,
        onSvgRendered
    ]);
    const getWittyInsightsStyle = ()=>{
        if (artifact?.promptId === "witty_insights") {
            return {
                background: "linear-gradient(180deg, #009fff 0%, #f0f8ff 100%)",
                borderRadius: "2px",
                padding: "2rem",
                margin: 6,
                boxShadow: "0 8px 16px rgba(31, 38, 135, 0.15)",
                transition: "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",
                _hover: {
                    transform: "translateY(-4px)",
                    boxShadow: "0 12px 24px rgba(31, 38, 135, 0.2)"
                }
            };
        }
        return {};
    };
    const handleZoom = (type)=>{
        const svgElement = svgRef.current.querySelector("svg");
        if (!svgElement) return;
        let newScale;
        if (type === "in") {
            newScale = scale + 0.1;
        } else {
            newScale = Math.max(0.1, scale - 0.1);
        }
        svgElement.style.transform = `translate(${position.x}px, ${position.y}px) scale(${newScale})`;
        setScale(newScale);
    };
    const handleMouseDown = (e)=>{
        if (!isFullscreen) return;
        setIsDragging(true);
        setDragStart({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
    };
    const handleMouseMove = (e)=>{
        if (!isDragging || !isFullscreen) return;
        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;
        setPosition({
            x: newX,
            y: newY
        });
        const svgElement = svgRef.current.querySelector("svg");
        if (svgElement) {
            svgElement.style.transform = `translate(${newX}px, ${newY}px) scale(${scale})`;
        }
    };
    const handleMouseUp = ()=>{
        setIsDragging(false);
    };
    if (error) {
        return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
            color: "red.500",
            children: t("error_loading_svg")
        });
    }
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const options = {
            root: null,
            rootMargin: "0px",
            threshold: 0.01 // 降低阈值，使其更容易触发
        };
        const observer = new IntersectionObserver((entries)=>{
            entries.forEach((entry)=>{
                if (entry.isIntersecting) {
                    adjustSvgContainerHeight();
                }
            });
        }, options);
        if (containerRef.current) {
            observer.observe(containerRef.current);
        }
        return ()=>{
            if (containerRef.current) {
                observer.unobserve(containerRef.current);
            }
        };
    }, []);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {
        spacing: 4,
        align: "center",
        width: "100%",
        ref: containerRef,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {
            ref: fullscreenContainerRef,
            width: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: shadow ? "0 6px 8px rgba(0, 0, 0, 0.15)" : "none",
            borderRadius: "lg",
            overflow: "hidden",
            backgroundColor: "white",
            padding: 2,
            style: {
                position: "relative",
                maxWidth: isMobile ? window.innerWidth : 800
            },
            id: `artifact-content-${artifact?._id}`,
            onMouseEnter: ()=>setIsHovered(true),
            onMouseLeave: ()=>setIsHovered(false),
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    ref: svgRef,
                    style: {
                        width: "100%",
                        height: "fit-content",
                        minWidth: 300,
                        display: "flex",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        flexDirection: "column",
                        overflow: "hidden",
                        cursor: isFullscreen ? isDragging ? "grabbing" : "grab" : "default",
                        ...getWittyInsightsStyle()
                    },
                    onMouseDown: handleMouseDown,
                    onMouseMove: handleMouseMove,
                    onMouseUp: handleMouseUp,
                    onMouseLeave: handleMouseUp,
                    children: [
                        artifact.type === "mermaid" && !isFullscreen && !!artifact.title && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            height: "-moz-min-content",
                            width: "100%",
                            fontWeight: "500",
                            color: "#555",
                            children: artifact.title
                        }),
                        artifact.imageUrl && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                            src: artifact.imageUrl,
                            alt: artifact.title || "Generated image",
                            style: {
                                maxWidth: "100%",
                                maxHeight: "100%",
                                objectFit: "contain",
                                borderRadius: 16
                            }
                        }),
                        artifact.type === "svg" && (svgContent ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            dangerouslySetInnerHTML: {
                                __html: svgContent
                            },
                            style: {
                                width: "100%",
                                height: "-webkit-fill-available",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center"
                            }
                        }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {
                            children: t("loading")
                        })),
                        artifact.type === "markdown" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            style: {
                                width: "100%",
                                padding: "4px",
                                overflow: "auto",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "flex-start"
                            },
                            className: "markdown-content",
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_markdown__WEBPACK_IMPORTED_MODULE_5__["default"], {
                                children: artifact.content
                            })
                        }),
                        artifact.type === "mermaid" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_MermaidRenderer__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
                            id: "mermaid-chart-" + artifact._id,
                            source: artifact.content
                        })
                    ]
                }),
                isHovered && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {
                    position: "absolute",
                    top: 2,
                    right: 2,
                    spacing: 1,
                    children: [
                        isFullscreen && !isMobile && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                    label: t("zoom_out"),
                                    placement: "bottom",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.ZoomOut, {
                                            size: 24
                                        }),
                                        "aria-label": t("zoom_out"),
                                        size: "sm",
                                        onClick: ()=>handleZoom("out")
                                    })
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                                    label: t("zoom_in"),
                                    placement: "bottom",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.ZoomIn, {
                                            size: 24
                                        }),
                                        "aria-label": t("zoom_in"),
                                        size: "sm",
                                        onClick: ()=>handleZoom("in")
                                    })
                                })
                            ]
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                            label: t(isFullscreen ? "exit_fullscreen_tooltip" : "fullscreen_tooltip"),
                            placement: isFullscreen ? "bottom" : "top",
                            children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {
                                icon: isFullscreen ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.FullscreenExit, {
                                    size: 28
                                }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__.Fullscreen, {
                                    size: 28
                                }),
                                "aria-label": t("fullscreen_display"),
                                size: "sm",
                                onClick: handleFullscreen
                            })
                        })
                    ]
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ArtifactDisplay);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 6736:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var mermaid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1024);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([mermaid__WEBPACK_IMPORTED_MODULE_1__]);
mermaid__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];



mermaid__WEBPACK_IMPORTED_MODULE_1__["default"].initialize({
    startOnLoad: true,
    theme: "default",
    securityLevel: "loose"
});
const MermaidRenderer = ({ source , id  })=>{
    const mermaidRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{
        const initializeMermaid = async ()=>{
            if (mermaidRef.current) {
                mermaidRef.current.innerHTML = source;
                const { svg , bindFunctions  } = await mermaid__WEBPACK_IMPORTED_MODULE_1__["default"].render(`mermaid-diagram-${id}`, source);
                if (mermaidRef.current) {
                    mermaidRef.current.innerHTML = svg;
                    bindFunctions?.(mermaidRef.current);
                }
            }
        };
        initializeMermaid();
        // Clean up mermaid instance when unmounting; doing nothing at the momemt
        return ()=>{};
    }, [
        source
    ]);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        className: "fill-available",
        style: {
            padding: 10,
            height: "-webkit-fill-available",
            minHeight: "100%",
            width: "-webkit-fill-available",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center"
        },
        id: id,
        ref: mermaidRef
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MermaidRenderer);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 1692:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1082);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2210);
/* harmony import */ var react_share__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2017);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1301);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7426);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_share__WEBPACK_IMPORTED_MODULE_4__, react_icons_fa__WEBPACK_IMPORTED_MODULE_5__]);
([_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_share__WEBPACK_IMPORTED_MODULE_4__, react_icons_fa__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);









const ShareModal = ({ app  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)("common");
    const { isOpen , artifactId , title ="" , imageDataUrl , closeShareModal , tool  } = (0,_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_2__/* .useShareModal */ .K)();
    const [shareUrl, setShareUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useBreakpointValue)({
        base: true,
        md: false
    });
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_7___default()().publicRuntimeConfig;
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (false) {}
    }, [
        artifactId
    ]);
    const copyToClipboard = async ()=>{
        try {
            const textToCopy = shareUrl;
            await navigator.clipboard.writeText(textToCopy);
            toast({
                title: t("title_and_link_copied"),
                status: "success",
                duration: 3000,
                isClosable: true
            });
        } catch (error) {
            console.error("Failed to copy:", error);
            toast({
                title: t("copy_failed"),
                status: "error",
                duration: 3000,
                isClosable: true
            });
        }
    };
    const shareToInstagram = ()=>{
        copyToClipboard();
        toast({
            title: t("title_and_link_copied"),
            description: t("save_image_and_paste_to_instagram"),
            status: "success",
            duration: 3000,
            isClosable: true
        });
    };
    const ShareButton = ({ children  })=>/*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: "10px",
            cursor: "pointer",
            p: 2,
            borderRadius: "md",
            transition: "background-color 0.2s",
            _hover: {
                backgroundColor: "gray.100"
            },
            width: "100%",
            children: children
        });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {
        isOpen: isOpen,
        onClose: closeShareModal,
        size: isMobile ? "full" : "2xl",
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {}),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {
                        children: t("share_to_social_platforms")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Flex, {
                            direction: isMobile || [
                                _utils_constants__WEBPACK_IMPORTED_MODULE_8__/* .APP_TYPE.mindmap */ .IF.mindmap
                            ].includes(app) ? "column" : "row",
                            gap: 4,
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {
                                    flex: 1,
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Image, {
                                        src: imageDataUrl,
                                        alt: "Card preview",
                                        w: "100%",
                                        h: "auto",
                                        mb: 4,
                                        borderRadius: "md"
                                    })
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {
                                    align: "stretch",
                                    flex: 1,
                                    style: {
                                        display: [
                                            _utils_constants__WEBPACK_IMPORTED_MODULE_8__/* .APP_TYPE.mindmap */ .IF.mindmap
                                        ].includes(app) && !isMobile ? "flow" : "flex",
                                        flexDirection: [
                                            _utils_constants__WEBPACK_IMPORTED_MODULE_8__/* .APP_TYPE.mindmap */ .IF.mindmap
                                        ].includes(app) && !isMobile ? "row" : "column"
                                    },
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.TwitterShareButton, {
                                            url: shareUrl,
                                            title: title,
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.TwitterIcon, {
                                                        size: 32,
                                                        round: true
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_to_twitter")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.FacebookShareButton, {
                                            url: shareUrl,
                                            quote: `${title}\n${shareUrl}`,
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.FacebookIcon, {
                                                        size: 32,
                                                        round: true
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_to_facebook")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.LinkedinShareButton, {
                                            url: shareUrl,
                                            title: title,
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.LinkedinIcon, {
                                                        size: 32,
                                                        round: true
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_to_linkedin")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.WhatsappShareButton, {
                                            url: shareUrl,
                                            title: title,
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.WhatsappIcon, {
                                                        size: 32,
                                                        round: true
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_to_whatsapp")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {
                                            as: "button",
                                            onClick: shareToInstagram,
                                            gap: "10px",
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaInstagram, {
                                                        size: 32
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_to_instagram")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.EmailShareButton, {
                                            url: shareUrl,
                                            subject: title,
                                            body: `${title}\n\n${shareUrl}`,
                                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ShareButton, {
                                                children: [
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_share__WEBPACK_IMPORTED_MODULE_4__.EmailIcon, {
                                                        size: 32,
                                                        round: true
                                                    }),
                                                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                                                        children: t("share_via_email")
                                                    })
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
                                            onClick: copyToClipboard,
                                            width: "100%",
                                            style: {
                                                marginRight: 10,
                                                marginTop: 6
                                            },
                                            children: t("copy_title_and_link")
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
                                            as: "a",
                                            href: imageDataUrl,
                                            download: "card.png",
                                            width: "100%",
                                            style: {
                                                marginRight: 10,
                                                marginTop: 6
                                            },
                                            children: t("download_image")
                                        })
                                    ]
                                })
                            ]
                        })
                    })
                ]
            })
        ]
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShareModal);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 8002:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "t": () => (/* binding */ AIActionList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2210);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(3620);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _ColorMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3666);
/* harmony import */ var _styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9077);
/* harmony import */ var _styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7987);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_6__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, react_i18next__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);


// import { useIntl } from 'react-intl';





const ActionItem = ({ key , parentIndex , nodeId , listType , text , onClick , color_theme , deleteEnabled , onDeleteItem , expandEnabled , onExpand  })=>{
    // const intl = useIntl();
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)("common");
    const [hovered, setHovered] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const isSubItems = parentIndex != undefined;
    const itemRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
        label: t("deep_dive_to_topic"),
        "aria-label": "deep div to topic tooltip",
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            ref: itemRef,
            onClick: onClick,
            style: {
                border: `1px solid ${color_theme.border}`,
                backgroundColor: color_theme.content_bg,
                boxShadow: `5px 5px 0px rgba(0, 0, 0, 10%)`,
                borderRadius: 4,
                padding: "6px",
                paddingTop: 2,
                paddingBottom: 3,
                paddingLeft: 4,
                cursor: onClick ? "pointer" : undefined,
                fontSize: 13,
                display: "flow",
                flexDirection: "row",
                alignItems: "center",
                position: "relative"
            },
            onMouseEnter: ()=>setHovered(true),
            onMouseLeave: ()=>setHovered(false),
            children: [
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                    style: {
                        fontWeight: 500,
                        fontSize: 16
                    },
                    children: [
                        onClick ? isSubItems ? "-" : "+" : "",
                        " "
                    ]
                }),
                " ",
                text,
                hovered && expandEnabled && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {
                    label: t("expand_ideas"),
                    "aria-label": "expand ideas tooltip",
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: "circle-button",
                        style: {
                            position: "absolute",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            height: 20,
                            width: 20,
                            top: "50%",
                            transform: "translate(50%, -50%)",
                            right: 0,
                            borderColor: color_theme.border,
                            backgroundColor: color_theme.content_bg
                        },
                        onClick: (event)=>{
                            event.stopPropagation();
                            onExpand(itemRef?.current);
                        },
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__.FlowCascade, {
                            size: 15,
                            color: "dodgerblue"
                        })
                    })
                })
            ]
        }, key)
    });
};
const AIActionList = ({ key , nodeId , listType , list , parentIndex , action , queryType , nodeType , aiItemClicked , onExpand , onDeleteItem , expandEnabled , ai_generated , color_start =0 , color_theme  })=>{
    const isSubItems = parentIndex != undefined;
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        style: {
            display: "flex",
            flexDirection: "column",
            rowGap: 10,
            paddingRight: isSubItems ? undefined : 10,
            paddingLeft: !isSubItems ? undefined : 16,
            marginTop: isSubItems ? undefined : 5,
            marginBottom: isSubItems ? undefined : 5
        },
        children: [
            " ",
            list?.map((item, index)=>{
                if (!item) return;
                let text = item;
                let subItems;
                if (typeof text === "object") {
                    text = item.name;
                    subItems = item.branches;
                }
                let colorTheme = color_theme || _ColorMenu__WEBPACK_IMPORTED_MODULE_4__/* .node_color_themes */ .U[(index + color_start) % _ColorMenu__WEBPACK_IMPORTED_MODULE_4__/* .node_color_themes.length */ .U.length];
                text = text?.trim();
                if (!text) return;
                let itemEle = /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ActionItem, {
                    index: index,
                    parentIndex: parentIndex,
                    nodeId: nodeId,
                    listType: listType,
                    text: text,
                    color_theme: colorTheme,
                    expandEnabled: expandEnabled,
                    onExpand: !onExpand ? undefined : (anchor)=>{
                        // aiItemClicked({ item: { action: 'breakdown' }, nodeType, queryType: 'breakdown', userInput: text, color_theme });
                        onExpand(anchor, text);
                    },
                    onClick: !aiItemClicked ? undefined : ()=>{
                        aiItemClicked({
                            item: {
                                action: action || "query"
                            },
                            nodeType,
                            queryType,
                            userInput: text,
                            color_theme: colorTheme,
                            ai_generated
                        });
                    },
                    deleteEnabled: !!onDeleteItem,
                    onDeleteItem: onDeleteItem
                }, index + "");
                if (subItems) {
                    itemEle = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                        children: [
                            itemEle,
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AIActionList, {
                                list: subItems,
                                nodeId: nodeId,
                                listType: listType,
                                action: action,
                                queryType: queryType,
                                aiItemClicked: aiItemClicked,
                                onExpand: onExpand,
                                onDeleteItem: onDeleteItem,
                                expandEnabled: expandEnabled,
                                ai_generated: ai_generated,
                                parentIndex: index,
                                color_theme: colorTheme
                            }, index + "_sub")
                        ]
                    });
                }
                return itemEle;
            })
        ]
    }, key || nodeId);
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 8869:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export selector */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(8902);
/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4810);
/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9048);
/* harmony import */ var _mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2728);
/* harmony import */ var _AIActionList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(8002);
/* harmony import */ var _TodoList__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(435);
/* harmony import */ var _ColorMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3666);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(2210);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(7987);
/* harmony import */ var _styled_icons_entypo_Link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(8847);
/* harmony import */ var _styled_icons_entypo_Link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_entypo_Link__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _styled_icons_bootstrap_FileText__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(39);
/* harmony import */ var _styled_icons_bootstrap_FileText__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap_FileText__WEBPACK_IMPORTED_MODULE_12__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__, _xyflow_react__WEBPACK_IMPORTED_MODULE_5__, _AIActionList__WEBPACK_IMPORTED_MODULE_6__, _TodoList__WEBPACK_IMPORTED_MODULE_7__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__, react_i18next__WEBPACK_IMPORTED_MODULE_10__]);
([_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__, _xyflow_react__WEBPACK_IMPORTED_MODULE_5__, _AIActionList__WEBPACK_IMPORTED_MODULE_6__, _TodoList__WEBPACK_IMPORTED_MODULE_7__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__, react_i18next__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);













//- text (for detailed explanations, narrative content, or topics requiring coherent exposition)
//- mindmap (for hierarchical information, multi-connected concepts, or topics needing an overall framework)
//- todo_list (for tasks with clear steps or action items, especially those to be completed sequentially)
const selector = (state)=>({
        nodes: state.nodes,
        setNodes: state.setNodes,
        addSubNode: state.addSubNode,
        addNode: state.addNode,
        deleteNode: state.deleteNode,
        getNode: state.getNode,
        updateNode: state.updateNode,
        updateNodeData: state.updateNodeData,
        edges: state.edges,
        getNodeEdges: state.getNodeEdges
    });
const NodeTitle = ({ title , url , loading , info , color_theme , nodeType , queryType  })=>{
    const [isOverflown, setIsOverflown] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);
    const containerRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);
    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{
        if (containerRef.current) {
            setIsOverflown(containerRef.current.scrollHeight > containerRef.current.clientHeight);
        }
    }, [
        title
    ]);
    const styles = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>({
            container: {
                display: "-webkit-box",
                WebkitLineClamp: 2,
                whiteSpace: "pre-line",
                overflow: "hidden",
                textOverflow: "ellipsis",
                WebkitBoxOrient: "vertical"
            }
        }), [
        color_theme
    ]);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        style: {
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: color_theme.title_bg,
            borderBottomWidth: "1px",
            borderColor: color_theme.border,
            padding: "12px",
            paddingTop: 5,
            paddingBottom: 3,
            paddingLeft: (queryType === "link" || nodeType === "funblocks_doc") && 6 || 12,
            minHeight: 20,
            fontSize: 14,
            fontWeight: 500,
            borderTopLeftRadius: 3,
            borderTopRightRadius: 3
        },
        children: [
            (queryType === "link" || nodeType === "funblocks_doc") && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                style: {
                    width: 18,
                    height: 24,
                    marginRight: 4,
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center"
                },
                children: [
                    queryType === "link" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_entypo_Link__WEBPACK_IMPORTED_MODULE_11__.Link, {
                        size: 18,
                        color: color_theme.border
                    }),
                    nodeType === "funblocks_doc" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap_FileText__WEBPACK_IMPORTED_MODULE_12__.FileText, {
                        size: 18,
                        color: color_theme.border
                    })
                ]
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                ref: containerRef,
                style: styles.container,
                children: isOverflown ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {
                    label: title,
                    "aria-label": "expand title tooltip",
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        style: {
                            height: "fit-content"
                        },
                        children: title
                    })
                }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    children: title
                })
            }),
            loading && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx((_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_4___default()), {
                size: 18
            }),
            !loading && !!info && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", {
                style: {
                    fontSize: 13
                },
                children: [
                    "(",
                    info,
                    ")"
                ]
            })
        ]
    });
};
const AINode = ({ data , isConnectable , selected  })=>{
    const [ai_menu_anchor, set_ai_menu_anchor] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const [sub_menu_anchor, set_sub_menu_anchor] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)("common");
    const selectedItemRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);
    const contentContainerRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);
    const videoContainerRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);
    const [itemTargeted, setItemTargeted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);
    const [sub_menu_parent, set_sub_menu_parent] = react__WEBPACK_IMPORTED_MODULE_1__.useState(-1);
    const [sub_menu_items, set_sub_menu_items] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);
    const [sub_menu_item_targeted, set_sub_menu_item_targeted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);
    const [sub_menu_visible, set_sub_menu_visible] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);
    // const [avaliableAssistantItems, setAvaliableAssistantItems] = React.useState(assistant_items);
    // const [extendedAssistantItems, setExtendedAssistantItems] = React.useState([]);
    // const [filteredAssistantItems, setFilteredAssistantItems] = React.useState([]);
    // const [groupedAssistantItems, setGroupedAssistantItems] = React.useState([]);
    // const [drafter, setDrafter] = React.useState([]);
    // const [selectedText, setSelectedText] = React.useState();
    // const [selectedTopic, setSelectedTopic] = React.useState();
    const [aigc_hovered, set_aigc_hovered] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const [sub_menu_hovered, set_sub_menu_hovered] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const [menuDroppedDown, setMenuDroppedDown] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const [form, setForm] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const { zoom  } = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.useViewport)();
    const [isMobile] = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery)("(max-width: 768px)");
    const nodeId = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.useNodeId)();
    const nodes = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.useNodes)();
    const node = nodes.find((node)=>node.id === nodeId);
    const { nodeType , title , content  } = data;
    const defaultWidth = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>nodeType === "slides" || data.ai_action === "slideshow" ? 480 : 340, []);
    const color_theme = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>_ColorMenu__WEBPACK_IMPORTED_MODULE_8__/* .node_color_themes.find */ .U.find((theme)=>theme.id === data.color_theme) || _ColorMenu__WEBPACK_IMPORTED_MODULE_8__/* .node_color_themes.find */ .U.find((theme)=>theme.id === "blue"), [
        data.color_theme
    ]);
    const aiItemClicked = react__WEBPACK_IMPORTED_MODULE_1__.useCallback(()=>{
        const event = new CustomEvent("showAIFlowToast");
        window.dispatchEvent(event);
    }, []);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        // ref={nodeRef}
        className: "node",
        style: nodeType == "group" ? {
            display: "contents"
        } : {
            backgroundColor: color_theme.content_bg,
            borderColor: color_theme.border,
            boxShadow: `0px 0px 6px #ccc`,
            borderRadius: nodeType == "note" ? 0 : undefined,
            width: nodeType === "video" && 562 || node?.width || defaultWidth,
            height: nodeType === "video" && "fit-content" || undefined,
            minHeight: data.ai_action === "slideshow" ? defaultWidth * 9 / 16 : undefined,
            maxHeight: data.minimized ? 100 : undefined,
            overflowY: data.minimized ? "clip" : undefined,
            pointerEvents: "all"
        },
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "target",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Top,
                id: "TT"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "target",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Bottom,
                id: "BT"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "target",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Left,
                id: "LT"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "target",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Right,
                id: "RT"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "source",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Top,
                id: "a"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "source",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Right,
                id: "b"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "source",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Bottom,
                id: "c"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Handle, {
                type: "source",
                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_5__.Position.Left,
                id: "d"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
                    children: [
                        // !['brainstorming_perspective'].includes(data.ai_action) &&
                        ![
                            "image"
                        ].includes(nodeType) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(NodeTitle, {
                            nodeType: nodeType,
                            queryType: data.queryType,
                            // loading={nodeType === 'video' && context_loading}
                            title: title,
                            url: data.context?.url,
                            color_theme: color_theme
                        }),
                        !!data.vid && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("iframe", {
                            width: "560",
                            height: "315",
                            ref: videoContainerRef,
                            src: `https://www.youtube.com/embed/${data.vid}`,
                            title: "FunBlocks AI Video Player",
                            frameBorder: "0",
                            allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",
                            referrerPolicy: "strict-origin-when-cross-origin",
                            allowFullScreen: true
                        }),
                        !!data.content?.src && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                            src: data.content.src,
                            style: {
                                borderRadius: 4
                            }
                        }),
                        (data.queryType === "link" || [
                            "funblocks_doc"
                        ].includes(data.nodeType) || content && nodeType !== "image" || data.brainstorming_scenario || data.todos || data.items) && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            className: isMobile || !selected ? undefined : "nodrag",
                            style: {
                                margin: "12px",
                                marginRight: data.todos || data.items ? 5 : 12,
                                marginTop: 7,
                                marginBottom: 7,
                                fontSize: 14,
                                overflowY: "auto",
                                cursor: "text"
                            },
                            ref: contentContainerRef,
                            children: [
                                content && ![
                                    "breakdown",
                                    "brainstorming_perspective",
                                    "slideshow"
                                ].includes(data.ai_action) && !data.todos?.length && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z, {
                                    content: content
                                }),
                                !!data.brainstorming_scenario && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    style: {
                                        fontSize: 13,
                                        color: "GrayText"
                                    },
                                    children: data.brainstorming_scenario
                                }),
                                data.items && // ['breakdown'].includes(data.ai_action) &&
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_AIActionList__WEBPACK_IMPORTED_MODULE_6__/* .AIActionList */ .t, {
                                    nodeId: nodeId,
                                    list: data.items,
                                    queryType: "tell_more",
                                    // queryType={data.ai_action == 'breakdown' && 'tell_more' || data.queryType === 'brainstorming_insights' && 'tell_more' || undefined}
                                    // action={data.queryType === 'perspective' && 'breakdown' || undefined}
                                    aiItemClicked: aiItemClicked,
                                    expandEnabled: true,
                                    onExpand: (anchor, topic)=>{
                                        aiItemClicked();
                                    }
                                }),
                                data.todos && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_TodoList__WEBPACK_IMPORTED_MODULE_7__/* .TodoList */ .B, {
                                    nodeId: nodeId,
                                    list: data.todos,
                                    priorities: data.priorities,
                                    queryType: "task_breakdown",
                                    aiItemClicked: aiItemClicked,
                                    expandEnabled: true,
                                    onExpand: (anchor, topic)=>{
                                        aiItemClicked();
                                    },
                                    updateNodeData: (data)=>{
                                        aiItemClicked();
                                    },
                                    handleUpdatePriority: aiItemClicked
                                }),
                                ([
                                    "link"
                                ].includes(data.queryType) || [
                                    "funblocks_doc"
                                ].includes(data.nodeType)) && ![
                                    "prompt",
                                    "note"
                                ].includes(data.nodeType) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    className: "fill-available",
                                    style: {
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "flex-end"
                                    },
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        onClick: async ()=>{
                                            let url = data.context?.url;
                                            if (nodeType === "funblocks_doc") {
                                                url = `${window.location.origin}/#/editor?hid=${data.hid}&space=workspace`;
                                            }
                                            window.open(url, "_blank");
                                        },
                                        style: {
                                            color: color_theme.border,
                                            border: "none",
                                            padding: "8px 12px",
                                            cursor: "pointer",
                                            textAlign: "center"
                                        },
                                        children: t("to_original_page")
                                    })
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const styles = {
    container: {
        backgroundColor: "white",
        border: "1px solid gray",
        boxShadow: "0px 0px 8px #bbb",
        margin: "6px",
        marginLeft: 0,
        borderRadius: "4px"
    },
    contentSect: {
        fontSize: 13,
        color: "gray"
    },
    toolbarIcon: {
        width: 35,
        height: 35,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center"
    }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AINode);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 3666:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "U": () => (/* binding */ node_color_themes)
/* harmony export */ });
/* unused harmony export ColorMenu */
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5692);
/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);



// import { useIntl } from 'react-intl';
const node_color_themes = [
    {
        id: "orange",
        title_bg: "rgb(244, 182, 121)",
        content_bg: "rgb(255, 235, 220)",
        border: "#DC7633"
    },
    {
        id: "pink",
        title_bg: "rgb(245, 193, 192)",
        content_bg: "rgb(255, 243, 243)",
        border: "rgb(225, 164, 165)",
        borderShadow: "#E1A4A5"
    },
    {
        id: "gray",
        title_bg: "rgb(205, 205, 205)",
        content_bg: "rgb(248, 248, 245)",
        border: "#777"
    },
    {
        id: "yellow",
        title_bg: "rgb(234, 205, 99)",
        content_bg: "rgb(255, 251, 236)",
        border: "rgb(202, 169, 72)",
        borderShadow: "#CAA948"
    },
    {
        id: "green",
        title_bg: "rgb(190, 219, 144)",
        // content_bg: 'rgb(218, 247, 206)',
        content_bg: "#F0FFF0",
        border: "rgb(180, 179, 124)"
    },
    {
        id: "blue",
        title_bg: "rgb(167, 221, 215)",
        content_bg: "azure",
        border: "rgb(99, 167, 159)",
        borderShadow: "#63A79F"
    },
    {
        id: "purple",
        title_bg: "#fa90e6",
        content_bg: "rgb(255, 239, 255)",
        // content_bg: '#fee0fd',
        border: "mediumorchid"
    }
];
const ColorMenu = (props)=>{
    // const intl = useIntl();
    const [anchorEl, setAnchorEl] = useState(null);
    const { children , onSubmit , iconStyle  } = props;
    const handleClose = ()=>{
        setAnchorEl(null);
    };
    return /*#__PURE__*/ _jsxs("div", {
        children: [
            /*#__PURE__*/ _jsx("div", {
                className: "hoverStand",
                style: iconStyle,
                onClick: (e)=>setAnchorEl(e.currentTarget),
                children: children
            }),
            /*#__PURE__*/ _jsx(Popover, {
                open: Boolean(anchorEl),
                onClose: handleClose,
                anchorEl: anchorEl,
                anchorOrigin: {
                    vertical: "top",
                    horizontal: "left"
                },
                transformOrigin: {
                    vertical: "bottom",
                    horizontal: "left"
                },
                children: /*#__PURE__*/ _jsx("div", {
                    style: {
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: 1,
                        justifyContent: "space-evenly"
                    },
                    children: node_color_themes.map((theme, index)=>{
                        return /*#__PURE__*/ _jsx("div", {
                            className: "hoverStand",
                            style: iconStyle,
                            onClick: ()=>onSubmit(theme.id),
                            children: /*#__PURE__*/ _jsx("div", {
                                style: {
                                    width: 20,
                                    height: 20,
                                    borderRadius: "50%",
                                    backgroundColor: theme.title_bg,
                                    cursor: "pointer"
                                }
                            })
                        }, index + "");
                    })
                })
            })
        ]
    });
};


/***/ }),

/***/ 283:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2728);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2210);
/* harmony import */ var react_icons_md__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(6905);
/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2944);
/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _SimpleFloatingEdge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7752);
/* harmony import */ var _AINode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8869);
/* harmony import */ var _utils_jsonStringUtil__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(1394);
/* harmony import */ var _ColorMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3666);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(7597);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(7987);
/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3196);
/* harmony import */ var react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(1152);
/* harmony import */ var react_icons_fa__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1301);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(7426);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_xyflow_react__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_icons_md__WEBPACK_IMPORTED_MODULE_4__, _SimpleFloatingEdge__WEBPACK_IMPORTED_MODULE_6__, _AINode__WEBPACK_IMPORTED_MODULE_7__, react_i18next__WEBPACK_IMPORTED_MODULE_9__, react_dnd__WEBPACK_IMPORTED_MODULE_10__, react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_11__, react_icons_fa__WEBPACK_IMPORTED_MODULE_12__]);
([_xyflow_react__WEBPACK_IMPORTED_MODULE_2__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__, react_icons_md__WEBPACK_IMPORTED_MODULE_4__, _SimpleFloatingEdge__WEBPACK_IMPORTED_MODULE_6__, _AINode__WEBPACK_IMPORTED_MODULE_7__, react_i18next__WEBPACK_IMPORTED_MODULE_9__, react_dnd__WEBPACK_IMPORTED_MODULE_10__, react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_11__, react_icons_fa__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);

















const snapGrid = [
    20,
    20
];
const nodeTypes = {
    ai_node: _AINode__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z
};
const edgeTypes = {
    float_edge: _SimpleFloatingEdge__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z
};
const defaultViewport = {
    x: 0,
    y: 0,
    zoom: 1.5
};
const FlowEditor = ({ content , mimeContents , imgGenerated , setImgGenerated , context , ai_action , mode , app , onDocSaved , doc  })=>{
    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useNodesState)([]);
    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useEdgesState)([]);
    const [rfInstance, setRfInstance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    // const [doc, setDoc] = useState();
    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)("common");
    const [fullscreenRef, setFullscreenRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useBreakpointValue)({
        base: true,
        md: false
    });
    const addNewNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data, nodes, edges, addToHead)=>{
        let node = {
            id: new Date().getTime() + nodes.length + "",
            type: "ai_node",
            data: {
                aigc_triggered: true,
                nodeType: "aigc",
                ...data
            }
        };
        if (nodes.length) {
            edges.push({
                id: new Date().getTime() + edges.length + "",
                source: addToHead ? node.id : nodes[0].id,
                target: addToHead ? nodes[0].id : node.id,
                type: "float_edge",
                markerEnd: {
                    type: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.MarkerType.ArrowClosed
                }
            });
        }
        if (addToHead) {
            nodes.unshift(node);
        } else {
            nodes.push(node);
        }
        return node;
    }, []);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (!content && !mimeContents?.length) return;
        let nodes = [];
        let edges = [];
        if ([
            _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.erase */ .IF.erase,
            _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.avatar */ .IF.avatar,
            _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.imageEditor */ .IF.imageEditor,
            _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.sketch */ .IF.sketch
        ].includes(app)) {
            setImgGenerated(mimeContents[0]?.src);
            mimeContents[0]?.src && addNewNode({
                nodeType: "image",
                queryType: "image",
                title: context?.title,
                content: {
                    src: mimeContents[0].src,
                    caption: content
                }
            }, nodes, edges);
        } else {
            const jsonResult = (0,_utils_jsonStringUtil__WEBPACK_IMPORTED_MODULE_14__/* .extractJSONFromString */ .G)(content);
            const generated = jsonResult?.generated;
            if (!generated) {
                return;
            }
            if (ai_action === "flow_task_breakdown") {
                const timeStamp = new Date().getTime();
                let priorities = [];
                generated.items?.forEach((item)=>{
                    if (!priorities.includes(item.priority)) {
                        priorities.push(item.priority);
                    }
                });
                if (!priorities.length) {
                    priorities = [
                        "high",
                        "medium",
                        "low"
                    ];
                }
                addNewNode({
                    aigc_done: false,
                    content: "",
                    contentType: "todos",
                    todos: generated.items?.map((item, index)=>{
                        item.id = timeStamp + index;
                        return item;
                    }),
                    priorities: priorities.length > 0 ? priorities : undefined
                }, nodes, edges);
            } else if (ai_action === "summary_keypoints") {
                addNewNode({
                    aigc_done: false,
                    title: "Summary and key points",
                    content: generated.summary,
                    items: generated.keypoints
                }, nodes, edges);
            } else {
                let rootNode = addNewNode({
                    // aigc_done: false,
                    ai_action,
                    queryType: [
                        "movie",
                        "book",
                        "link",
                        "video"
                    ].includes(mode) && mode || "brainstorming",
                    content: generated?.central_topic || generated?.core_story || generated?.initial_analysis || generated?.decision || generated?.description || generated?.summary,
                    title: generated?.theme || generated?.title || generated?.problem || "FunBlocks AI Mindmap",
                    brainstorming_scenario: generated?.target_scenario,
                    is_mindmap_root: true
                }, nodes, edges);
                const perspectives = generated?.key_perspectives || generated?.primary_branches || generated?.branches;
                if (perspectives?.length > 0) {
                    // const positions = getSubnodesPositions(node, perspectives.length);
                    perspectives.map((item, index)=>{
                        if (!item.branches) return;
                        const color_theme = _ColorMenu__WEBPACK_IMPORTED_MODULE_8__/* .node_color_themes */ .U[index % _ColorMenu__WEBPACK_IMPORTED_MODULE_8__/* .node_color_themes.length */ .U.length];
                        addNewNode({
                            queryType: [
                                "flow_subtopic_brainstorming",
                                "flow_brainstorming",
                                "flow_decision_analysis"
                            ].includes(ai_action) && "perspective" || [
                                "flow_mindmap",
                                "flow_book_mindmap",
                                "flow_movie_mindmap",
                                "describe_image_mindmap"
                            ].includes(ai_action) && "mindmap_primary_branch" || "perspective",
                            ai_action: [
                                "flow_subtopic_brainstorming",
                                "flow_brainstorming",
                                "flow_decision_analysis"
                            ].includes(ai_action) && "brainstorming_perspective" || ai_action === "flow_mindmap" && "mindmap_primary_branch" || ai_action === "flow_book_mindmap" && "book_mindmap_primary_branch" || ai_action === "flow_movie_mindmap" && "movie_mindmap_primary_branch" || ai_action === "describe_image_mindmap" && "image_mindmap_primary_branch" || "brainstorming_perspective",
                            title: item.name,
                            items: item.branches,
                            userInput: item.name,
                            context_node_id: rootNode.id,
                            // context: { topic: generated.central_topic || node.data.title },
                            color_theme: color_theme?.id
                        }, nodes, edges);
                    });
                }
                const insights = generated?.summary_insights || generated?.key_insights || generated?.transformative_insights || generated?.insights;
                if (insights) {
                    addNewNode({
                        queryType: "brainstorming_insights",
                        // ai_action: 'brainstorming_perspective',
                        context_node_id: rootNode.id,
                        title: "Summary insights",
                        items: insights,
                        // context: { topic: generated.central_topic || node.data.title },
                        color_theme: _ColorMenu__WEBPACK_IMPORTED_MODULE_8__/* .node_color_themes.find */ .U.find((theme)=>theme.id === "blue")?.id
                    }, nodes, edges);
                }
            }
        }
        nodes = nodes.map((node, index)=>{
            if (index === 0) {
                node.position = {
                    x: 0,
                    y: 0
                };
            } else {
                const angle = -(index - 1) * (2 * Math.PI / (nodes.length - 1));
                const radius = 650; // You can adjust the radius as needed
                node.position = {
                    x: radius * Math.cos(angle),
                    y: radius * Math.sin(angle) - (index > nodes.length / 2 ? 300 : 0)
                };
            }
            return node;
        });
        if (nodes && [
            "video",
            "link",
            "image",
            "others"
        ].includes(mode)) {
            let contextNode = addNewNode({
                nodeType: mode === "video" && "video" || mode === "image" && "image" || mode === "others" && "note" || "aigc",
                queryType: [
                    "video",
                    "link",
                    "image"
                ].includes(mode) && mode || undefined,
                vid: context.vid,
                context: [
                    "others",
                    "image"
                ].includes(mode) ? undefined : context,
                title: context.title,
                content: mode === "others" ? context.userInput : mode === "image" ? context : undefined,
                userInput: mode === "others" ? context.userInput : undefined
            }, nodes, edges, true);
            nodes[0].position = {
                x: [
                    _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.erase */ .IF.erase,
                    _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.avatar */ .IF.avatar,
                    _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.imageEditor */ .IF.imageEditor,
                    _utils_constants__WEBPACK_IMPORTED_MODULE_13__/* .APP_TYPE.sketch */ .IF.sketch
                ].includes(app) ? -450 : -900,
                y: 0
            };
            nodes[1].data.context_node_id = contextNode.id;
        }
        setNodes(nodes);
        setEdges(edges);
        onDocSaved(null);
    }, [
        content,
        mimeContents
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (nodes?.length) return;
        if (!doc?.jsonString) return;
        const flow = JSON.parse(doc.jsonString || "{}");
        // const { x = 0, y = 0, zoom = 1 } = flow.viewport || {};
        setNodes(flow.nodes || []);
        setEdges(flow.edges || []);
    }, [
        doc
    ]);
    const saveDoc = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (nodes, jsonString)=>{
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(()=>controller.abort(), 180000);
            let data = doc ? {
                doc: {
                    hid: doc.hid,
                    type: "flow",
                    jsonString
                }
            } : {
                doc: {
                    type: "flow",
                    title: nodes[0].data.userInput || nodes[0].data.title,
                    jsonString,
                    app,
                    mode
                },
                source: "aitools"
            };
            setIsSaving(true);
            const endpoint = "/doc/upsert";
            const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_15__/* .apiUrl */ .J}${endpoint}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    data
                }),
                credentials: "include",
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            if (response.ok) {
                const result = await response.json();
                if (result?.data) {
                    // setDoc(result.data)
                    !!onDocSaved && onDocSaved(result.data);
                }
            }
        } catch (error) {
            console.error("Error in saving doc:", error);
        } finally{
            setIsSaving(false);
        }
    }, [
        onDocSaved,
        doc
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (nodes?.length) {
            if (!doc && !isSaving) {
                let flow = rfInstance?.getNodes()?.length ? rfInstance.toObject() : {
                    nodes,
                    edges,
                    viewport: {
                        x: 0,
                        y: 0,
                        zoom: 1
                    }
                };
                const jsonString = JSON.stringify(flow);
                saveDoc(nodes, jsonString);
            }
        }
    }, [
        nodes,
        edges,
        rfInstance
    ]);
    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((params)=>setEdges((eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.addEdge)({
                ...params,
                animated: true
            }, eds)), []);
    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{
        const handleShowToast = ()=>{
            toast({
                title: t("to_aiflow"),
                description: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    style: {
                        display: "flex",
                        flexDirection: "column",
                        rowGap: 10
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("span", {
                            style: {
                                font: 18
                            },
                            children: t("to_aiflow_tips")
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {
                            size: "sm",
                            onClick: ()=>window.open("https://app.funblocks.net/#/aiflow?hid=" + doc.hid),
                            children: "FunBlocks AIFlow"
                        })
                    ]
                }),
                duration: 10000,
                isClosable: true
            });
        };
        window.addEventListener("showAIFlowToast", handleShowToast);
        return ()=>{
            window.removeEventListener("showAIFlowToast", handleShowToast);
        };
    }, [
        toast,
        t,
        doc
    ]);
    const toggleFullscreen = ()=>{
        if (!fullscreenRef) return;
        if (!document.fullscreenElement) {
            fullscreenRef.requestFullscreen().catch((err)=>{
                console.error(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const handleFullscreenChange = ()=>{
            setIsFullscreen(!!document.fullscreenElement);
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return ()=>{
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_dnd__WEBPACK_IMPORTED_MODULE_10__.DndProvider, {
        backend: react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_11__.HTML5Backend,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            ref: setFullscreenRef,
            style: {
                position: "relative",
                width: "100%",
                height: "100%",
                backgroundColor: isFullscreen ? "#f8f8f8" : "transparent"
            },
            children: [
                isMobile && imgGenerated && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("img", {
                    src: imgGenerated
                }),
                !(isMobile && imgGenerated) && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.ReactFlow, {
                    nodes: nodes,
                    edges: edges,
                    onNodesChange: onNodesChange,
                    onEdgesChange: onEdgesChange,
                    onConnect: onConnect,
                    onInit: setRfInstance,
                    nodeTypes: nodeTypes,
                    edgeTypes: edgeTypes,
                    snapToGrid: true,
                    snapGrid: snapGrid,
                    defaultViewport: defaultViewport,
                    fitView: true,
                    attributionPosition: "bottom-left",
                    style: {
                        borderRadius: 6
                    },
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Background, {
                            style: {
                                backgroundColor: "#f8f8f8"
                            }
                        }),
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Controls, {}),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            style: {
                                position: "absolute",
                                right: "20px",
                                bottom: "20px",
                                zIndex: 4,
                                display: "flex",
                                flexDirection: "column",
                                rowGap: 10
                            },
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {
                                    label: "Open with FunBlocks AIFlow",
                                    "aria-label": "Open with FunBlocks AIFlow",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {
                                        "aria-label": "Open with FunBlocks AIFlow",
                                        icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_md__WEBPACK_IMPORTED_MODULE_4__.MdEdit, {
                                            size: 20
                                        }),
                                        onClick: ()=>window.open("https://app.funblocks.net/#/aiflow?hid=" + doc.hid),
                                        colorScheme: "gray",
                                        variant: "solid",
                                        size: "md",
                                        borderRadius: "full",
                                        boxShadow: "md",
                                        _hover: {
                                            transform: "scale(1.1)"
                                        },
                                        transition: "all 0.2s"
                                    })
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {
                                    label: isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen",
                                    "aria-label": "Fullscreen tooltip",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {
                                        "aria-label": isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen",
                                        icon: isFullscreen ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_md__WEBPACK_IMPORTED_MODULE_4__.MdFullscreenExit, {
                                            size: 24
                                        }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_icons_md__WEBPACK_IMPORTED_MODULE_4__.MdFullscreen, {
                                            size: 24
                                        }),
                                        onClick: toggleFullscreen,
                                        colorScheme: "gray",
                                        variant: "solid",
                                        size: "md",
                                        borderRadius: "full",
                                        boxShadow: "md",
                                        _hover: {
                                            transform: "scale(1.1)"
                                        },
                                        transition: "all 0.2s"
                                    })
                                })
                            ]
                        })
                    ]
                })
            ]
        })
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlowEditor);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 8902:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3135);
/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9521);
/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9832);
/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6809);
/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1871);
/* harmony import */ var _styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(3665);
/* harmony import */ var _styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_7__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_markdown__WEBPACK_IMPORTED_MODULE_2__, rehype_katex__WEBPACK_IMPORTED_MODULE_3__, remark_math__WEBPACK_IMPORTED_MODULE_4__, remark_gfm__WEBPACK_IMPORTED_MODULE_5__, rehype_raw__WEBPACK_IMPORTED_MODULE_6__]);
([react_markdown__WEBPACK_IMPORTED_MODULE_2__, rehype_katex__WEBPACK_IMPORTED_MODULE_3__, remark_math__WEBPACK_IMPORTED_MODULE_4__, remark_gfm__WEBPACK_IMPORTED_MODULE_5__, rehype_raw__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);








// import { PrismLight as SyntaxHighlighter } from 'react-syntax-highlighter';
// import vscDarkPlus from 'react-syntax-highlighter/dist/esm/styles/prism/vsc-dark-plus';
// // 导入更多语言
// import jsx from 'react-syntax-highlighter/dist/esm/languages/prism/jsx';
// import javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript';
// import typescript from 'react-syntax-highlighter/dist/esm/languages/prism/typescript';
// import python from 'react-syntax-highlighter/dist/esm/languages/prism/python';
// import java from 'react-syntax-highlighter/dist/esm/languages/prism/java';
// import csharp from 'react-syntax-highlighter/dist/esm/languages/prism/csharp';
// import php from 'react-syntax-highlighter/dist/esm/languages/prism/php';
// import ruby from 'react-syntax-highlighter/dist/esm/languages/prism/ruby';
// import swift from 'react-syntax-highlighter/dist/esm/languages/prism/swift';
// import go from 'react-syntax-highlighter/dist/esm/languages/prism/go';
// import rust from 'react-syntax-highlighter/dist/esm/languages/prism/rust';
// import scala from 'react-syntax-highlighter/dist/esm/languages/prism/scala';
// import kotlin from 'react-syntax-highlighter/dist/esm/languages/prism/kotlin';
// import sql from 'react-syntax-highlighter/dist/esm/languages/prism/sql';
// import bash from 'react-syntax-highlighter/dist/esm/languages/prism/bash';
// import css from 'react-syntax-highlighter/dist/esm/languages/prism/css';
// import html from 'react-syntax-highlighter/dist/esm/languages/prism/markup';
// import { Clipboard } from '@styled-icons/bootstrap/Clipboard';
// import { ClipboardCheck } from '@styled-icons/bootstrap/ClipboardCheck'
// // 注册所有导入的语言
// SyntaxHighlighter.registerLanguage('jsx', jsx);
// SyntaxHighlighter.registerLanguage('javascript', javascript);
// SyntaxHighlighter.registerLanguage('typescript', typescript);
// SyntaxHighlighter.registerLanguage('python', python);
// SyntaxHighlighter.registerLanguage('java', java);
// SyntaxHighlighter.registerLanguage('csharp', csharp);
// SyntaxHighlighter.registerLanguage('php', php);
// SyntaxHighlighter.registerLanguage('ruby', ruby);
// SyntaxHighlighter.registerLanguage('swift', swift);
// SyntaxHighlighter.registerLanguage('go', go);
// SyntaxHighlighter.registerLanguage('rust', rust);
// SyntaxHighlighter.registerLanguage('scala', scala);
// SyntaxHighlighter.registerLanguage('kotlin', kotlin);
// SyntaxHighlighter.registerLanguage('sql', sql);
// SyntaxHighlighter.registerLanguage('bash', bash);
// SyntaxHighlighter.registerLanguage('css', css);
// SyntaxHighlighter.registerLanguage('html', html);
const CodeBlockRenderer = ({ props , value , language  })=>{
    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const handleCopy = (value)=>{
        navigator.clipboard.writeText(value);
        setCopied(true);
        setTimeout(()=>setCopied(false), 2000);
    };
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        style: {
            position: "relative"
        },
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(SyntaxHighlighter, {
                style: vscDarkPlus,
                language: language,
                showLineNumbers: true,
                wrapLines: true,
                customStyle: {
                    borderRadius: 4
                },
                PreTag: "div",
                ...props,
                children: value
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", {
                onClick: ()=>handleCopy(value),
                style: {
                    position: "absolute",
                    top: 0,
                    right: 0,
                    background: "none",
                    border: "none",
                    color: "white",
                    cursor: "pointer",
                    padding: "5px",
                    color: copied ? "lightskyblue" : "white"
                },
                children: [
                    copied && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_7__.ClipboardCheck, {
                        color: "lightskyblue",
                        size: 16
                    }),
                    !copied && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_7__.Clipboard, {
                        size: 16
                    }),
                    copied ? "Copid" : "Copy"
                ]
            })
        ]
    });
};
const ArtifactButton = ({ onClick , children , setArtifactOffsetTop  })=>{
    const buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("button", {
        ref: buttonRef,
        onClick: ()=>{
            !!setArtifactOffsetTop && setArtifactOffsetTop(buttonRef?.current?.offsetTop);
            !!onClick && onClick();
        },
        style: {
            padding: "5px 10px",
            margin: "5px",
            backgroundColor: "#f0f0f0",
            border: "1px solid #ccc",
            borderRadius: "4px",
            cursor: "pointer"
        },
        children: children
    });
};
const MarkdownRenderer = ({ content , artifacts , setArtifacts , setSelectedArtifact , setArtifactOffsetTop  })=>{
    const [processedContent, setProcessedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    // const [artifacts, setArtifacts] = useState([]);
    // const [selectedArtifact, setSelectedArtifact] = useState(null);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const extractArtifacts = (markdown)=>{
            // 正则表达式匹配 SVG, HTML 和 Mermaid 代码块
            const artifactRegex = /<(svg)[\s\S]*?<\/\1>|```mermaid[\s\S]*?```/gi;
            const extractedArtifacts = [];
            let artifactId = 0;
            const processedMarkdown = markdown.replace(artifactRegex, (match)=>{
                let type;
                if (match.startsWith("<svg")) {
                    type = "SVG";
                // } else if (match.startsWith('<html')) {
                //   type = 'HTML';
                } else if (match.startsWith("```mermaid")) {
                    type = "Mermaid";
                    // 移除 Mermaid 代码块的首尾 ```
                    match = match.replace(/```mermaid\n?/, "").replace(/```$/, "");
                }
                extractedArtifacts.push({
                    id: artifactId,
                    type,
                    content: match
                });
                const placeholder = `[ARTIFACT_${artifactId}]`;
                artifactId++;
                return placeholder;
            });
            setArtifacts(extractedArtifacts);
            return processedMarkdown;
        };
        const processedMarkdown = setArtifacts ? extractArtifacts(content) : content;
        setProcessedContent(processedMarkdown?.replace(/(\S)?(\*\*.*?\*\*)(\S)?/g, (match, p1, p2, p3)=>{
            const before = p1 ? p1 + " " : "";
            const after = p3 ? " " + p3 : "";
            return before + p2 + after;
        }));
    }, [
        content
    ]);
    const renderArtifactButton = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{
        const artifact = artifacts.find((a)=>a.id === id);
        if (!artifact) return null;
        return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(ArtifactButton, {
            onClick: ()=>{
                setSelectedArtifact(artifact);
            },
            setArtifactOffsetTop: setArtifactOffsetTop,
            children: [
                "View ",
                artifact.type,
                " Content"
            ]
        });
    }, [
        artifacts
    ]);
    const processArtifactContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((child)=>{
        if (typeof child === "string") {
            // 如果字符串包含 artifact 标记，替换为按钮
            if (child.includes("[ARTIFACT_")) {
                const artifactId = parseInt(child.match(/\[ARTIFACT_(\d+)\]/)[1], 10);
                // 分割文本，保留 artifact 标记前后的内容
                const parts = child.split(/\[ARTIFACT_\d+\]/);
                return [
                    parts[0],
                    renderArtifactButton(artifactId),
                    parts[1]
                ];
            }
            return child;
        }
        if (Array.isArray(child)) {
            return child.map(processArtifactContent).flat();
        }
        // 处理 React 元素
        if (child?.props?.children) {
            const processedChildren = processArtifactContent(child.props.children);
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(child, {
                children: processedChildren
            });
        }
        return child;
    }, [
        artifacts
    ]);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(react_markdown__WEBPACK_IMPORTED_MODULE_2__["default"], {
        remarkPlugins: [
            remark_gfm__WEBPACK_IMPORTED_MODULE_5__["default"],
            remark_math__WEBPACK_IMPORTED_MODULE_4__["default"]
        ],
        rehypePlugins: [
            rehype_katex__WEBPACK_IMPORTED_MODULE_3__["default"],
            rehype_raw__WEBPACK_IMPORTED_MODULE_6__["default"]
        ],
        components: {
            p ({ node , children  }) {
                // 递归处理内容，保留非 artifact 内容
                const processedChildren = processArtifactContent(children);
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("p", {
                    children: processedChildren
                });
            },
            code ({ node , inline , className , children , ...props }) {
                const match = /language-(\w+)/.exec(className || "");
                const value = String(children).replace(/\n$/, "");
                if (typeof children === "string" && value.includes("[ARTIFACT_")) {
                    const artifactId = parseInt(value.match(/\d+/)[0], 10);
                    return renderArtifactButton(artifactId);
                }
                if (match && [
                    "svg"
                ].includes(match[1])) {
                    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        dangerouslySetInnerHTML: {
                            __html: value
                        }
                    });
                }
                // if (match && match[1] === 'mermaid') {
                //   return <MermaidRenderer id={'mermaid-chart'} source={value} />
                // }
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    className: "nodrag",
                    children: !inline && match ? /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(CodeBlockRenderer, {
                        props: props,
                        language: match[1],
                        value: value
                    }) : /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("code", {
                        className: className,
                        ...props,
                        children: children
                    })
                });
            }
        },
        children: processedContent
    });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarkdownRenderer);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 7752:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Z": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(2728);
/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(6676);
/* harmony import */ var _styled_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(384);
/* harmony import */ var _styled_icons_material_Close__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_xyflow_react__WEBPACK_IMPORTED_MODULE_2__, _utils_js__WEBPACK_IMPORTED_MODULE_3__]);
([_xyflow_react__WEBPACK_IMPORTED_MODULE_2__, _utils_js__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);





function SimpleFloatingEdge({ id , source , target , markerEnd , style  }) {
    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const sourceNode = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useStore)((0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((store)=>store.nodeLookup?.get(source), [
        source
    ]));
    const targetNode = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useStore)((0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((store)=>store.nodeLookup?.get(target), [
        target
    ]));
    const { setEdges  } = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.useReactFlow)();
    const edgeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const onEdgeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{
        event.stopPropagation();
        setEdges((edges)=>edges.filter((edge)=>edge.id !== id));
    }, [
        setEdges,
        id
    ]);
    // const onEdgeMouseEnter = useCallback(() => {
    //   setIsHovered(true);
    // }, []);
    // const onEdgeMouseLeave = useCallback((event) => {
    //   // 检查鼠标是否真的离开了整个边缘区域，包括按钮
    //   if (!edgeRef.current.contains(event.relatedTarget)) {
    //     setIsHovered(false);
    //   }
    // }, []);
    if (!sourceNode || !targetNode) {
        return null;
    }
    const { sx , sy , tx , ty , sourcePos , targetPos  } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__/* .getEdgeParams */ .x)(sourceNode, targetNode);
    const [edgePath, labelX, labelY] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_2__.getBezierPath)({
        sourceX: sx,
        sourceY: sy,
        sourcePosition: sourcePos,
        targetPosition: targetPos,
        targetX: tx,
        targetY: ty
    });
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("g", {
        ref: edgeRef,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("path", {
                id: `${id}-selector`,
                className: "react-flow__edge-selector",
                d: edgePath,
                strokeWidth: 20,
                fill: "none",
                stroke: "transparent"
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("path", {
                id: id,
                className: "react-flow__edge-path",
                d: edgePath,
                strokeWidth: 5,
                markerEnd: markerEnd,
                style: {
                    ...style,
                    stroke: isHovered ? "#555" : style?.stroke
                }
            })
        ]
    });
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleFloatingEdge);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 435:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "B": () => (/* binding */ TodoList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _styled_icons_fluentui_system_regular_CheckboxUnchecked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7520);
/* harmony import */ var _styled_icons_fluentui_system_regular_CheckboxUnchecked__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_fluentui_system_regular_CheckboxUnchecked__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _styled_icons_fluentui_system_regular_CheckboxChecked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1977);
/* harmony import */ var _styled_icons_fluentui_system_regular_CheckboxChecked__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_fluentui_system_regular_CheckboxChecked__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _ColorMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3666);
/* harmony import */ var _styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9077);
/* harmony import */ var _styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _dndlist_SortableCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(4011);
/* harmony import */ var immutability_helper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(6333);
/* harmony import */ var immutability_helper__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(immutability_helper__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(3620);
/* harmony import */ var _styled_icons_material__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_material__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(3770);
/* harmony import */ var _styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_fluentui_system_regular_Edit__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(7987);
/* harmony import */ var _styled_icons_bootstrap_TextLeft__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(450);
/* harmony import */ var _styled_icons_bootstrap_TextLeft__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap_TextLeft__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(2210);
/* harmony import */ var _styled_icons_bootstrap_ChevronDown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(9327);
/* harmony import */ var _styled_icons_bootstrap_ChevronDown__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_styled_icons_bootstrap_ChevronDown__WEBPACK_IMPORTED_MODULE_13__);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_dndlist_SortableCard__WEBPACK_IMPORTED_MODULE_6__, react_i18next__WEBPACK_IMPORTED_MODULE_10__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__]);
([_dndlist_SortableCard__WEBPACK_IMPORTED_MODULE_6__, react_i18next__WEBPACK_IMPORTED_MODULE_10__, _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);














const TodoItem = ({ item , index , nodeId , onClick , onCheck , onUpdatePriority , onRemoveTask , onTaskBreakdown , expandEnabled , onExpand , priorities , color_theme , moveCard  })=>{
    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_10__.useTranslation)("common");
    const [hovered, setHovered] = react__WEBPACK_IMPORTED_MODULE_1__.useState();
    const itemRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();
    const Check = item.done ? _styled_icons_fluentui_system_regular_CheckboxChecked__WEBPACK_IMPORTED_MODULE_3__.CheckboxChecked : _styled_icons_fluentui_system_regular_CheckboxUnchecked__WEBPACK_IMPORTED_MODULE_2__.CheckboxUnchecked;
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_dndlist_SortableCard__WEBPACK_IMPORTED_MODULE_6__/* .SortableCard */ .$, {
        index: index,
        id: item.id,
        moveCard: moveCard,
        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
            style: {
                position: "relative",
                border: `1px solid ${color_theme.border}`,
                backgroundColor: color_theme.content_bg,
                boxShadow: `5px 5px 0px rgba(0, 0, 0, 10%)`,
                borderRadius: 4,
                padding: "6px",
                paddingLeft: 4,
                paddingTop: 3,
                paddingBottom: 0,
                fontSize: 13,
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                columnGap: 4
            },
            ref: itemRef,
            onMouseEnter: ()=>setHovered(true),
            onMouseLeave: ()=>setHovered(false),
            children: [
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                    style: {
                        cursor: "pointer"
                    },
                    onClick: (event)=>{
                        event.stopPropagation();
                        onCheck();
                    },
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Check, {
                        color: "#333",
                        size: 18
                    })
                }),
                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    className: "fill-available",
                    children: [
                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                            style: {
                                textDecoration: item.done ? "line-through" : "none",
                                color: item.done ? "GrayText" : undefined,
                                marginTop: 2,
                                marginBottom: 1
                            },
                            children: item.description?.trim()
                        }),
                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                            style: {
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "flex-end",
                                columnGap: 3,
                                marginTop: 2,
                                fontSize: 11,
                                width: "100%"
                            },
                            children: [
                                item.dueDate && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    children: [
                                        t("due_date"),
                                        ": ",
                                        item.dueDate
                                    ]
                                }),
                                priorities && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                    style: {
                                        color: "GrayText",
                                        display: "flex",
                                        flexDirection: "row",
                                        alignItems: "center"
                                    },
                                    children: [
                                        t("priority"),
                                        ":",
                                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                                            style: {
                                                display: "flex",
                                                flexDirection: "row",
                                                alignItems: "center"
                                            },
                                            children: [
                                                item.priority || "Mediumn",
                                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {
                                                    label: t("task_priority"),
                                                    "aria-label": "task priority update",
                                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                                        style: {
                                                            display: "flex",
                                                            flexDirection: "row",
                                                            alignItems: "center",
                                                            cursor: "pointer",
                                                            padding: 3
                                                        },
                                                        onClick: onUpdatePriority,
                                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap_ChevronDown__WEBPACK_IMPORTED_MODULE_13__.ChevronDown, {
                                                            size: 10
                                                        })
                                                    })
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {
                                    label: t("task_analysis"),
                                    "aria-label": "task analysis",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        className: "transparent-background",
                                        style: {
                                            padding: 2
                                        },
                                        onClick: ()=>{
                                            !item.done && onClick();
                                        },
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_bootstrap_TextLeft__WEBPACK_IMPORTED_MODULE_11__.TextLeft, {
                                            size: 15,
                                            color: color_theme.border
                                        })
                                    })
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {
                                    label: t("task_breakdown"),
                                    "aria-label": "task breakdown",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                        className: "transparent-background",
                                        style: {
                                            padding: 2
                                        },
                                        onClick: (event)=>{
                                            event.stopPropagation();
                                            onTaskBreakdown();
                                        },
                                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__.FlowCascade, {
                                            size: 13,
                                            color: color_theme.border
                                        })
                                    })
                                })
                            ]
                        })
                    ]
                }),
                hovered && expandEnabled && // <Tooltip
                //   title={intl.formatMessage({ id: 'expand_ideas' })}
                //   placement='bottom'
                // >
                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {
                    label: t("expand_ideas"),
                    "aria-label": "expand ideas tooltip",
                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        className: "circle-button",
                        style: {
                            position: "absolute",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            height: 20,
                            width: 20,
                            top: "50%",
                            transform: "translate(50%, -50%)",
                            right: 0,
                            borderColor: color_theme.border,
                            backgroundColor: color_theme.content_bg
                        },
                        onClick: (event)=>{
                            event.stopPropagation();
                            onExpand(itemRef?.current);
                        },
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_styled_icons_entypo_FlowCascade__WEBPACK_IMPORTED_MODULE_5__.FlowCascade, {
                            size: 15,
                            color: "dodgerblue"
                        })
                    })
                })
            ]
        })
    });
};
const TodoList = ({ nodeId , list , priorities , listType , queryType , nodeType , aiItemClicked , handleUpdatePriority , updateNodeData , expandEnabled , onExpand  })=>{
    const [items, setItems] = react__WEBPACK_IMPORTED_MODULE_1__.useState(list);
    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{
        setItems(list);
    }, [
        list
    ]);
    const moveCard = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((dragIndex, hoverIndex)=>{
        setItems((prevCards)=>immutability_helper__WEBPACK_IMPORTED_MODULE_7___default()(prevCards, {
                $splice: [
                    [
                        dragIndex,
                        1
                    ],
                    [
                        hoverIndex,
                        0,
                        prevCards[dragIndex]
                    ]
                ]
            }));
    }, []);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
        style: {
            display: "flex",
            flexDirection: "column",
            rowGap: 10,
            paddingRight: 10,
            marginTop: 5,
            marginBottom: 5,
            cursor: "move"
        },
        onDrop: (event)=>{
            // updateProperties(items, 'moveCard');
            updateNodeData(nodeId, {
                todos: items
            });
        },
        children: [
            " ",
            items?.map((item, index)=>{
                if (!item) return;
                const priority_index = priorities?.findIndex((prio)=>prio === item.priority);
                const color_theme = _ColorMenu__WEBPACK_IMPORTED_MODULE_4__/* .node_color_themes.find */ .U.find((theme)=>theme.id === (item.done && "gray" || priority_index === 0 && "purple" || priority_index === 2 && "blue" || "green"));
                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(TodoItem, {
                    index: index,
                    nodeId: nodeId,
                    list: list,
                    item: item,
                    listType: listType,
                    priorities: priorities,
                    color_theme: color_theme,
                    moveCard: moveCard,
                    onCheck: ()=>{
                        aiItemClicked();
                    },
                    expandEnabled: expandEnabled,
                    onExpand: !onExpand ? undefined : (anchor)=>{
                        // ReactGA.event({ category: 'TodoListItem', action: 'expand ideas', label: listType });
                        // aiItemClicked({ item: { action: 'breakdown' }, nodeType, queryType: 'breakdown', userInput: text, color_theme });
                        onExpand(anchor, item.description);
                    },
                    onTaskBreakdown: ()=>{
                        // ReactGA.event({ category: 'TodoListItem', action: 'flow_task_breakdown', label: listType });
                        aiItemClicked({
                            item: {
                                action: "flow_task_breakdown"
                            },
                            nodeType,
                            queryType,
                            userInput: item.description,
                            color_theme
                        });
                    },
                    onClick: ()=>{
                        // ReactGA.event({ category: 'TodoListItem', action: 'flow_task_analysis', label: listType });
                        aiItemClicked({
                            item: {
                                action: "flow_task_analysis"
                            },
                            nodeType,
                            queryType,
                            userInput: item.description,
                            color_theme
                        });
                    },
                    onUpdatePriority: (priority)=>handleUpdatePriority(item, priority),
                    onRemoveTask: ()=>handleRemoveTask(item)
                }, item.id + "");
            })
        ]
    });
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 6676:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "x": () => (/* binding */ getEdgeParams)
/* harmony export */ });
/* unused harmony export getHandlePosition */
/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2728);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_xyflow_react__WEBPACK_IMPORTED_MODULE_0__]);
_xyflow_react__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];

function getHandlePosition(nodeA, nodeB) {
    const centerA = getNodeCenter(nodeA);
    const centerB = getNodeCenter(nodeB);
    const horizontalDiff = Math.abs(centerA.x - centerB.x);
    const verticalDiff = Math.abs(centerA.y - centerB.y);
    let position;
    // when the horizontal difference between the nodes is bigger, we use Position.Left or Position.Right for the handle
    if (horizontalDiff > verticalDiff) {
        position = centerA.x > centerB.x ? _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Left : _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Right;
    } else {
        // here the vertical difference between the nodes is bigger, so we use Position.Top or Position.Bottom for the handle
        position = centerA.y > centerB.y ? _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Top : _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Bottom;
    }
    return position;
}
// returns the position (top,right,bottom or right) passed node compared to
function getParams(nodeA, nodeB) {
    const position = getHandlePosition(nodeA, nodeB);
    const [x, y] = getHandleCoordsByPosition(nodeA, position);
    return [
        x,
        y,
        position
    ];
}
function getHandleCoordsByPosition(node, handlePosition) {
    // all handles are from type source, that's why we use handleBounds.source here
    const handle = node.internals.handleBounds.source.find((h)=>h.position === handlePosition);
    let offsetX = handle.width / 2;
    let offsetY = handle.height / 2;
    // this is a tiny detail to make the markerEnd of an edge visible.
    // The handle position that gets calculated has the origin top-left, so depending which side we are using, we add a little offset
    // when the handlePosition is Position.Right for example, we need to add an offset as big as the handle itself in order to get the correct position
    switch(handlePosition){
        case _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Left:
            offsetX = 0;
            break;
        case _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Right:
            offsetX = handle.width;
            break;
        case _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Top:
            offsetY = 0;
            break;
        case _xyflow_react__WEBPACK_IMPORTED_MODULE_0__.Position.Bottom:
            offsetY = handle.height;
            break;
    }
    // const x = (node.computed?.positionAbsolute?.x || node.position.x) + handle.x + offsetX;
    // const y = (node.computed?.positionAbsolute?.y || node.position.y) + handle.y + offsetY;
    const x = (node.internals?.positionAbsolute?.x || node.position?.x) + handle.x + offsetX;
    const y = (node.internals?.positionAbsolute?.y || node.position?.y) + handle.y + offsetY;
    return [
        x,
        y
    ];
}
function getNodeCenter(node) {
    return {
        // x: (node.computed?.positionAbsolute?.x || node.position.x) + node.computed?.width / 2,
        // y: (node.computed?.positionAbsolute?.y || node.position.y) + node.computed?.height / 2,
        x: (node.internals?.positionAbsolute?.x || node.position?.x) + (node.measured?.width || node.computed?.width) / 2,
        y: (node.internals?.positionAbsolute?.y || node.position?.y) + (node.measured?.height || node.computed?.height) / 2
    };
}
// returns the parameters (sx, sy, tx, ty, sourcePos, targetPos) you need to create an edge
function getEdgeParams(source, target) {
    const [sx, sy, sourcePos] = getParams(source, target);
    const [tx, ty, targetPos] = getParams(target, source);
    return {
        sx,
        sy,
        tx,
        ty,
        sourcePos,
        targetPos
    };
}

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 2492:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "_": () => (/* binding */ ItemTypes)
/* harmony export */ });
const ItemTypes = {
    CARD: "card"
};


/***/ }),

/***/ 4011:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "$": () => (/* binding */ SortableCard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3196);
/* harmony import */ var _ItemTypes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2492);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dnd__WEBPACK_IMPORTED_MODULE_2__]);
react_dnd__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];




const SortableCard = ({ id , text , index , moveCard , children , style  })=>{
    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const [{ handlerId  }, drop] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_2__.useDrop)({
        accept: _ItemTypes_js__WEBPACK_IMPORTED_MODULE_3__/* .ItemTypes.CARD */ ._.CARD,
        collect (monitor) {
            return {
                handlerId: monitor.getHandlerId()
            };
        },
        hover (item, monitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;
            // Don't replace items with themselves
            if (dragIndex === hoverIndex) {
                return;
            }
            // Determine rectangle on screen
            const hoverBoundingRect = ref.current?.getBoundingClientRect();
            // Get vertical middle
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
            // Determine mouse position
            const clientOffset = monitor.getClientOffset();
            // Get pixels to the top
            const hoverClientY = clientOffset.y - hoverBoundingRect.top;
            // Only perform the move when the mouse has crossed half of the items height
            // When dragging downwards, only move when the cursor is below 50%
            // When dragging upwards, only move when the cursor is above 50%
            // Dragging downwards
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }
            // Dragging upwards
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }
            // Time to actually perform the action
            moveCard(dragIndex, hoverIndex);
            // Note: we're mutating the monitor item here!
            // Generally it's better to avoid mutations,
            // but it's good here for the sake of performance
            // to avoid expensive index searches.
            item.index = hoverIndex;
        }
    });
    const [{ isDragging  }, drag] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_2__.useDrag)({
        type: _ItemTypes_js__WEBPACK_IMPORTED_MODULE_3__/* .ItemTypes.CARD */ ._.CARD,
        // options: {
        //   dropEffect: 'move',
        // },
        item: ()=>{
            return {
                id,
                index
            };
        },
        collect: (monitor)=>({
                isDragging: monitor.isDragging()
            })
    });
    const opacity = isDragging ? 0 : 1;
    drag(drop(ref));
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
        ref: ref,
        style: {
            ...style,
            opacity
        },
        "data-handler-id": handlerId,
        children: children
    });
};

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 1082:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "$": () => (/* binding */ ShareModalProvider),
/* harmony export */   "K": () => (/* binding */ useShareModal)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2210);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__]);
_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];




const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_2___default()().publicRuntimeConfig;
const ShareModalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();
const ShareModalProvider = ({ children  })=>{
    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [artifactId, setArtifactId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [imageDataUrl, setImageDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const isMobile = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useBreakpointValue)({
        base: true,
        md: false
    });
    const [app, setApp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    const shareOnMobile = async (app, title, artifactId, imageDataUrl)=>{
        if (navigator.share) {
            try {
                // Convert the data URL to a Blob directly
                const blob = await (await fetch(imageDataUrl)).blob();
                const file = new File([
                    blob
                ], "card.png", {
                    type: "image/png"
                });
                await navigator.share({
                    title: title,
                    text: title,
                    url: `${window.location.origin}${basePath}/share/${app}/${artifactId}`,
                    files: [
                        file
                    ]
                });
                return true; // Share successful
            } catch (error) {
                console.error("Error sharing:", error);
                return false; // Share failed
            }
        }
        return false; // Web Share API not supported
    };
    const openShareModal = async (app, artifactId, imgDataUrl, title)=>{
        if (isMobile) {
            const shared = await shareOnMobile(app, title, artifactId, imgDataUrl);
            if (shared) return; // 如果成功分享，就不打开模态框
        }
        setApp(app);
        setArtifactId(artifactId);
        setImageDataUrl(imgDataUrl);
        setTitle(title);
        setIsOpen(true);
    };
    const closeShareModal = ()=>{
        setApp(null);
        setIsOpen(false);
        setArtifactId(null);
        setImageDataUrl(null);
        setTitle(null);
    };
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(ShareModalContext.Provider, {
        value: {
            tool: app,
            isOpen,
            artifactId,
            imageDataUrl,
            title,
            openShareModal,
            closeShareModal
        },
        children: children
    });
};
const useShareModal = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ShareModalContext);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 7426:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "IF": () => (/* binding */ APP_TYPE),
/* harmony export */   "WD": () => (/* binding */ getAppname),
/* harmony export */   "X0": () => (/* binding */ getAppValue)
/* harmony export */ });
const APP_TYPE = {
    mindmap: "Mindmap",
    graphics: "Graphics",
    infographic: "Infographic",
    insightcards: "InsightCards",
    brainstorming: "Brainstorming",
    mindkit: "MindKit",
    mindsnap: "MindSnap",
    businessmodel: "Businessmodel",
    startupmentor: "Startupmentor",
    okr: "Okr",
    decision: "Decision",
    planner: "Planner",
    slides: "Slides",
    onePageSlides: "one-page-slide",
    erase: "Erase",
    avatar: "Avatar",
    imageEditor: "ImageEditor",
    sketch: "Sketch",
    youtube: "Youtube",
    counselor: "Counselor",
    dreamlens: "DreamLens",
    horoscope: "Horoscope",
    art: "Art",
    photo: "Photo",
    reading: "Reading",
    movie: "Movie",
    criticalThinking: "critical-thinking",
    reflection: "reflection",
    refineQuestion: "refine-question",
    bias: "Bias",
    poetic: "Poetic",
    feynman: "Feynman",
    bloom: "Bloom",
    solo: "solo",
    dok: "dok",
    marzano: "marzano",
    layeredExplanation: "layered-explanation",
    promptOptimizer: "prompt-optimizer",
    lessonPlanner: "lesson-plans",
    teachingSlides: "teaching-slides",
    dokAssessment: "dok-assessment"
};
const getAppname = (app)=>{
    return app === APP_TYPE.art && "Art Insight" || app === APP_TYPE.onePageSlides && "SlideGenius" || app === APP_TYPE.poetic && "Poetic Lens" || app === APP_TYPE.bloom && `Bloom's Taxonomy` || app === APP_TYPE.solo && "SOLO Taxonomy" || app === APP_TYPE.dok && `Webb's DOK` || app === APP_TYPE.marzano && `Marzano's Taxonomy` || app === APP_TYPE.feynman && "Feynman Tutor" || app === APP_TYPE.reading && "Reading Map" || app === APP_TYPE.movie && "CineMap" || app === APP_TYPE.photo && "Photo Coach" || app === APP_TYPE.decision && "Decision Analyst" || app === APP_TYPE.criticalThinking && "Critical Analysis" || app === APP_TYPE.refineQuestion && "QuestionCraft AI" || app === APP_TYPE.bias && "LogicLens" || app === APP_TYPE.youtube && "Youtube Summarizer" || app === APP_TYPE.planner && "Task Planner" || app === APP_TYPE.startupmentor && "Startup Mentor" || app === APP_TYPE.businessmodel && "Business Model Consultant" || app === APP_TYPE.okr && "OKR Assistant" || app === APP_TYPE.erase && "Watermarks Eraser" || app === APP_TYPE.avatar && "Avatar Studio" || app === APP_TYPE.imageEditor && "AI Image Editor" || app === APP_TYPE.sketch && "AI Sketch" || app === APP_TYPE.insightcards && "InsightCards" || app === APP_TYPE.layeredExplanation && "MindLadder" || app === APP_TYPE.lessonPlanner && "Lesson Plans" || app === APP_TYPE.teachingSlides && "Teaching Slides" || app === APP_TYPE.dokAssessment && "DOK Assessment" || app.charAt(0).toUpperCase() + app.slice(1);
};
const getAppValue = (app)=>{
    const lowerApp = app?.toLowerCase();
    return Object.values(APP_TYPE).find((value)=>value.toLowerCase() === lowerApp) || null;
};


/***/ }),

/***/ 5744:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Bf": () => (/* binding */ handleShare),
/* harmony export */   "OR": () => (/* binding */ getProxiedImageUrl),
/* harmony export */   "gp": () => (/* binding */ handleDownload)
/* harmony export */ });
/* harmony import */ var html_to_image__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6131);
/* harmony import */ var html_to_image__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(html_to_image__WEBPACK_IMPORTED_MODULE_0__);

const handleShare = async (app, id, title, svgRef, onShare, toast)=>{
    try {
        if (id) {
            const dataUrl = await getImageData(svgRef);
            onShare(app, id, dataUrl, title);
        } else {
            toast({
                description: "Have nothing to share",
                status: "error",
                duration: 3000,
                isClosable: true
            });
        }
    } catch (error) {
        toast({
            title: "Failed to share",
            description: "Please try again later",
            status: "error",
            duration: 3000,
            isClosable: true
        });
        console.error("分享图片时出错:", error);
    }
};
const getImageData = (svgRef)=>{
    return new Promise((resolve, reject)=>{
        const element = svgRef.current;
        (0,html_to_image__WEBPACK_IMPORTED_MODULE_0__.toPng)(element).then((dataUrl)=>{
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");
            const img = new Image();
            img.onload = ()=>{
                const padding = 30;
                const qrSize = 80;
                const textHeight = 20;
                canvas.width = img.width + padding * 2;
                canvas.height = img.height + padding * 2 + qrSize + textHeight;
                // Set white background
                ctx.fillStyle = "white";
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                // Draw shadow
                ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 4;
                ctx.shadowOffsetY = 6;
                // Draw rounded rectangle for card effect
                ctx.beginPath();
                ctx.roundRect(padding, padding, img.width, img.height, 10);
                ctx.fillStyle = "white";
                ctx.fill();
                // Reset shadow
                ctx.shadowColor = "transparent";
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                // Draw the image
                ctx.drawImage(img, padding, padding);
                const qrCodeDataUrl = document.getElementById("qrcode-canvas").toDataURL("image/png");
                if (qrCodeDataUrl) {
                    const qrImg = new Image();
                    qrImg.onload = ()=>{
                        // Draw QR code with slight shadow
                        ctx.shadowColor = "rgba(0, 0, 0, 0.1)";
                        ctx.shadowBlur = 5;
                        ctx.shadowOffsetX = 2;
                        ctx.shadowOffsetY = 2;
                        ctx.drawImage(qrImg, canvas.width - qrSize - padding, canvas.height - qrSize - padding, qrSize, qrSize);
                        // Reset shadow
                        ctx.shadowColor = "transparent";
                        ctx.shadowBlur = 0;
                        ctx.shadowOffsetX = 0;
                        ctx.shadowOffsetY = 0;
                        // Draw "Generated with" text
                        ctx.font = "15px Arial";
                        ctx.fillStyle = "#666666";
                        ctx.textAlign = "right";
                        ctx.fillText("Generated with", canvas.width - padding - qrSize - 18, canvas.height - padding - qrSize + 15);
                        // Draw FunBlocks AI Graphics with improved gradient
                        const textX = canvas.width - padding - qrSize - 18;
                        const gradientWidth = 200; // Fixed width for gradient
                        const gradient = ctx.createLinearGradient(textX - gradientWidth, 0, textX, 0);
                        gradient.addColorStop(0, "#4361ee");
                        gradient.addColorStop(1, "#7209b7");
                        ctx.font = "bold 24px Arial";
                        ctx.fillStyle = gradient;
                        ctx.textAlign = "right";
                        ctx.fillText("FunBlocks AI Graphics", textX, canvas.height - padding - qrSize / 2 + 5);
                        // Draw slogan
                        ctx.font = "20px Arial";
                        ctx.fillStyle = "#444444";
                        ctx.textAlign = "right";
                        ctx.fillText("Where wit meets wisdom", textX, canvas.height - padding - 5);
                        resolve(canvas.toDataURL("image/png"));
                    };
                    qrImg.src = qrCodeDataUrl;
                } else {
                    resolve(canvas.toDataURL("image/png"));
                }
            };
            img.src = dataUrl;
        }).catch((error)=>{
            console.error("Error getting image data:", error);
            reject(error);
        });
    });
};
const handleDownload = async (svgRef, toast)=>{
    try {
        const dataUrl = await getImageData(svgRef);
        const link = document.createElement("a");
        link.download = "artifact.png";
        link.href = dataUrl;
        link.click();
    } catch (error) {
        toast({
            title: "下载图片时出错",
            description: "请稍后再试",
            status: "error",
            duration: 3000,
            isClosable: true
        });
        console.error("下载图片时出错:", error);
    }
};
const getProxiedImageUrl = (url, server_host)=>{
    if (url.includes(server_host) || url.includes("/imgproxy?") || url.startsWith("data:")) {
        return url;
    }
    let proxiedUrl = server_host + "/imgproxy?url=" + encodeURIComponent(url);
    return proxiedUrl;
};


/***/ }),

/***/ 1394:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "G": () => (/* binding */ extractJSONFromString)
/* harmony export */ });
const removeNewlinesFromJSON = (jsonStr)=>{
    // 将对象转换为JSON字符串，并使用正则表达式去除换行符（仅限键和值之间的换行符）
    const cleanedJsonStr = jsonStr// .replace(/(?<=[:,\{\}\[\]])\s*\n\s*/g, '')
    .replace(/(\s*\n\s*)?(?=[\{\}\[\],:])/g, "").replace(/(?<=[\{\}\[\],:])(\s*\n\s*)?/g, "");
    // console.log({ jsonStr, cleanedJsonStr })
    return cleanedJsonStr;
};
const escapeControlChars = (str)=>{
    const controlChars = /[\x00-\x1F\x7F]/g; // Regular expression to match control characters
    const replacer = (match)=>{
        const charCode = match.charCodeAt(0);
        switch(charCode){
            case 0x08:
                return "\\b";
            case 0x09:
                return "\\t";
            case 0x0A:
                return "\\n";
            case 0x0C:
                return "\\f";
            case 0x0D:
                return "\\r";
            case 0x1B:
                return "\\e";
            default:
                return "\\x" + charCode.toString(16).padStart(2, "0");
        }
    };
    return str.replace(controlChars, replacer);
};
const formatMarkdownOrderedList = (markdownText)=>{
    // 正则表达式匹配模式：^(\*\*\d+\.\s*)(.+?)(\*\*)$
    // 解释：
    // ^ 匹配行的开始
    // (\*\*\d+\.\s*) 匹配 "**数字." 和可能的空格（第一个捕获组）
    // (.+?) 非贪婪匹配任何字符（第二个捕获组，列表项内容）
    // (\*\*)$ 匹配行末的 "**"（第三个捕获组）
    const regex = /^(\*\*\d+\.\s*)(.+?)(\*\*)$/gm;
    // 使用替换函数
    return markdownText.replace(regex, (match, start, content, end)=>{
        // 移除开头的 "**"，保留数字和点
        const formattedStart = start.replace(/^\*\*/, "");
        return `${formattedStart}**${content}**`;
    });
};
const escapeUnescapedQuotesInArray = (str)=>{
    // 正则匹配数组
    // const arrayPattern = /\[(.*?)\]/g;
    const arrayPattern = /\[((?:\{.*?\}|[^\[\]]*?))\]/g;
    let result = str;
    result = result.replace(arrayPattern, (match, content)=>{
        // 如果匹配的内容以 { 开头，说明是 JSON 对象数组，直接返回不处理
        if (content.trim().startsWith("{")) {
            return match;
        }
        // 处理普通数组
        let elements = content.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);
        elements = elements.map((element)=>{
            return element.trim().replace(/(^"|"$)/g, "") // 去除开头和结尾的引号，用于处理内容
            .replace(/([^\\])"/g, '$1\\"'); // 找出未转义的引号并转义
        });
        // 将元素重新组合为数组字符串
        return "[" + elements.map((e)=>`"${e}"`).join(",") + "]";
    });
    return result;
};
const escapeQuoteFromValue = (str)=>{
    str = escapeUnescapedQuotesInArray(str);
    return str.replace(/([{,]\s*")([^"]+)":\s*"(.*?)"\s*(?=[,\]}])/gs, function(match, p1, p2, p3) {
        // 对没有被转义的双引号进行转义
        let escapedValue = p3.replace(/([^\\])"/g, '$1\\"');
        return `${p1}${p2}": "${escapedValue}"`;
    });
};
const extractJSONFromString = (str)=>{
    let stack = [];
    let startIndex = -1;
    let endIndex = -1;
    for(let i = 0; i < str.length; i++){
        if (str[i] === "{") {
            if (stack.length === 0) {
                startIndex = i;
            }
            stack.push("{");
        } else if (str[i] === "}" && stack.length > 0) {
            stack.pop();
            if (stack.length === 0) {
                endIndex = i;
                break; // 找到第一个完整的 JSON 对象，提前结束循环
            }
        }
    }
    if (startIndex == -1 || endIndex == -1) return null;
    const jsonString = str.substring(startIndex, endIndex + 1);
    let parsedJSON;
    try {
        parsedJSON = JSON.parse(jsonString);
        return parsedJSON;
    } catch (error) {
        try {
            parsedJSON = JSON.parse(formatMarkdownOrderedList(escapeControlChars(removeNewlinesFromJSON(jsonString))));
            return parsedJSON;
        } catch (err) {
            try {
                let processedStr = escapeQuoteFromValue(formatMarkdownOrderedList(escapeControlChars(removeNewlinesFromJSON(jsonString))));
                parsedJSON = JSON.parse(processedStr);
                return parsedJSON;
            } catch (err1) {
                console.error("failed to parse json...........", err1, jsonString);
            }
        }
    }
};


/***/ }),

/***/ 9399:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "j": () => (/* binding */ getSvgDimensions),
/* harmony export */   "w": () => (/* binding */ preprocessSvg)
/* harmony export */ });
// 在文件顶部添加这个新的辅助函数
const convertAttributeValue = (value, totalSize)=>{
    if (typeof value !== "string") return value;
    // 处理百分比
    if (value.endsWith("%")) {
        const percentage = parseFloat(value) / 100;
        return totalSize * percentage;
    }
    // 处理带单位的值（如 px）
    const numericValue = parseFloat(value);
    return isNaN(numericValue) ? value : numericValue;
};
const preprocessSvg = (svgString)=>{
    // 移除 @import 语句
    const cleanedSrc = svgString.replace(/@import\s+url\([^)]+\);?/g, "");
    // 使用DOMParser解析清理后的SVG字符串
    const parser = new DOMParser();
    const doc = parser.parseFromString(cleanedSrc, "image/svg+xml");
    const svgElement = doc.documentElement;
    if (svgElement.tagName === "parsererror") {
        throw new Error("Invalid SVG content");
    }
    // 获取 SVG 的宽度和高度
    const svgWidth = parseFloat(svgElement.getAttribute("width")) || svgElement.viewBox?.baseVal?.width;
    const svgHeight = parseFloat(svgElement.getAttribute("height")) || svgElement.viewBox?.baseVal?.height;
    // 处理所有元素的 x, y, width, height 属性
    const allElements = svgElement.getElementsByTagName("*");
    for (let element of allElements){
        [
            "x",
            "y",
            "width",
            "height"
        ].forEach((attr)=>{
            if (element.hasAttribute(attr)) {
                const value = element.getAttribute(attr);
                const convertedValue = convertAttributeValue(value, attr === "x" || attr === "width" ? svgWidth : svgHeight);
                element.setAttribute(attr, convertedValue);
            }
        });
    }
    // 设置 viewBox 以保持宽高比
    if (!svgElement.getAttribute("viewBox")) {
        const width = svgWidth || svgElement.width?.baseVal?.value || 400;
        const height = svgHeight || svgElement.height?.baseVal?.value || 800;
        svgElement.setAttribute("viewBox", `0 0 ${width} ${height}`);
    }
    //   设置 preserveAspectRatio 以确保 SVG 完整显示
    svgElement.setAttribute("preserveAspectRatio", "xMidYMid meet");
    // 处理文本换行
    wrapLongText(svgElement);
    // 将修改后的SVG转换回字符串
    const serializer = new XMLSerializer();
    return serializer.serializeToString(svgElement);
};
const wrapLongText = (svgElement)=>{
    const texts = svgElement.querySelectorAll("text");
    texts.forEach((text)=>{
        const x = parseFloat(text.getAttribute("x") || 0);
        const y = parseFloat(text.getAttribute("y") || 0);
        const textWidth = parseFloat(text.getAttribute("width") || 0);
        // 使用 text 元素的 width 属性（如果存在），否则使用 SVG 宽度的 80%
        const svgWidth = parseFloat(svgElement.getAttribute("width") || svgElement.viewBox?.baseVal?.width);
        const maxWidth = textWidth || svgWidth * 0.8;
        const tspans = text.querySelectorAll("tspan");
        if (tspans.length > 0) {
            tspans.forEach((tspan, index)=>{
                tspan.setAttribute("x", x);
                if (index > 0) {
                    tspan.setAttribute("dy", "1.2em");
                } else {
                    tspan.setAttribute("y", y);
                }
            });
        } else {
            const textContent = text.textContent;
            const words = textContent.split(/\s+/);
            text.textContent = "";
            let line = "";
            let tspan = document.createElementNS("http://www.w3.org/2000/svg", "tspan");
            tspan.setAttribute("x", x);
            tspan.setAttribute("y", y);
            text.appendChild(tspan);
            words.forEach((word, index)=>{
                const testLine = line + (line ? " " : "") + word;
                const testWidth = getTextWidth(testLine + " ", text, svgElement);
                // console.log('testWidth..............', testWidth, maxWidth, testLine);
                if (testWidth > maxWidth && index > 0) {
                    tspan.textContent = line;
                    line = word;
                    tspan = document.createElementNS("http://www.w3.org/2000/svg", "tspan");
                    tspan.setAttribute("x", x);
                    tspan.setAttribute("dy", "1.2em");
                    text.appendChild(tspan);
                } else {
                    line = testLine;
                }
            });
            tspan.textContent = line;
        }
    });
};
const extractStylesFromSvg = (svgElement)=>{
    const styles = {};
    const styleElements = svgElement.getElementsByTagName("style");
    for (let styleEl of styleElements){
        const cssText = styleEl.textContent;
        const rules = cssText.match(/[^\{\}]+\{[^\}]+\}/g);
        if (rules) {
            rules.forEach((rule)=>{
                const [selector, declaration] = rule.split("{");
                const properties = declaration.replace("}", "").split(";");
                properties.forEach((prop)=>{
                    const [key, value] = prop.split(":").map((s)=>s.trim());
                    if (key && value) {
                        if (!styles[selector.trim()]) {
                            styles[selector.trim()] = {};
                        }
                        styles[selector.trim()][key] = value;
                    }
                });
            });
        }
    }
    return styles;
};
const getTextWidth = (text, element, svgElement)=>{
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    // 提取 SVG 中的样式
    const svgStyles = extractStylesFromSvg(svgElement);
    //   console.log('svgStyles..............', svgStyles);
    // 尝试获取元素的类名
    let fontSize = element.getAttribute("font-size");
    let fontFamily = element.getAttribute("font-family");
    const className = element.getAttribute("class");
    // 如果元素有类名，尝试从 SVG 样式中获取字体信息
    if (className && svgStyles[`.${className}`]) {
        fontSize = fontSize || svgStyles[`.${className}`]["font-size"];
        fontFamily = fontFamily || svgStyles[`.${className}`]["font-family"];
    }
    fontSize = fontSize || svgStyles["text"] && svgStyles["text"]["font-size"] || svgStyles[".content"] && svgStyles[".content"]["font-size"] || "14px";
    fontFamily = fontFamily || svgStyles["text"] && svgStyles["text"]["font-family"] || svgStyles[".content"] && svgStyles[".content"]["font-family"] || "Arial, sans-serif";
    //   console.log('Font info:', parseInt(fontSize), fontFamily);
    context.font = `${fontSize}px ${fontFamily}`;
    return context.measureText(text).width;
};
function getSvgDimensions(svgString) {
    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(svgString.replace(/@import\s+url\([^)]+\);?/g, ""), "image/svg+xml");
    const svgElement = svgDoc.documentElement;
    // console.log('svgElement..............', svgElement, svgElement.getAttribute('width'), svgElement.getAttribute('height'), svgElement.viewBox);
    let width = svgElement.getAttribute("width") || svgElement.viewBox?.baseVal?.width;
    let height = svgElement.getAttribute("height") || svgElement.viewBox?.baseVal?.height;
    // 如果没有明确的宽度和高度属性，尝试从 viewBox 中获取
    if (!width || !height) {
        const viewBox = svgElement.getAttribute("viewBox");
        if (viewBox) {
            const [, , vbWidth, vbHeight] = viewBox.split(" ").map(Number);
            width = width || vbWidth;
            height = height || vbHeight;
        }
    }
    // 处理可能的百分比或带单位的值
    // const totalWidth = svgElement.viewBox?.baseVal?.width || 300;
    // const totalHeight = svgElement.viewBox?.baseVal?.height || 150;
    // width = convertAttributeValue(width, totalWidth);
    // height = convertAttributeValue(height, totalHeight);
    // // 如果仍然没有有效的宽度或高度，设置默认值
    // if (isNaN(width) || width <= 0) width = 300;
    // if (isNaN(height) || height <= 0) height = 150;
    return {
        width,
        height
    };
}


/***/ }),

/***/ 2944:
/***/ (() => {



/***/ }),

/***/ 4810:
/***/ (() => {



/***/ })

};
;