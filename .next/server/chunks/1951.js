"use strict";
exports.id = 1951;
exports.ids = [1951];
exports.modules = {

/***/ 1951:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "H": () => (/* binding */ AuthProvider),
/* harmony export */   "a": () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7597);



const AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();
const AuthProvider = ({ children  })=>{
    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const openLoginModal = ()=>{
        setIsLoginModalOpen(true);
    };
    const closeLoginModal = ()=>setIsLoginModalOpen(false);
    const checkUserInfo = async ()=>{
        try {
            const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_2__/* .apiUrl */ .J}/users/info`, {
                method: "GET",
                credentials: "include"
            });
            if (response.ok) {
                const result = await response.json();
                setUser(result.data);
                setIsLoggedIn(true);
                // 同步到 localStorage，方便下次快速恢复
                localStorage.setItem("isLoggedIn", "true");
                localStorage.setItem("user", JSON.stringify(result.data));
                // 如果登录成功且 modal 是打开的，关闭 modal
                if (isLoginModalOpen) {
                    setIsLoginModalOpen(false);
                }
                return true;
            } else if (response.status === 403) {
                setIsLoggedIn(false);
                setUser(null);
                localStorage.removeItem("isLoggedIn");
                localStorage.removeItem("user");
                // onLoginRequired?.();
                return false;
            } else {
                throw new Error("Failed to fetch user info");
            }
        } catch (error) {
            console.error("Error fetching user info:", error);
            setIsLoggedIn(false);
            setUser(null);
            return false;
        }
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        checkUserInfo();
    }, [
        _utils_apiUtils__WEBPACK_IMPORTED_MODULE_2__/* .apiUrl */ .J
    ]);
    const logon = (user)=>{
        localStorage.setItem("isLoggedIn", "true");
        localStorage.setItem("user", JSON.stringify(user));
        setUser(user);
        setIsLoggedIn(true);
        setIsLoginModalOpen(false);
    };
    const logout = ()=>{
        localStorage.removeItem("isLoggedIn");
        localStorage.removeItem("user");
        setUser(null);
        setIsLoggedIn(false);
    };
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(AuthContext.Provider, {
        value: {
            isLoggedIn,
            logon,
            logout,
            isLoginModalOpen,
            openLoginModal,
            closeLoginModal,
            user,
            checkUserInfo
        },
        children: children
    });
};
const useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);


/***/ }),

/***/ 7597:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "J": () => (/* binding */ apiUrl)
/* harmony export */ });
const apiUrl = "https://www.funblocks.net/service" || 0;


/***/ })

};
;