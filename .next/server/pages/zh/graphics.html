<!DOCTYPE html><html lang="zh"><head><meta charSet="utf-8"/><link rel="icon" href="/aitools/icon.png"/><meta name="description" content="使用FunBlocks AI洞察生成有趣而富有见地的AI卡片。探索、创建和分享独特的AI生成内容。"/><meta property="og:title" content="FunBlocks AI洞察 - AI生成的趣味洞察卡片"/><meta property="og:description" content="使用FunBlocks AI洞察生成有趣而富有见地的AI卡片。探索、创建和分享独特的AI生成内容。"/><meta property="og:image" content="https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png"/><meta property="og:url" content="https://www.funblocks.net/aitools"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="FunBlocks AI洞察 - AI生成的趣味洞察卡片"/><meta name="twitter:description" content="使用FunBlocks AI洞察生成有趣而富有见地的AI卡片。探索、创建和分享独特的AI生成内容。"/><meta name="twitter:image" content="https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png"/><link rel="canonical" href="https://www.funblocks.net/aitools"/><title>AI Graphics Generator: Create Infographics, Flowcharts &amp; Data Visualizations | FunBlocks AI</title><meta name="description" content="Transform complex information into stunning infographics, flowcharts, and data visualizations in seconds with our AI Graphics Generator. No design skills needed - perfect for presentations, social media, and reports."/><meta name="keywords" content="AI Infographic generator, AI flowchart generator, data chart creator, AI SVG Code generator, data visualization, visual content, infographic maker, flowchart maker, chart generator, SVG creator, AI graphics tool, process diagram, timeline generator, concept map, comparison table, FunBlocks AI"/><meta property="og:type" content="website"/><meta property="og:title" content="AI Graphics Generator: Create Infographics, Flowcharts &amp; Data Visualizations | FunBlocks AI"/><meta property="og:description" content="Transform complex information into stunning infographics, flowcharts, and data visualizations in seconds with our AI Graphics Generator. No design skills needed - perfect for presentations, social media, and reports."/><meta property="og:image" content="/og-image.png"/><meta property="og:url" content="/aitools/graphics"/><meta property="og:site_name" content="FunBlocks AI"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="AI Graphics Generator: Create Infographics, Flowcharts &amp; Data Visualizations | FunBlocks AI"/><meta name="twitter:description" content="Transform complex information into stunning infographics, flowcharts, and data visualizations in seconds with our AI Graphics Generator. No design skills needed - perfect for presentations, social media, and reports."/><meta name="twitter:image" content="/og-image.png"/><link rel="canonical" href="https://www.funblocks.net/aitools/graphics"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><meta name="robots" content="index, follow"/><meta name="next-head-count" content="30"/><link data-next-font="" rel="preconnect" href="/" crossorigin="anonymous"/><link rel="preload" href="/aitools/_next/static/css/9c4ca3c9d1c3cac0.css" as="style"/><link rel="stylesheet" href="/aitools/_next/static/css/9c4ca3c9d1c3cac0.css" data-n-g=""/><link rel="preload" href="/aitools/_next/static/css/0eca55213de89ff7.css" as="style"/><link rel="stylesheet" href="/aitools/_next/static/css/0eca55213de89ff7.css" data-n-p=""/><link rel="preload" href="/aitools/_next/static/css/5c21647854bdc450.css" as="style"/><link rel="stylesheet" href="/aitools/_next/static/css/5c21647854bdc450.css" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/aitools/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js"></script><script src="/aitools/_next/static/chunks/webpack-d93499e80b8e9f5a.js" defer=""></script><script src="/aitools/_next/static/chunks/framework-ce84985cd166733a.js" defer=""></script><script src="/aitools/_next/static/chunks/main-c9990a6b8fe0188b.js" defer=""></script><script src="/aitools/_next/static/chunks/pages/_app-a8c5f292875b6742.js" defer=""></script><script src="/aitools/_next/static/chunks/fea29d9f-26f0b9bae002a563.js" defer=""></script><script src="/aitools/_next/static/chunks/cb355538-be3cbfc14821f5f3.js" defer=""></script><script src="/aitools/_next/static/chunks/175675d1-df2fae392ee7e47f.js" defer=""></script><script src="/aitools/_next/static/chunks/e8c4b161-dfadda4c56049bc3.js" defer=""></script><script src="/aitools/_next/static/chunks/8e31ca6e-e1742938274be730.js" defer=""></script><script src="/aitools/_next/static/chunks/85d7bc83-dbab93147734e483.js" defer=""></script><script src="/aitools/_next/static/chunks/4577d2ec-9551474f23e7f9c6.js" defer=""></script><script src="/aitools/_next/static/chunks/2e3a845b-624dc23328dbbda0.js" defer=""></script><script src="/aitools/_next/static/chunks/8417-86f1e01a61e6e142.js" defer=""></script><script src="/aitools/_next/static/chunks/5460-8ffca87d21ace7e3.js" defer=""></script><script src="/aitools/_next/static/chunks/1582-7c46829d104acda8.js" defer=""></script><script src="/aitools/_next/static/chunks/2658-c86dec41f550a2a6.js" defer=""></script><script src="/aitools/_next/static/chunks/1664-c4204b3d8b82bc4e.js" defer=""></script><script src="/aitools/_next/static/chunks/7919-0f2d39a0d2f50927.js" defer=""></script><script src="/aitools/_next/static/chunks/901-343dc776f0b17f40.js" defer=""></script><script src="/aitools/_next/static/chunks/2613-d27b8cbda12f8cf9.js" defer=""></script><script src="/aitools/_next/static/chunks/5408-7d1e5ee45830d3c3.js" defer=""></script><script src="/aitools/_next/static/chunks/6734-60624c22e0655dcb.js" defer=""></script><script src="/aitools/_next/static/chunks/5794-b108689e9fd865fb.js" defer=""></script><script src="/aitools/_next/static/chunks/pages/graphics-19bb7ceae78912ec.js" defer=""></script><script src="/aitools/_next/static/nm9AgT6Nhv6t9TwNeg-eH/_buildManifest.js" defer=""></script><script src="/aitools/_next/static/nm9AgT6Nhv6t9TwNeg-eH/_ssgManifest.js" defer=""></script></head><body><div id="__next"><style data-emotion="css-global rh8y69">:host,:root,[data-theme]{--chakra-ring-inset:var(--chakra-empty,/*!*/ /*!*/);--chakra-ring-offset-width:0px;--chakra-ring-offset-color:#fff;--chakra-ring-color:rgba(66, 153, 225, 0.6);--chakra-ring-offset-shadow:0 0 #0000;--chakra-ring-shadow:0 0 #0000;--chakra-space-x-reverse:0;--chakra-space-y-reverse:0;--chakra-colors-transparent:transparent;--chakra-colors-current:currentColor;--chakra-colors-black:#000000;--chakra-colors-white:#FFFFFF;--chakra-colors-whiteAlpha-50:rgba(255, 255, 255, 0.04);--chakra-colors-whiteAlpha-100:rgba(255, 255, 255, 0.06);--chakra-colors-whiteAlpha-200:rgba(255, 255, 255, 0.08);--chakra-colors-whiteAlpha-300:rgba(255, 255, 255, 0.16);--chakra-colors-whiteAlpha-400:rgba(255, 255, 255, 0.24);--chakra-colors-whiteAlpha-500:rgba(255, 255, 255, 0.36);--chakra-colors-whiteAlpha-600:rgba(255, 255, 255, 0.48);--chakra-colors-whiteAlpha-700:rgba(255, 255, 255, 0.64);--chakra-colors-whiteAlpha-800:rgba(255, 255, 255, 0.80);--chakra-colors-whiteAlpha-900:rgba(255, 255, 255, 0.92);--chakra-colors-blackAlpha-50:rgba(0, 0, 0, 0.04);--chakra-colors-blackAlpha-100:rgba(0, 0, 0, 0.06);--chakra-colors-blackAlpha-200:rgba(0, 0, 0, 0.08);--chakra-colors-blackAlpha-300:rgba(0, 0, 0, 0.16);--chakra-colors-blackAlpha-400:rgba(0, 0, 0, 0.24);--chakra-colors-blackAlpha-500:rgba(0, 0, 0, 0.36);--chakra-colors-blackAlpha-600:rgba(0, 0, 0, 0.48);--chakra-colors-blackAlpha-700:rgba(0, 0, 0, 0.64);--chakra-colors-blackAlpha-800:rgba(0, 0, 0, 0.80);--chakra-colors-blackAlpha-900:rgba(0, 0, 0, 0.92);--chakra-colors-gray-50:#F7FAFC;--chakra-colors-gray-100:#EDF2F7;--chakra-colors-gray-200:#E2E8F0;--chakra-colors-gray-300:#CBD5E0;--chakra-colors-gray-400:#A0AEC0;--chakra-colors-gray-500:#718096;--chakra-colors-gray-600:#4A5568;--chakra-colors-gray-700:#2D3748;--chakra-colors-gray-800:#1A202C;--chakra-colors-gray-900:#171923;--chakra-colors-red-50:#FFF5F5;--chakra-colors-red-100:#FED7D7;--chakra-colors-red-200:#FEB2B2;--chakra-colors-red-300:#FC8181;--chakra-colors-red-400:#F56565;--chakra-colors-red-500:#E53E3E;--chakra-colors-red-600:#C53030;--chakra-colors-red-700:#9B2C2C;--chakra-colors-red-800:#822727;--chakra-colors-red-900:#63171B;--chakra-colors-orange-50:#FFFAF0;--chakra-colors-orange-100:#FEEBC8;--chakra-colors-orange-200:#FBD38D;--chakra-colors-orange-300:#F6AD55;--chakra-colors-orange-400:#ED8936;--chakra-colors-orange-500:#DD6B20;--chakra-colors-orange-600:#C05621;--chakra-colors-orange-700:#9C4221;--chakra-colors-orange-800:#7B341E;--chakra-colors-orange-900:#652B19;--chakra-colors-yellow-50:#FFFFF0;--chakra-colors-yellow-100:#FEFCBF;--chakra-colors-yellow-200:#FAF089;--chakra-colors-yellow-300:#F6E05E;--chakra-colors-yellow-400:#ECC94B;--chakra-colors-yellow-500:#D69E2E;--chakra-colors-yellow-600:#B7791F;--chakra-colors-yellow-700:#975A16;--chakra-colors-yellow-800:#744210;--chakra-colors-yellow-900:#5F370E;--chakra-colors-green-50:#F0FFF4;--chakra-colors-green-100:#C6F6D5;--chakra-colors-green-200:#9AE6B4;--chakra-colors-green-300:#68D391;--chakra-colors-green-400:#48BB78;--chakra-colors-green-500:#38A169;--chakra-colors-green-600:#2F855A;--chakra-colors-green-700:#276749;--chakra-colors-green-800:#22543D;--chakra-colors-green-900:#1C4532;--chakra-colors-teal-50:#E6FFFA;--chakra-colors-teal-100:#B2F5EA;--chakra-colors-teal-200:#81E6D9;--chakra-colors-teal-300:#4FD1C5;--chakra-colors-teal-400:#38B2AC;--chakra-colors-teal-500:#319795;--chakra-colors-teal-600:#2C7A7B;--chakra-colors-teal-700:#285E61;--chakra-colors-teal-800:#234E52;--chakra-colors-teal-900:#1D4044;--chakra-colors-blue-50:#ebf8ff;--chakra-colors-blue-100:#bee3f8;--chakra-colors-blue-200:#90cdf4;--chakra-colors-blue-300:#63b3ed;--chakra-colors-blue-400:#4299e1;--chakra-colors-blue-500:#3182ce;--chakra-colors-blue-600:#2b6cb0;--chakra-colors-blue-700:#2c5282;--chakra-colors-blue-800:#2a4365;--chakra-colors-blue-900:#1A365D;--chakra-colors-cyan-50:#EDFDFD;--chakra-colors-cyan-100:#C4F1F9;--chakra-colors-cyan-200:#9DECF9;--chakra-colors-cyan-300:#76E4F7;--chakra-colors-cyan-400:#0BC5EA;--chakra-colors-cyan-500:#00B5D8;--chakra-colors-cyan-600:#00A3C4;--chakra-colors-cyan-700:#0987A0;--chakra-colors-cyan-800:#086F83;--chakra-colors-cyan-900:#065666;--chakra-colors-purple-50:#FAF5FF;--chakra-colors-purple-100:#E9D8FD;--chakra-colors-purple-200:#D6BCFA;--chakra-colors-purple-300:#B794F4;--chakra-colors-purple-400:#9F7AEA;--chakra-colors-purple-500:#805AD5;--chakra-colors-purple-600:#6B46C1;--chakra-colors-purple-700:#553C9A;--chakra-colors-purple-800:#44337A;--chakra-colors-purple-900:#322659;--chakra-colors-pink-50:#FFF5F7;--chakra-colors-pink-100:#FED7E2;--chakra-colors-pink-200:#FBB6CE;--chakra-colors-pink-300:#F687B3;--chakra-colors-pink-400:#ED64A6;--chakra-colors-pink-500:#D53F8C;--chakra-colors-pink-600:#B83280;--chakra-colors-pink-700:#97266D;--chakra-colors-pink-800:#702459;--chakra-colors-pink-900:#521B41;--chakra-borders-none:0;--chakra-borders-1px:1px solid;--chakra-borders-2px:2px solid;--chakra-borders-4px:4px solid;--chakra-borders-8px:8px solid;--chakra-fonts-heading:-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--chakra-fonts-body:-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--chakra-fonts-mono:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--chakra-fontSizes-3xs:0.45rem;--chakra-fontSizes-2xs:0.625rem;--chakra-fontSizes-xs:0.75rem;--chakra-fontSizes-sm:0.875rem;--chakra-fontSizes-md:1rem;--chakra-fontSizes-lg:1.125rem;--chakra-fontSizes-xl:1.25rem;--chakra-fontSizes-2xl:1.5rem;--chakra-fontSizes-3xl:1.875rem;--chakra-fontSizes-4xl:2.25rem;--chakra-fontSizes-5xl:3rem;--chakra-fontSizes-6xl:3.75rem;--chakra-fontSizes-7xl:4.5rem;--chakra-fontSizes-8xl:6rem;--chakra-fontSizes-9xl:8rem;--chakra-fontWeights-hairline:100;--chakra-fontWeights-thin:200;--chakra-fontWeights-light:300;--chakra-fontWeights-normal:400;--chakra-fontWeights-medium:500;--chakra-fontWeights-semibold:600;--chakra-fontWeights-bold:700;--chakra-fontWeights-extrabold:800;--chakra-fontWeights-black:900;--chakra-letterSpacings-tighter:-0.05em;--chakra-letterSpacings-tight:-0.025em;--chakra-letterSpacings-normal:0;--chakra-letterSpacings-wide:0.025em;--chakra-letterSpacings-wider:0.05em;--chakra-letterSpacings-widest:0.1em;--chakra-lineHeights-3:.75rem;--chakra-lineHeights-4:1rem;--chakra-lineHeights-5:1.25rem;--chakra-lineHeights-6:1.5rem;--chakra-lineHeights-7:1.75rem;--chakra-lineHeights-8:2rem;--chakra-lineHeights-9:2.25rem;--chakra-lineHeights-10:2.5rem;--chakra-lineHeights-normal:normal;--chakra-lineHeights-none:1;--chakra-lineHeights-shorter:1.25;--chakra-lineHeights-short:1.375;--chakra-lineHeights-base:1.5;--chakra-lineHeights-tall:1.625;--chakra-lineHeights-taller:2;--chakra-radii-none:0;--chakra-radii-sm:0.125rem;--chakra-radii-base:0.25rem;--chakra-radii-md:0.375rem;--chakra-radii-lg:0.5rem;--chakra-radii-xl:0.75rem;--chakra-radii-2xl:1rem;--chakra-radii-3xl:1.5rem;--chakra-radii-full:9999px;--chakra-space-1:0.25rem;--chakra-space-2:0.5rem;--chakra-space-3:0.75rem;--chakra-space-4:1rem;--chakra-space-5:1.25rem;--chakra-space-6:1.5rem;--chakra-space-7:1.75rem;--chakra-space-8:2rem;--chakra-space-9:2.25rem;--chakra-space-10:2.5rem;--chakra-space-12:3rem;--chakra-space-14:3.5rem;--chakra-space-16:4rem;--chakra-space-20:5rem;--chakra-space-24:6rem;--chakra-space-28:7rem;--chakra-space-32:8rem;--chakra-space-36:9rem;--chakra-space-40:10rem;--chakra-space-44:11rem;--chakra-space-48:12rem;--chakra-space-52:13rem;--chakra-space-56:14rem;--chakra-space-60:15rem;--chakra-space-64:16rem;--chakra-space-72:18rem;--chakra-space-80:20rem;--chakra-space-96:24rem;--chakra-space-px:1px;--chakra-space-0-5:0.125rem;--chakra-space-1-5:0.375rem;--chakra-space-2-5:0.625rem;--chakra-space-3-5:0.875rem;--chakra-shadows-xs:0 0 0 1px rgba(0, 0, 0, 0.05);--chakra-shadows-sm:0 1px 2px 0 rgba(0, 0, 0, 0.05);--chakra-shadows-base:0 1px 3px 0 rgba(0, 0, 0, 0.1),0 1px 2px 0 rgba(0, 0, 0, 0.06);--chakra-shadows-md:0 4px 6px -1px rgba(0, 0, 0, 0.1),0 2px 4px -1px rgba(0, 0, 0, 0.06);--chakra-shadows-lg:0 10px 15px -3px rgba(0, 0, 0, 0.1),0 4px 6px -2px rgba(0, 0, 0, 0.05);--chakra-shadows-xl:0 20px 25px -5px rgba(0, 0, 0, 0.1),0 10px 10px -5px rgba(0, 0, 0, 0.04);--chakra-shadows-2xl:0 25px 50px -12px rgba(0, 0, 0, 0.25);--chakra-shadows-outline:0 0 0 3px rgba(66, 153, 225, 0.6);--chakra-shadows-inner:inset 0 2px 4px 0 rgba(0,0,0,0.06);--chakra-shadows-none:none;--chakra-shadows-dark-lg:rgba(0, 0, 0, 0.1) 0px 0px 0px 1px,rgba(0, 0, 0, 0.2) 0px 5px 10px,rgba(0, 0, 0, 0.4) 0px 15px 40px;--chakra-sizes-1:0.25rem;--chakra-sizes-2:0.5rem;--chakra-sizes-3:0.75rem;--chakra-sizes-4:1rem;--chakra-sizes-5:1.25rem;--chakra-sizes-6:1.5rem;--chakra-sizes-7:1.75rem;--chakra-sizes-8:2rem;--chakra-sizes-9:2.25rem;--chakra-sizes-10:2.5rem;--chakra-sizes-12:3rem;--chakra-sizes-14:3.5rem;--chakra-sizes-16:4rem;--chakra-sizes-20:5rem;--chakra-sizes-24:6rem;--chakra-sizes-28:7rem;--chakra-sizes-32:8rem;--chakra-sizes-36:9rem;--chakra-sizes-40:10rem;--chakra-sizes-44:11rem;--chakra-sizes-48:12rem;--chakra-sizes-52:13rem;--chakra-sizes-56:14rem;--chakra-sizes-60:15rem;--chakra-sizes-64:16rem;--chakra-sizes-72:18rem;--chakra-sizes-80:20rem;--chakra-sizes-96:24rem;--chakra-sizes-px:1px;--chakra-sizes-0-5:0.125rem;--chakra-sizes-1-5:0.375rem;--chakra-sizes-2-5:0.625rem;--chakra-sizes-3-5:0.875rem;--chakra-sizes-max:max-content;--chakra-sizes-min:min-content;--chakra-sizes-full:100%;--chakra-sizes-3xs:14rem;--chakra-sizes-2xs:16rem;--chakra-sizes-xs:20rem;--chakra-sizes-sm:24rem;--chakra-sizes-md:28rem;--chakra-sizes-lg:32rem;--chakra-sizes-xl:36rem;--chakra-sizes-2xl:42rem;--chakra-sizes-3xl:48rem;--chakra-sizes-4xl:56rem;--chakra-sizes-5xl:64rem;--chakra-sizes-6xl:72rem;--chakra-sizes-7xl:80rem;--chakra-sizes-8xl:90rem;--chakra-sizes-prose:60ch;--chakra-sizes-container-sm:640px;--chakra-sizes-container-md:768px;--chakra-sizes-container-lg:1024px;--chakra-sizes-container-xl:1280px;--chakra-zIndices-hide:-1;--chakra-zIndices-auto:auto;--chakra-zIndices-base:0;--chakra-zIndices-docked:10;--chakra-zIndices-dropdown:1000;--chakra-zIndices-sticky:1100;--chakra-zIndices-banner:1200;--chakra-zIndices-overlay:1300;--chakra-zIndices-modal:1400;--chakra-zIndices-popover:1500;--chakra-zIndices-skipLink:1600;--chakra-zIndices-toast:1700;--chakra-zIndices-tooltip:1800;--chakra-transition-property-common:background-color,border-color,color,fill,stroke,opacity,box-shadow,transform;--chakra-transition-property-colors:background-color,border-color,color,fill,stroke;--chakra-transition-property-dimensions:width,height;--chakra-transition-property-position:left,right,top,bottom;--chakra-transition-property-background:background-color,background-image,background-position;--chakra-transition-easing-ease-in:cubic-bezier(0.4, 0, 1, 1);--chakra-transition-easing-ease-out:cubic-bezier(0, 0, 0.2, 1);--chakra-transition-easing-ease-in-out:cubic-bezier(0.4, 0, 0.2, 1);--chakra-transition-duration-ultra-fast:50ms;--chakra-transition-duration-faster:100ms;--chakra-transition-duration-fast:150ms;--chakra-transition-duration-normal:200ms;--chakra-transition-duration-slow:300ms;--chakra-transition-duration-slower:400ms;--chakra-transition-duration-ultra-slow:500ms;--chakra-blur-none:0;--chakra-blur-sm:4px;--chakra-blur-base:8px;--chakra-blur-md:12px;--chakra-blur-lg:16px;--chakra-blur-xl:24px;--chakra-blur-2xl:40px;--chakra-blur-3xl:64px;--chakra-breakpoints-base:0em;--chakra-breakpoints-sm:30em;--chakra-breakpoints-md:48em;--chakra-breakpoints-lg:62em;--chakra-breakpoints-xl:80em;--chakra-breakpoints-2xl:96em;}.chakra-ui-light :host:not([data-theme]),.chakra-ui-light :root:not([data-theme]),.chakra-ui-light [data-theme]:not([data-theme]),[data-theme=light] :host:not([data-theme]),[data-theme=light] :root:not([data-theme]),[data-theme=light] [data-theme]:not([data-theme]),:host[data-theme=light],:root[data-theme=light],[data-theme][data-theme=light]{--chakra-colors-chakra-body-text:var(--chakra-colors-gray-800);--chakra-colors-chakra-body-bg:var(--chakra-colors-white);--chakra-colors-chakra-border-color:var(--chakra-colors-gray-200);--chakra-colors-chakra-inverse-text:var(--chakra-colors-white);--chakra-colors-chakra-subtle-bg:var(--chakra-colors-gray-100);--chakra-colors-chakra-subtle-text:var(--chakra-colors-gray-600);--chakra-colors-chakra-placeholder-color:var(--chakra-colors-gray-500);}.chakra-ui-dark :host:not([data-theme]),.chakra-ui-dark :root:not([data-theme]),.chakra-ui-dark [data-theme]:not([data-theme]),[data-theme=dark] :host:not([data-theme]),[data-theme=dark] :root:not([data-theme]),[data-theme=dark] [data-theme]:not([data-theme]),:host[data-theme=dark],:root[data-theme=dark],[data-theme][data-theme=dark]{--chakra-colors-chakra-body-text:var(--chakra-colors-whiteAlpha-900);--chakra-colors-chakra-body-bg:var(--chakra-colors-gray-800);--chakra-colors-chakra-border-color:var(--chakra-colors-whiteAlpha-300);--chakra-colors-chakra-inverse-text:var(--chakra-colors-gray-800);--chakra-colors-chakra-subtle-bg:var(--chakra-colors-gray-700);--chakra-colors-chakra-subtle-text:var(--chakra-colors-gray-400);--chakra-colors-chakra-placeholder-color:var(--chakra-colors-whiteAlpha-400);}</style><style data-emotion="css-global fubdgu">html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:system-ui,sans-serif;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;touch-action:manipulation;}body{position:relative;min-height:100%;margin:0;font-feature-settings:"kern";}:where(*, *::before, *::after){border-width:0;border-style:solid;box-sizing:border-box;word-wrap:break-word;}main{display:block;}hr{border-top-width:1px;box-sizing:content-box;height:0;overflow:visible;}:where(pre, code, kbd,samp){font-family:SFMono-Regular,Menlo,Monaco,Consolas,monospace;font-size:1em;}a{background-color:transparent;color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit;}abbr[title]{border-bottom:none;-webkit-text-decoration:underline;text-decoration:underline;-webkit-text-decoration:underline dotted;-webkit-text-decoration:underline dotted;text-decoration:underline dotted;}:where(b, strong){font-weight:bold;}small{font-size:80%;}:where(sub,sup){font-size:75%;line-height:0;position:relative;vertical-align:baseline;}sub{bottom:-0.25em;}sup{top:-0.5em;}img{border-style:none;}:where(button, input, optgroup, select, textarea){font-family:inherit;font-size:100%;line-height:1.15;margin:0;}:where(button, input){overflow:visible;}:where(button, select){text-transform:none;}:where(
          button::-moz-focus-inner,
          [type="button"]::-moz-focus-inner,
          [type="reset"]::-moz-focus-inner,
          [type="submit"]::-moz-focus-inner
        ){border-style:none;padding:0;}fieldset{padding:0.35em 0.75em 0.625em;}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal;}progress{vertical-align:baseline;}textarea{overflow:auto;}:where([type="checkbox"], [type="radio"]){box-sizing:border-box;padding:0;}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{-webkit-appearance:none!important;}input[type="number"]{-moz-appearance:textfield;}input[type="search"]{-webkit-appearance:textfield;outline-offset:-2px;}input[type="search"]::-webkit-search-decoration{-webkit-appearance:none!important;}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit;}details{display:block;}summary{display:-webkit-box;display:-webkit-list-item;display:-ms-list-itembox;display:list-item;}template{display:none;}[hidden]{display:none!important;}:where(
          blockquote,
          dl,
          dd,
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          hr,
          figure,
          p,
          pre
        ){margin:0;}button{background:transparent;padding:0;}fieldset{margin:0;padding:0;}:where(ol, ul){margin:0;padding:0;}textarea{resize:vertical;}:where(button, [role="button"]){cursor:pointer;}button::-moz-focus-inner{border:0!important;}table{border-collapse:collapse;}:where(h1, h2, h3, h4, h5, h6){font-size:inherit;font-weight:inherit;}:where(button, input, optgroup, select, textarea){padding:0;line-height:inherit;color:inherit;}:where(img, svg, video, canvas, audio, iframe, embed, object){display:block;}:where(img, video){max-width:100%;height:auto;}[data-js-focus-visible] :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ){outline:none;box-shadow:none;}select::-ms-expand{display:none;}:root,:host{--chakra-vh:100vh;}@supports (height: -webkit-fill-available){:root,:host{--chakra-vh:-webkit-fill-available;}}@supports (height: -moz-fill-available){:root,:host{--chakra-vh:-moz-fill-available;}}@supports (height: 100dvh){:root,:host{--chakra-vh:100dvh;}}</style><style data-emotion="css-global 1cgn62j">body{font-family:var(--chakra-fonts-body);color:var(--chakra-colors-chakra-body-text);background:var(--chakra-colors-chakra-body-bg);transition-property:background-color;transition-duration:var(--chakra-transition-duration-normal);line-height:var(--chakra-lineHeights-base);}*::-webkit-input-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*::-moz-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*:-ms-input-placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*::placeholder{color:var(--chakra-colors-chakra-placeholder-color);}*,*::before,::after{border-color:var(--chakra-colors-chakra-border-color);}</style><script type="application/ld+json">{"@context":"https://schema.org","@type":"Organization","name":"FunBlocks AI","url":"https://www.funblocks.net","logo":"/aitools/icon.png","description":"通过可视化界面与AI互动——超越文本聊天框。","sameAs":["https://twitter.com/funblocks_ai"]}</script><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","url":"https://www.funblocks.net/aitools","name":"FunBlocks AI Tools","description":"探索FunBlocks AI的智能工具套件：AI图形、思维导图生成器、头脑风暴助手、演示文稿创建器、决策分析器和任务规划器。通过我们的可视化AI工具提升您的生产力和创造力。"}</script><script type="application/ld+json">{"@context":"https://schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"什么是 FunBlocks AI Graphics？","acceptedAnswer":{"@type":"Answer","text":"FunBlocks AI Graphics是一款利用大语言模型（LLM）技术生成有趣且富有洞见的 SVG 信息图卡的工具。它可以帮助用户创建适合社交媒体、演示或个人笔记的可分享内容。"}},{"@type":"Question","name":"FunBlocks AI Graphics能做什么？","acceptedAnswer":{"@type":"Answer","text":"它可以生成适用于社交媒体、演示、个人笔记，甚至仅为乐趣而制作的有趣且富有洞见的 SVG 信息图卡。"}},{"@type":"Question","name":"使用这个工具需要设计技能吗？","acceptedAnswer":{"@type":"Answer","text":"完全不需要！我们的 AI 会负责设计部分。你只需描述你想要的内容，AI 就会为你生成专业的图卡。"}},{"@type":"Question","name":"我可以制作哪些类型的图形？","acceptedAnswer":{"@type":"Answer","text":"你可以制作多种类型的可视化内容，包括信息图、流程图、步骤图、数据图表、时间线、对比表格等。"}},{"@type":"Question","name":"How does the AI Infographic Generator work?","acceptedAnswer":{"@type":"Answer","text":"Our AI Infographic Generator analyzes your input text or data and automatically creates visually appealing infographics that effectively communicate your information. It handles layout, color schemes, typography, and visual elements to produce professional-quality results in seconds."}},{"@type":"Question","name":"Can I create flowcharts with this AI tool?","acceptedAnswer":{"@type":"Answer","text":"Yes, our AI Flowchart Generator is specifically designed to create clear, professional flowcharts from your process descriptions. Simply describe the process or workflow, and the AI will generate a structured flowchart with proper connections, decision points, and visual hierarchy."}},{"@type":"Question","name":"What types of data charts can I create?","acceptedAnswer":{"@type":"Answer","text":"You can create a wide variety of data charts including bar charts, pie charts, line graphs, area charts, scatter plots, bubble charts, radar charts, and more. Our AI analyzes your data and recommends the most effective chart type for your specific information."}},{"@type":"Question","name":"Does the tool generate SVG code that I can edit?","acceptedAnswer":{"@type":"Answer","text":"Yes, our AI SVG Code Generator creates editable SVG code that you can further customize in any vector graphics editor. This gives you the flexibility to use our AI-generated graphics as a starting point and then fine-tune them to your exact specifications."}},{"@type":"Question","name":"How can AI-generated graphics improve my presentations?","acceptedAnswer":{"@type":"Answer","text":"AI-generated graphics can transform complex data and concepts into clear, engaging visuals that capture attention and improve understanding. Studies show that presentations with quality visuals are 43% more persuasive and information retention increases by up to 65% when paired with relevant images."}},{"@type":"Question","name":"Can I use these graphics for commercial purposes?","acceptedAnswer":{"@type":"Answer","text":"Yes, all graphics generated by our AI tool can be used for both personal and commercial purposes. You retain full ownership of the content you create with our tool, making it perfect for business presentations, marketing materials, educational content, and more."}},{"@type":"Question","name":"How does the AI ensure my graphics are visually appealing?","acceptedAnswer":{"@type":"Answer","text":"Our AI is trained on design principles including color theory, typography, visual hierarchy, and composition. It automatically applies these principles to create balanced, professional graphics that effectively communicate your information while maintaining visual appeal."}},{"@type":"Question","name":"What makes FunBlocks AI Graphics different from other AI design tools?","acceptedAnswer":{"@type":"Answer","text":"FunBlocks AI Graphics specializes in information-rich visual content like infographics, flowcharts, and data visualizations, unlike general AI image generators. Our tool understands the principles of data visualization and information design, creating graphics that are not just visually appealing but also communicate complex information effectively."}}]}</script><script type="application/ld+json">{"@context":"https://schema.org","@type":"SoftwareApplication","name":"FunBlocks AI Graphics Generator","applicationCategory":"DesignApplication","operatingSystem":"Web","offers":{"@type":"Offer","price":"0","priceCurrency":"USD"},"description":"Create stunning infographics, flowcharts, data charts, and SVG code in seconds with our AI-powered graphics generator. Transform complex information into engaging visuals without design skills.","aggregateRating":{"@type":"AggregateRating","ratingValue":"4.8","ratingCount":"1250"},"featureList":["AI Infographic Generator","AI Flowchart Generator","Data Chart Creator","SVG Code Generator","Visual Content Creation","Process Diagram Designer","Timeline Generator","Comparison Table Maker","Concept Map Builder","Data Visualization Tool"],"applicationSubCategory":"Data Visualization","releaseNotes":"Latest version includes enhanced flowchart capabilities and improved data visualization options","screenshot":"https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_swot.png","softwareVersion":"2.0"}</script><style data-emotion="css 1f7oryc">.css-1f7oryc{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;position:-webkit-sticky;position:sticky;top:0px;width:100%;z-index:var(--chakra-zIndices-sticky);background:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-md);-webkit-transition:opacity 0.3s;transition:opacity 0.3s;opacity:1;pointer-events:auto;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;padding:var(--chakra-space-1);}</style><div class="css-1f7oryc"><style data-emotion="css 13sr8jm">.css-13sr8jm{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);height:var(--chakra-sizes-10);min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-md);border:1px solid;border-color:var(--chakra-colors-gray-200);color:var(--chakra-colors-gray-800);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;}.css-13sr8jm:focus-visible,.css-13sr8jm[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-13sr8jm:disabled,.css-13sr8jm[disabled],.css-13sr8jm[aria-disabled=true],.css-13sr8jm[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-13sr8jm:hover,.css-13sr8jm[data-hover]{background:var(--chakra-colors-gray-100);}.css-13sr8jm:hover:disabled,.css-13sr8jm[data-hover]:disabled,.css-13sr8jm:hover[disabled],.css-13sr8jm[data-hover][disabled],.css-13sr8jm:hover[aria-disabled=true],.css-13sr8jm[data-hover][aria-disabled=true],.css-13sr8jm:hover[data-disabled],.css-13sr8jm[data-hover][data-disabled]{background:initial;}.chakra-button__group[data-attached][data-orientation=horizontal]>.css-13sr8jm:not(:last-of-type){-webkit-margin-end:-1px;margin-inline-end:-1px;}.chakra-button__group[data-attached][data-orientation=vertical]>.css-13sr8jm:not(:last-of-type){margin-bottom:-1px;}.css-13sr8jm:active,.css-13sr8jm[data-active]{background:var(--chakra-colors-gray-200);}</style><button type="button" class="chakra-button chakra-menu__menu-button css-13sr8jm" aria-label="Options" id="menu-button-:Rcsbq6H1:" aria-expanded="false" aria-haspopup="menu" aria-controls="menu-list-:Rcsbq6H1:"><style data-emotion="css onkibi">.css-onkibi{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;vertical-align:middle;}</style><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon css-onkibi" aria-hidden="true"><path fill="currentColor" d="M 3 5 A 1.0001 1.0001 0 1 0 3 7 L 21 7 A 1.0001 1.0001 0 1 0 21 5 L 3 5 z M 3 11 A 1.0001 1.0001 0 1 0 3 13 L 21 13 A 1.0001 1.0001 0 1 0 21 11 L 3 11 z M 3 17 A 1.0001 1.0001 0 1 0 3 19 L 21 19 A 1.0001 1.0001 0 1 0 21 17 L 3 17 z"></path></svg></button><style data-emotion="css ktd6ms">.css-ktd6ms{z-index:var(--chakra-zIndices-dropdown);}</style><div style="visibility:hidden;position:absolute;min-width:max-content;inset:0 auto auto 0" class="css-ktd6ms"><style data-emotion="css s5t7bz">.css-s5t7bz{outline:2px solid transparent;outline-offset:2px;--menu-bg:#fff;--menu-shadow:var(--chakra-shadows-sm);color:inherit;min-width:var(--chakra-sizes-3xs);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);z-index:var(--chakra-zIndices-dropdown);border-radius:var(--chakra-radii-md);border-width:1px;background:var(--menu-bg);box-shadow:var(--menu-shadow);}.chakra-ui-dark .css-s5t7bz:not([data-theme]),[data-theme=dark] .css-s5t7bz:not([data-theme]),.css-s5t7bz[data-theme=dark]{--menu-bg:var(--chakra-colors-gray-700);--menu-shadow:var(--chakra-shadows-dark-lg);}</style><div tabindex="-1" role="menu" id="menu-list-:Rcsbq6H1:" aria-orientation="vertical" class="chakra-menu__menu-list css-s5t7bz" style="transform-origin:var(--popper-transform-origin);opacity:0;visibility:hidden;transform:scale(0.8) translateZ(0)"><style data-emotion="css y7jzs3">.css-y7jzs3{-webkit-text-decoration:none;text-decoration:none;color:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;text-align:start;-webkit-flex:0 0 auto;-ms-flex:0 0 auto;flex:0 0 auto;outline:2px solid transparent;outline-offset:2px;padding-top:var(--chakra-space-1-5);padding-bottom:var(--chakra-space-1-5);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);transition-property:var(--chakra-transition-property-background);transition-duration:var(--chakra-transition-duration-ultra-fast);transition-timing-function:var(--chakra-transition-easing-ease-in);background:var(--menu-bg);}.css-y7jzs3:focus,.css-y7jzs3[data-focus]{--menu-bg:var(--chakra-colors-gray-100);}.chakra-ui-dark .css-y7jzs3:focus:not([data-theme]),.chakra-ui-dark .css-y7jzs3[data-focus]:not([data-theme]),[data-theme=dark] .css-y7jzs3:focus:not([data-theme]),[data-theme=dark] .css-y7jzs3[data-focus]:not([data-theme]),.css-y7jzs3:focus[data-theme=dark],.css-y7jzs3[data-focus][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-100);}.css-y7jzs3:active,.css-y7jzs3[data-active]{--menu-bg:var(--chakra-colors-gray-200);}.chakra-ui-dark .css-y7jzs3:active:not([data-theme]),.chakra-ui-dark .css-y7jzs3[data-active]:not([data-theme]),[data-theme=dark] .css-y7jzs3:active:not([data-theme]),[data-theme=dark] .css-y7jzs3[data-active]:not([data-theme]),.css-y7jzs3:active[data-theme=dark],.css-y7jzs3[data-active][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-200);}.css-y7jzs3[aria-expanded=true],.css-y7jzs3[data-expanded],.css-y7jzs3[data-state=expanded]{--menu-bg:var(--chakra-colors-gray-100);}.chakra-ui-dark .css-y7jzs3[aria-expanded=true]:not([data-theme]),.chakra-ui-dark .css-y7jzs3[data-expanded]:not([data-theme]),.chakra-ui-dark .css-y7jzs3[data-state=expanded]:not([data-theme]),[data-theme=dark] .css-y7jzs3[aria-expanded=true]:not([data-theme]),[data-theme=dark] .css-y7jzs3[data-expanded]:not([data-theme]),[data-theme=dark] .css-y7jzs3[data-state=expanded]:not([data-theme]),.css-y7jzs3[aria-expanded=true][data-theme=dark],.css-y7jzs3[data-expanded][data-theme=dark],.css-y7jzs3[data-state=expanded][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-100);}.css-y7jzs3:disabled,.css-y7jzs3[disabled],.css-y7jzs3[aria-disabled=true],.css-y7jzs3[data-disabled]{opacity:0.4;cursor:not-allowed;}</style><style data-emotion="css 1wdxdhd">.css-1wdxdhd{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:inherit;-webkit-text-decoration:none;text-decoration:none;color:inherit;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;text-align:start;-webkit-flex:0 0 auto;-ms-flex:0 0 auto;flex:0 0 auto;outline:2px solid transparent;outline-offset:2px;padding-top:var(--chakra-space-1-5);padding-bottom:var(--chakra-space-1-5);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);transition-property:var(--chakra-transition-property-background);transition-duration:var(--chakra-transition-duration-ultra-fast);transition-timing-function:var(--chakra-transition-easing-ease-in);background:var(--menu-bg);}.css-1wdxdhd:hover,.css-1wdxdhd[data-hover]{-webkit-text-decoration:underline;text-decoration:underline;}.css-1wdxdhd:focus-visible,.css-1wdxdhd[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-1wdxdhd:focus,.css-1wdxdhd[data-focus]{--menu-bg:var(--chakra-colors-gray-100);}.chakra-ui-dark .css-1wdxdhd:focus:not([data-theme]),.chakra-ui-dark .css-1wdxdhd[data-focus]:not([data-theme]),[data-theme=dark] .css-1wdxdhd:focus:not([data-theme]),[data-theme=dark] .css-1wdxdhd[data-focus]:not([data-theme]),.css-1wdxdhd:focus[data-theme=dark],.css-1wdxdhd[data-focus][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-100);}.css-1wdxdhd:active,.css-1wdxdhd[data-active]{--menu-bg:var(--chakra-colors-gray-200);}.chakra-ui-dark .css-1wdxdhd:active:not([data-theme]),.chakra-ui-dark .css-1wdxdhd[data-active]:not([data-theme]),[data-theme=dark] .css-1wdxdhd:active:not([data-theme]),[data-theme=dark] .css-1wdxdhd[data-active]:not([data-theme]),.css-1wdxdhd:active[data-theme=dark],.css-1wdxdhd[data-active][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-200);}.css-1wdxdhd[aria-expanded=true],.css-1wdxdhd[data-expanded],.css-1wdxdhd[data-state=expanded]{--menu-bg:var(--chakra-colors-gray-100);}.chakra-ui-dark .css-1wdxdhd[aria-expanded=true]:not([data-theme]),.chakra-ui-dark .css-1wdxdhd[data-expanded]:not([data-theme]),.chakra-ui-dark .css-1wdxdhd[data-state=expanded]:not([data-theme]),[data-theme=dark] .css-1wdxdhd[aria-expanded=true]:not([data-theme]),[data-theme=dark] .css-1wdxdhd[data-expanded]:not([data-theme]),[data-theme=dark] .css-1wdxdhd[data-state=expanded]:not([data-theme]),.css-1wdxdhd[aria-expanded=true][data-theme=dark],.css-1wdxdhd[data-expanded][data-theme=dark],.css-1wdxdhd[data-state=expanded][data-theme=dark]{--menu-bg:var(--chakra-colors-whiteAlpha-100);}.css-1wdxdhd:disabled,.css-1wdxdhd[disabled],.css-1wdxdhd[aria-disabled=true],.css-1wdxdhd[data-disabled]{opacity:0.4;cursor:not-allowed;}</style><a target="_blank" rel="noopener" class="chakra-link chakra-menu__menuitem css-1wdxdhd" type="button" href="/aitools" id="menu-list-:Rcsbq6H1:-menuitem-:Rqlcsbq6:" role="menuitem" tabindex="0">FunBlocks AI Tools</a><a target="_blank" rel="noopener" class="chakra-link chakra-menu__menuitem css-1wdxdhd" type="button" href="https://app.funblocks.net/#/aiplans" id="menu-list-:Rcsbq6H1:-menuitem-:R1alcsbq6:" role="menuitem" tabindex="0">定价</a><button type="button" id="menu-list-:Rcsbq6H1:-menuitem-:R1qlcsbq6:" role="menuitem" tabindex="0" class="chakra-menu__menuitem css-y7jzs3">登录</button></div></div><style data-emotion="css 2wijx4">.css-2wijx4{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-column-gap:var(--chakra-space-2);column-gap:var(--chakra-space-2);}</style><div style="font-size:18px;font-weight:bold" class="css-2wijx4"><style data-emotion="css spn4bz">.css-spn4bz{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:inherit;}.css-spn4bz:hover,.css-spn4bz[data-hover]{-webkit-text-decoration:underline;text-decoration:underline;}.css-spn4bz:focus-visible,.css-spn4bz[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}</style><a target="_blank" rel="noopener" class="chakra-link css-spn4bz" href="/aitools" style="color:dodgerblue">AI</a><span style="color:blueviolet">Graphics</span></div><style data-emotion="css 1unws2c">.css-1unws2c{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;z-index:1;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-column-gap:var(--chakra-space-2);column-gap:var(--chakra-space-2);}</style><div class="css-1unws2c"><style data-emotion="css 1xrhsyg">.css-1xrhsyg{height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:relative;width:auto;}</style><div class="chakra-select__wrapper css-1xrhsyg"><style data-emotion="css 804138">.css-804138{-webkit-padding-end:var(--chakra-space-8);padding-inline-end:var(--chakra-space-8);width:100%;height:var(--input-height);font-size:var(--input-font-size);-webkit-padding-start:var(--input-padding);padding-inline-start:var(--input-padding);min-width:0px;outline:2px solid transparent;outline-offset:2px;position:relative;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);padding-bottom:1px;line-height:var(--chakra-lineHeights-normal);background:var(--chakra-colors-gray-100);--select-bg:var(--chakra-colors-white);--input-font-size:var(--chakra-fontSizes-sm);--input-padding:var(--chakra-space-3);--input-border-radius:var(--chakra-radii-sm);--input-height:var(--chakra-sizes-8);border:2px solid;border-color:var(--chakra-colors-transparent);border-radius:var(--chakra-radii-full);}.css-804138:disabled,.css-804138[disabled],.css-804138[aria-disabled=true],.css-804138[data-disabled]{opacity:0.4;cursor:not-allowed;}.chakra-ui-dark .css-804138:not([data-theme]),[data-theme=dark] .css-804138:not([data-theme]),.css-804138[data-theme=dark]{--select-bg:var(--chakra-colors-gray-700);}.css-804138 >option,.css-804138 >optgroup{background:var(--select-bg);}.css-804138:hover,.css-804138[data-hover]{background:var(--chakra-colors-gray-200);}.css-804138[aria-readonly=true],.css-804138[readonly],.css-804138[data-readonly]{box-shadow:var(--chakra-shadows-none)!important;-webkit-user-select:all;-moz-user-select:all;-ms-user-select:all;user-select:all;}.css-804138[aria-invalid=true],.css-804138[data-invalid]{border-color:#E53E3E;}.css-804138:focus-visible,.css-804138[data-focus-visible]{background:var(--chakra-colors-transparent);border-color:#3182ce;}.css-804138:focus,.css-804138[data-focus]{z-index:unset;}</style><select class="chakra-select css-804138"><option value="en">English</option><option value="zh" selected="">中文</option></select><style data-emotion="css iohxn1">.css-iohxn1{width:var(--chakra-sizes-6);height:100%;right:var(--chakra-space-2);color:currentColor;font-size:var(--chakra-fontSizes-xl);position:absolute;display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;pointer-events:none;top:50%;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);}.css-iohxn1:disabled,.css-iohxn1[disabled],.css-iohxn1[aria-disabled=true],.css-iohxn1[data-disabled]{opacity:0.5;}</style><div class="chakra-select__icon-wrapper css-iohxn1"><svg viewBox="0 0 24 24" role="presentation" class="chakra-select__icon" focusable="false" aria-hidden="true" style="width:1em;height:1em;color:currentColor"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></div></div></div></div><style data-emotion="css 1g5naoy">.css-1g5naoy{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-10);width:100%;min-height:100vh;}</style><div class="chakra-stack css-1g5naoy"><div style="display:none"><canvas style="height:100px;width:100px" height="100" width="100" role="img" id="qrcode-canvas"></canvas></div><style data-emotion="css wo1513">.css-wo1513{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}</style><div class="css-wo1513"><style data-emotion="css dwxnbz">.css-dwxnbz{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:var(--chakra-space-2);padding-top:var(--chakra-space-6);padding-bottom:var(--chakra-space-6);}</style><div class="css-dwxnbz"><style data-emotion="css 2evtte">.css-2evtte{font-family:var(--chakra-fonts-heading);line-height:1.33;padding-bottom:0px;padding-left:0px;padding-right:0px;background-image:linear-gradient(to right, dodgerblue, fuchsia);color:transparent;-webkit-background-clip:text;background-clip:text;font-size:var(--chakra-fontSizes-xl);font-weight:var(--chakra-fontWeights-extrabold);}@media screen and (min-width: 48em){.css-2evtte{line-height:1.2;}}</style><h2 class="chakra-heading css-2evtte">智能视觉，轻松创造</h2><style data-emotion="css 1stajkv">.css-1stajkv{font-size:unset;padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-4);}</style><p class="chakra-text css-1stajkv">AI将文本和数据转换为引人注目的信息图，根据主题创作聪明的知识卡，无需设计技能。</p><style data-emotion="css dyeykw">.css-dyeykw{max-width:900px;width:100%;padding-top:var(--chakra-space-2);}</style><div spacing="3" align="center" class="css-dyeykw"><style data-emotion="css hn4ywi">.css-hn4ywi{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-3);background-color:#f8f8f8;padding:var(--chakra-space-3);border-radius:8px;padding-bottom:var(--chakra-space-3);}</style><div class="chakra-stack css-hn4ywi"><style data-emotion="css 6bc98m">.css-6bc98m{position:relative;display:block;width:100%;}</style><div class="chakra-tabs css-6bc98m"><style data-emotion="css myj99w">.css-myj99w{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;width:100%;overflow-x:auto;overflow-y:hidden;scrollbar-width:none;-ms-overflow-style:none;white-space:nowrap;}.css-myj99w::-webkit-scrollbar{display:none;}</style><div role="tablist" aria-orientation="horizontal" class="chakra-tabs__tablist css-myj99w"><style data-emotion="css y13pj5">.css-y13pj5{outline:2px solid transparent;outline-offset:2px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);font-size:var(--chakra-fontSizes-md);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);border-radius:var(--chakra-radii-full);font-weight:var(--chakra-fontWeights-semibold);color:var(--chakra-colors-gray-600);min-width:120px;-webkit-flex:none;-ms-flex:none;flex:none;}.css-y13pj5:focus-visible,.css-y13pj5[data-focus-visible]{z-index:1;box-shadow:var(--chakra-shadows-outline);}.css-y13pj5:disabled,.css-y13pj5[disabled],.css-y13pj5[aria-disabled=true],.css-y13pj5[data-disabled]{cursor:not-allowed;opacity:0.4;}.css-y13pj5[aria-selected=true],.css-y13pj5[data-selected]{color:#2c5282;background:#bee3f8;}</style><button type="button" aria-disabled="false" id="tabs-:R36sd5cbq6:--tab--1" role="tab" tabindex="0" aria-selected="true" aria-controls="tabs-:R36sd5cbq6:--tabpanel--1" class="chakra-tabs__tab css-y13pj5">制作图表</button><button type="button" aria-disabled="false" id="tabs-:R36sd5cbq6:--tab--1" role="tab" tabindex="0" aria-selected="true" aria-controls="tabs-:R36sd5cbq6:--tabpanel--1" class="chakra-tabs__tab css-y13pj5">洞见卡片</button><button type="button" aria-disabled="false" id="tabs-:R36sd5cbq6:--tab--1" role="tab" tabindex="0" aria-selected="true" aria-controls="tabs-:R36sd5cbq6:--tabpanel--1" class="chakra-tabs__tab css-y13pj5">图表对话</button></div></div><style data-emotion="css r54r42">.css-r54r42{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-2);width:100%;}</style><div class="chakra-stack css-r54r42"><style data-emotion="css 1kxonj9">.css-1kxonj9{width:100%;position:relative;}</style><div role="group" class="chakra-form-control css-1kxonj9"><style data-emotion="css 79elbk">.css-79elbk{position:relative;}</style><div class="css-79elbk"><style data-emotion="css 92kh2h">.css-92kh2h{position:absolute;top:0px;left:0px;right:0px;bottom:0px;pointer-events:none;}</style><div class="gradient-border-textarea-bg css-92kh2h"></div><style data-emotion="css 1ceazil">.css-1ceazil{width:100%;font-size:var(--input-font-size);-webkit-padding-start:var(--input-padding);padding-inline-start:var(--input-padding);-webkit-padding-end:var(--input-padding);padding-inline-end:var(--input-padding);border-radius:var(--input-border-radius);min-width:0px;outline:2px solid transparent;outline-offset:2px;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);line-height:var(--chakra-lineHeights-short);vertical-align:top;--input-font-size:var(--chakra-fontSizes-md);--input-padding:var(--chakra-space-4);--input-border-radius:var(--chakra-radii-md);--input-height:var(--chakra-sizes-10);border-color:inherit;background:inherit;border:var(--chakra-borders-none);z-index:1;position:relative;}.css-1ceazil:disabled,.css-1ceazil[disabled],.css-1ceazil[aria-disabled=true],.css-1ceazil[data-disabled]{opacity:0.4;cursor:not-allowed;}.css-1ceazil:hover,.css-1ceazil[data-hover]{border-color:var(--chakra-colors-gray-300);}.css-1ceazil[aria-readonly=true],.css-1ceazil[readonly],.css-1ceazil[data-readonly]{box-shadow:var(--chakra-shadows-none)!important;-webkit-user-select:all;-moz-user-select:all;-ms-user-select:all;user-select:all;}.css-1ceazil[aria-invalid=true],.css-1ceazil[data-invalid]{border-color:#E53E3E;box-shadow:0 0 0 1px #E53E3E;}.css-1ceazil:focus-visible,.css-1ceazil[data-focus-visible]{z-index:1;border-color:#3182ce;box-shadow:0 0 0 1px #3182ce;}.css-1ceazil:focus,.css-1ceazil[data-focus]{box-shadow:var(--chakra-shadows-none);}</style><textarea rows="3" placeholder="请输入你的主题..." maxLength="Infinity" id="field-:R1l6sd5cbq6:" class="chakra-textarea css-1ceazil"></textarea></div></div></div><div style="width:100%;display:flex;flex-direction:column;justify-content:space-between;align-items:center;gap:12px"><style data-emotion="css n3uhkm">.css-n3uhkm{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.5rem;width:100%;}</style><div class="chakra-stack css-n3uhkm"><style data-emotion="css x3jchx">.css-x3jchx{display:block;text-align:start;font-size:var(--chakra-fontSizes-md);-webkit-margin-end:var(--chakra-space-3);margin-inline-end:var(--chakra-space-3);margin-bottom:var(--chakra-space-2);font-weight:var(--chakra-fontWeights-medium);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);opacity:1;white-space:nowrap;margin:0px;margin-right:0px;}.css-x3jchx:disabled,.css-x3jchx[disabled],.css-x3jchx[aria-disabled=true],.css-x3jchx[data-disabled]{opacity:0.4;}</style><label class="chakra-form__label css-x3jchx">输出语言</label><style data-emotion="css 42b2qy">.css-42b2qy{width:100%;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;position:relative;}</style><div class="chakra-select__wrapper css-42b2qy"><style data-emotion="css 3h9tbc">.css-3h9tbc{-webkit-padding-end:var(--chakra-space-8);padding-inline-end:var(--chakra-space-8);width:100%;height:var(--input-height);font-size:var(--input-font-size);-webkit-padding-start:var(--input-padding);padding-inline-start:var(--input-padding);border-radius:var(--input-border-radius);min-width:0px;outline:2px solid transparent;outline-offset:2px;position:relative;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);padding-bottom:1px;line-height:var(--chakra-lineHeights-normal);--select-bg:var(--chakra-colors-white);--input-font-size:var(--chakra-fontSizes-md);--input-padding:var(--chakra-space-4);--input-border-radius:var(--chakra-radii-md);--input-height:var(--chakra-sizes-10);background:var(--chakra-colors-white);}.css-3h9tbc:disabled,.css-3h9tbc[disabled],.css-3h9tbc[aria-disabled=true],.css-3h9tbc[data-disabled]{opacity:0.4;cursor:not-allowed;}.chakra-ui-dark .css-3h9tbc:not([data-theme]),[data-theme=dark] .css-3h9tbc:not([data-theme]),.css-3h9tbc[data-theme=dark]{--select-bg:var(--chakra-colors-gray-700);}.css-3h9tbc >option,.css-3h9tbc >optgroup{background:var(--select-bg);}.css-3h9tbc:focus,.css-3h9tbc[data-focus]{z-index:unset;}</style><select class="chakra-select css-3h9tbc"></select><style data-emotion="css iohxn1">.css-iohxn1{width:var(--chakra-sizes-6);height:100%;right:var(--chakra-space-2);color:currentColor;font-size:var(--chakra-fontSizes-xl);position:absolute;display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;pointer-events:none;top:50%;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);}.css-iohxn1:disabled,.css-iohxn1[disabled],.css-iohxn1[aria-disabled=true],.css-iohxn1[data-disabled]{opacity:0.5;}</style><div class="chakra-select__icon-wrapper css-iohxn1"><svg viewBox="0 0 24 24" role="presentation" class="chakra-select__icon" focusable="false" aria-hidden="true" style="width:1em;height:1em;color:currentColor"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></div></div></div><style data-emotion="css 33gw3x">.css-33gw3x{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);height:var(--chakra-sizes-10);min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-md);-webkit-padding-start:var(--chakra-space-8);padding-inline-start:var(--chakra-space-8);-webkit-padding-end:var(--chakra-space-8);padding-inline-end:var(--chakra-space-8);background:var(--chakra-colors-purple-500);color:var(--chakra-colors-white);}.css-33gw3x:focus-visible,.css-33gw3x[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-33gw3x:disabled,.css-33gw3x[disabled],.css-33gw3x[aria-disabled=true],.css-33gw3x[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-33gw3x:hover,.css-33gw3x[data-hover]{background:var(--chakra-colors-purple-600);}.css-33gw3x:hover:disabled,.css-33gw3x[data-hover]:disabled,.css-33gw3x:hover[disabled],.css-33gw3x[data-hover][disabled],.css-33gw3x:hover[aria-disabled=true],.css-33gw3x[data-hover][aria-disabled=true],.css-33gw3x:hover[data-disabled],.css-33gw3x[data-hover][data-disabled]{background:var(--chakra-colors-purple-500);}.css-33gw3x:active,.css-33gw3x[data-active]{background:var(--chakra-colors-purple-700);}</style><button type="button" class="chakra-button css-33gw3x">生成</button></div></div></div></div><style data-emotion="css 1ls92nf">.css-1ls92nf{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:0px;width:100%;margin-top:var(--chakra-space-6);}</style><div class="chakra-stack css-1ls92nf"><style data-emotion="css 1mcv9q8">.css-1mcv9q8{width:100%;background-image:linear-gradient(to bottom right, var(--chakra-colors-blue-50), var(--chakra-colors-purple-50));padding-top:var(--chakra-space-12);padding-bottom:var(--chakra-space-12);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);border-bottom-width:1px;border-bottom-color:var(--chakra-colors-gray-200);}</style><div class="css-1mcv9q8"><style data-emotion="css 11eestw">.css-11eestw{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-8);max-width:var(--chakra-sizes-4xl);-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;text-align:center;}</style><div class="chakra-stack css-11eestw"><style data-emotion="css 1h7h85m">.css-1h7h85m{display:inline-block;white-space:nowrap;vertical-align:middle;text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-blue-100);--badge-color:var(--chakra-colors-blue-800);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);border-radius:var(--chakra-radii-full);}.chakra-ui-dark .css-1h7h85m:not([data-theme]),[data-theme=dark] .css-1h7h85m:not([data-theme]),.css-1h7h85m[data-theme=dark]{--badge-bg:rgba(144, 205, 244, 0.16);--badge-color:var(--chakra-colors-blue-200);}</style><span class="chakra-badge css-1h7h85m">AI 图形生成器</span><style data-emotion="css 11j9a8f">.css-11j9a8f{font-family:var(--chakra-fonts-heading);font-size:var(--chakra-fontSizes-3xl);font-weight:var(--chakra-fontWeights-extrabold);background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-purple-500));color:transparent;-webkit-background-clip:text;background-clip:text;line-height:1.2;}</style><h2 class="chakra-heading css-11j9a8f">用人工智能将您的想法转化为精美图形</h2><style data-emotion="css unfk8h">.css-unfk8h{font-size:var(--chakra-fontSizes-lg);color:var(--chakra-colors-gray-600);max-width:var(--chakra-sizes-2xl);}</style><p class="chakra-text css-unfk8h">在几秒钟内创建令人惊叹的信息图表、流程图、数据图表和视觉内容 - 无需设计技能。</p><style data-emotion="css 1rafi8n">.css-1rafi8n{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:var(--chakra-space-4);}</style><div class="chakra-stack css-1rafi8n"><style data-emotion="css 1xtfdg5">.css-1xtfdg5{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);height:var(--chakra-sizes-12);min-width:var(--chakra-sizes-12);font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:var(--chakra-space-6);padding-inline-start:var(--chakra-space-6);-webkit-padding-end:var(--chakra-space-6);padding-inline-end:var(--chakra-space-6);background:var(--chakra-colors-blue-500);color:var(--chakra-colors-white);}.css-1xtfdg5:focus-visible,.css-1xtfdg5[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-1xtfdg5:disabled,.css-1xtfdg5[disabled],.css-1xtfdg5[aria-disabled=true],.css-1xtfdg5[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-1xtfdg5:hover,.css-1xtfdg5[data-hover]{background:var(--chakra-colors-blue-600);}.css-1xtfdg5:hover:disabled,.css-1xtfdg5[data-hover]:disabled,.css-1xtfdg5:hover[disabled],.css-1xtfdg5[data-hover][disabled],.css-1xtfdg5:hover[aria-disabled=true],.css-1xtfdg5[data-hover][aria-disabled=true],.css-1xtfdg5:hover[data-disabled],.css-1xtfdg5[data-hover][data-disabled]{background:var(--chakra-colors-blue-500);}.css-1xtfdg5:active,.css-1xtfdg5[data-active]{background:var(--chakra-colors-blue-700);}</style><button type="button" class="chakra-button css-1xtfdg5">立即开始<style data-emotion="css 1hzyiq5">.css-1hzyiq5{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-margin-start:0.5rem;margin-inline-start:0.5rem;}</style><span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button><style data-emotion="css 1qtxob7">.css-1qtxob7{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);height:var(--chakra-sizes-12);min-width:var(--chakra-sizes-12);font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:var(--chakra-space-6);padding-inline-start:var(--chakra-space-6);-webkit-padding-end:var(--chakra-space-6);padding-inline-end:var(--chakra-space-6);border:1px solid;border-color:currentColor;color:var(--chakra-colors-purple-600);background:var(--chakra-colors-transparent);}.css-1qtxob7:focus-visible,.css-1qtxob7[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-1qtxob7:disabled,.css-1qtxob7[disabled],.css-1qtxob7[aria-disabled=true],.css-1qtxob7[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-1qtxob7:hover,.css-1qtxob7[data-hover]{background:var(--chakra-colors-purple-50);}.css-1qtxob7:hover:disabled,.css-1qtxob7[data-hover]:disabled,.css-1qtxob7:hover[disabled],.css-1qtxob7[data-hover][disabled],.css-1qtxob7:hover[aria-disabled=true],.css-1qtxob7[data-hover][aria-disabled=true],.css-1qtxob7:hover[data-disabled],.css-1qtxob7[data-hover][data-disabled]{background:initial;}.chakra-button__group[data-attached][data-orientation=horizontal]>.css-1qtxob7:not(:last-of-type){-webkit-margin-end:-1px;margin-inline-end:-1px;}.chakra-button__group[data-attached][data-orientation=vertical]>.css-1qtxob7:not(:last-of-type){margin-bottom:-1px;}.css-1qtxob7:active,.css-1qtxob7[data-active]{background:var(--chakra-colors-purple-100);}</style><button type="button" class="chakra-button css-1qtxob7">查看示例</button></div><style data-emotion="css 1rj4op4">.css-1rj4op4{display:grid;grid-gap:var(--chakra-space-4);grid-template-columns:repeat(1, minmax(0, 1fr));width:100%;padding-top:var(--chakra-space-8);}@media screen and (min-width: 48em){.css-1rj4op4{grid-template-columns:repeat(3, minmax(0, 1fr));}}</style><div class="css-1rj4op4"><style data-emotion="css tl3ftk">.css-tl3ftk{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:0.5rem;}</style><div class="chakra-stack css-tl3ftk"><style data-emotion="css 1i5j3tx">.css-1i5j3tx{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-3xl);line-height:1.33;color:var(--chakra-colors-blue-500);}@media screen and (min-width: 48em){.css-1i5j3tx{font-size:var(--chakra-fontSizes-4xl);line-height:1.2;}}</style><h2 class="chakra-heading css-1i5j3tx">10倍</h2><style data-emotion="css jneyc">.css-jneyc{color:var(--chakra-colors-gray-600);}</style><p class="chakra-text css-jneyc">比传统设计更快</p></div><div class="chakra-stack css-tl3ftk"><style data-emotion="css 15wjfkr">.css-15wjfkr{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-3xl);line-height:1.33;color:var(--chakra-colors-purple-500);}@media screen and (min-width: 48em){.css-15wjfkr{font-size:var(--chakra-fontSizes-4xl);line-height:1.2;}}</style><h2 class="chakra-heading css-15wjfkr">50+</h2><p class="chakra-text css-jneyc">视觉风格和格式</p></div><div class="chakra-stack css-tl3ftk"><style data-emotion="css ssjs6n">.css-ssjs6n{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-3xl);line-height:1.33;color:var(--chakra-colors-green-500);}@media screen and (min-width: 48em){.css-ssjs6n{font-size:var(--chakra-fontSizes-4xl);line-height:1.2;}}</style><h2 class="chakra-heading css-ssjs6n">75%</h2><p class="chakra-text css-jneyc">信息记忆能力提高</p></div></div></div></div><style data-emotion="css 1udq1c6">.css-1udq1c6{background:var(--chakra-colors-gray-50);padding-top:4rem;padding-bottom:4rem;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}</style><div class="css-1udq1c6"><style data-emotion="css 1vr2anf">.css-1vr2anf{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}</style><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 3bip5u">.css-3bip5u{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);line-height:1.33;font-size:var(--chakra-fontSizes-2xl);margin-bottom:var(--chakra-space-4);text-align:center;background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-purple-400));color:transparent;-webkit-background-clip:text;background-clip:text;}@media screen and (min-width: 48em){.css-3bip5u{line-height:1.2;}}</style><h2 class="chakra-heading css-3bip5u">有洞见的信息图</h2><style data-emotion="css 1b36cmq">.css-1b36cmq{display:grid;grid-gap:var(--chakra-space-8);grid-template-columns:repeat(1, minmax(0, 1fr));}@media screen and (min-width: 48em){.css-1b36cmq{grid-template-columns:repeat(3, minmax(0, 1fr));}}</style><div class="css-1b36cmq"><style data-emotion="css 1afqsa">.css-1afqsa{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-3);padding:var(--chakra-space-5);background:var(--chakra-colors-white);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;}</style><div class="chakra-stack css-1afqsa"><style data-emotion="css o7r789">.css-o7r789{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-blue-500);width:var(--chakra-sizes-6);height:var(--chakra-sizes-6);}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M128 352H32c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h96c17.67 0 32-14.33 32-32v-96c0-17.67-14.33-32-32-32zm-24-80h192v48h48v-48h192v48h48v-57.59c0-21.17-17.23-38.41-38.41-38.41H344v-64h40c17.67 0 32-14.33 32-32V32c0-17.67-14.33-32-32-32H256c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h40v64H94.41C73.23 224 56 241.23 56 262.41V320h48v-48zm264 80h-96c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h96c17.67 0 32-14.33 32-32v-96c0-17.67-14.33-32-32-32zm240 0h-96c-17.67 0-32 14.33-32 32v96c0 17.67 14.33 32 32 32h96c17.67 0 32-14.33 32-32v-96c0-17.67-14.33-32-32-32z"></path></svg><style data-emotion="css wjomy9">.css-wjomy9{font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-lg);}</style><p class="chakra-text css-wjomy9">聊天图卡</p><p class="chakra-text css-jneyc">体验前所未有的信息获取效率，比传统的文字对话更清晰简洁。</p></div><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M96.06 454.35c.01 6.29 1.87 12.45 5.36 17.69l17.09 25.69a31.99 31.99 0 0 0 26.64 14.28h61.71a31.99 31.99 0 0 0 26.64-14.28l17.09-25.69a31.989 31.989 0 0 0 5.36-17.69l.04-38.35H96.01l.05 38.35zM0 176c0 44.37 16.45 84.85 43.56 115.78 16.52 18.85 42.36 58.23 52.21 91.45.04.26.07.52.11.78h160.24c.04-.26.07-.51.11-.78 9.85-33.22 35.69-72.6 52.21-91.45C335.55 260.85 352 220.37 352 176 352 78.61 272.91-.3 175.45 0 73.44.31 0 82.97 0 176zm176-80c-44.11 0-80 35.89-80 80 0 8.84-7.16 16-16 16s-16-7.16-16-16c0-61.76 50.24-112 112-112 8.84 0 16 7.16 16 16s-7.16 16-16 16z"></path></svg><p class="chakra-text css-wjomy9">信息图</p><p class="chakra-text css-jneyc">通过精心设计的提示词引导大模型，获得更深入的理解与乐趣。</p></div><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M352 320c-22.608 0-43.387 7.819-59.79 20.895l-102.486-64.054a96.551 96.551 0 0 0 0-41.683l102.486-64.054C308.613 184.181 329.392 192 352 192c53.019 0 96-42.981 96-96S405.019 0 352 0s-96 42.981-96 96c0 7.158.79 14.13 2.276 20.841L155.79 180.895C139.387 167.819 118.608 160 96 160c-53.019 0-96 42.981-96 96s42.981 96 96 96c22.608 0 43.387-7.819 59.79-20.895l102.486 64.054A96.301 96.301 0 0 0 256 416c0 53.019 42.981 96 96 96s96-42.981 96-96-42.981-96-96-96z"></path></svg><p class="chakra-text css-wjomy9">智慧图卡</p><p class="chakra-text css-jneyc">深入分析给定主题，发现新的见解，并制作易于分享的有吸引力社交媒体卡片。</p></div></div><style data-emotion="css oe1143">.css-oe1143{margin-top:var(--chakra-space-12);padding:var(--chakra-space-6);background:var(--chakra-colors-blue-50);border-radius:var(--chakra-radii-xl);width:100%;max-width:var(--chakra-sizes-3xl);border-width:1px;border-color:var(--chakra-colors-blue-200);}</style><div class="css-oe1143"><style data-emotion="css 1cggwyz">.css-1cggwyz{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-4);}</style><div class="chakra-stack css-1cggwyz"><style data-emotion="css 1igwmid">.css-1igwmid{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.5rem;}</style><div class="chakra-stack css-1igwmid"><style data-emotion="css ntclmb">.css-ntclmb{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-blue-500);width:var(--chakra-sizes-5);height:var(--chakra-sizes-5);}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-ntclmb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M176 80c-52.94 0-96 43.06-96 96 0 8.84 7.16 16 16 16s16-7.16 16-16c0-35.3 28.72-64 64-64 8.84 0 16-7.16 16-16s-7.16-16-16-16zM96.06 459.17c0 3.15.93 6.22 2.68 8.84l24.51 36.84c2.97 4.46 7.97 7.14 13.32 7.14h78.85c5.36 0 10.36-2.68 13.32-7.14l24.51-36.84c1.74-2.62 2.67-5.7 2.68-8.84l.05-43.18H96.02l.04 43.18zM176 0C73.72 0 0 82.97 0 176c0 44.37 16.45 84.85 43.56 115.78 16.64 18.99 42.74 58.8 52.42 92.16v.06h48v-.12c-.01-4.77-.72-9.51-2.15-14.07-5.59-17.81-22.82-64.77-62.17-109.67-20.54-23.43-31.52-53.15-31.61-84.14-.2-73.64 59.67-128 127.95-128 70.58 0 128 57.42 128 128 0 30.97-11.24 60.85-31.65 84.14-39.11 44.61-56.42 91.47-62.1 109.46a47.507 47.507 0 0 0-2.22 14.3v.1h48v-.05c9.68-33.37 35.78-73.18 52.42-92.16C335.55 260.85 352 220.37 352 176 352 78.8 273.2 0 176 0z"></path></svg><style data-emotion="css dt342z">.css-dt342z{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-md);line-height:1.2;color:var(--chakra-colors-blue-700);}</style><h2 class="chakra-heading css-dt342z">FunBlocks AI Graphics不仅仅是普通的内容生成工具，它可以生成真正深刻、有价值、独特的内容：</h2></div><style data-emotion="css 1xa84ef">.css-1xa84ef{list-style-type:none;}.css-1xa84ef>*:not(style)~*:not(style){margin-top:var(--chakra-space-3);}</style><ul role="list" class="css-1xa84ef"><style data-emotion="css 70qvj9">.css-70qvj9{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}</style><li class="css-70qvj9"><style data-emotion="css 169zzeb">.css-169zzeb{width:1em;height:1em;display:inline;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-green-500);-webkit-margin-end:var(--chakra-space-2);margin-inline-end:var(--chakra-space-2);vertical-align:text-bottom;}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg><style data-emotion="css qqfgvy">.css-qqfgvy{font-size:var(--chakra-fontSizes-md);}</style><p class="chakra-text css-qqfgvy">深刻洞见：AI 深度解析主题，带来独特观点</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg><p class="chakra-text css-qqfgvy">高质量内容：融合海量知识，输出优质内容</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg><p class="chakra-text css-qqfgvy">创新思维：打破常规，激发新颖观点</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg><p class="chakra-text css-qqfgvy">智慧幽默融合：巧妙结合幽默与智慧，让内容更有趣</p></li></ul></div></div></div></div><style data-emotion="css v3gv3t">.css-v3gv3t{background:var(--chakra-colors-white);padding-top:4rem;padding-bottom:4rem;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}</style><div id="examples" class="css-v3gv3t"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 1gxxl08">.css-1gxxl08{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-2xl);line-height:1.33;margin-bottom:var(--chakra-space-8);text-align:center;background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-purple-400));color:transparent;-webkit-background-clip:text;background-clip:text;}@media screen and (min-width: 48em){.css-1gxxl08{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;}}</style><h2 class="chakra-heading css-1gxxl08">您可以创建什么</h2><style data-emotion="css 1whttwt">.css-1whttwt{display:grid;grid-gap:var(--chakra-space-8);grid-template-columns:repeat(1, minmax(0, 1fr));width:100%;}@media screen and (min-width: 48em){.css-1whttwt{grid-template-columns:repeat(2, minmax(0, 1fr));}}</style><div class="css-1whttwt"><style data-emotion="css 1e905xt">.css-1e905xt{background:var(--chakra-colors-white);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;overflow:hidden;height:100%;-webkit-transition:-webkit-transform 0.3s,box-shadow 0.3s;transition:transform 0.3s,box-shadow 0.3s;}.css-1e905xt:hover,.css-1e905xt[data-hover]{-webkit-transform:translateY(-5px);-moz-transform:translateY(-5px);-ms-transform:translateY(-5px);transform:translateY(-5px);box-shadow:var(--chakra-shadows-lg);}</style><div class="css-1e905xt"><style data-emotion="css dbu5v5">.css-dbu5v5{object-fit:contain;height:240px;width:100%;}</style><img alt="数据可视化" src="https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_piechart.png" class="chakra-image css-dbu5v5"/><style data-emotion="css r4358i">.css-r4358i{padding:var(--chakra-space-5);}</style><div class="css-r4358i"><style data-emotion="css 131zawk">.css-131zawk{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-xl);line-height:1.2;margin-bottom:var(--chakra-space-2);}</style><h2 class="chakra-heading css-131zawk">数据可视化</h2><p class="chakra-text css-jneyc">将复杂数据转化为直观、美观的图表，一目了然地讲述故事。</p></div></div><div class="css-1e905xt"><img alt="流程示意图" src="https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png" class="chakra-image css-dbu5v5"/><div class="css-r4358i"><h2 class="chakra-heading css-131zawk">流程示意图</h2><p class="chakra-text css-jneyc">用清晰、引人入胜的视觉效果说明多步骤流程，提高理解度。</p></div></div><div class="css-1e905xt"><img alt="概念流程图" src="https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_conceptchart.png" class="chakra-image css-dbu5v5"/><div class="css-r4358i"><h2 class="chakra-heading css-131zawk">概念流程图</h2><p class="chakra-text css-jneyc">用专业外观的图表绘制关系、层次结构和决策流程。</p></div></div><div class="css-1e905xt"><img alt="见解卡片" src="https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_insights.png" class="chakra-image css-dbu5v5"/><div class="css-r4358i"><h2 class="chakra-heading css-131zawk">见解卡片</h2><p class="chakra-text css-jneyc">用优雅的视觉效果呈现主题，突出核心见解。</p></div></div></div><style data-emotion="css zp010v">.css-zp010v{text-align:center;margin-top:var(--chakra-space-10);}</style><div class="css-zp010v"><style data-emotion="css ywuwxm">.css-ywuwxm{font-size:var(--chakra-fontSizes-lg);font-weight:var(--chakra-fontWeights-medium);margin-bottom:var(--chakra-space-4);}</style><p class="chakra-text css-ywuwxm">准备好创建您自己的精美图形了吗？</p><button type="button" class="chakra-button css-1xtfdg5">立即开始创建</button></div></div></div><div class="css-1udq1c6"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css h6lx5">.css-h6lx5{font-size:var(--chakra-fontSizes-md);color:var(--chakra-colors-gray-600);text-align:center;max-width:var(--chakra-sizes-2xl);margin-bottom:var(--chakra-space-8);}</style><p class="chakra-text css-h6lx5">FunBlocks AI图形将人工智能与设计原则相结合，帮助您在几分钟内（而不是几小时）创建专业质量的视觉内容。</p><style data-emotion="css 166b8wq">.css-166b8wq{display:grid;grid-gap:var(--chakra-space-6);grid-template-columns:repeat(1, minmax(0, 1fr));}@media screen and (min-width: 48em){.css-166b8wq{grid-template-columns:repeat(4, minmax(0, 1fr));}}</style><div class="css-166b8wq"><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M224 96l16-32 32-16-32-16-16-32-16 32-32 16 32 16 16 32zM80 160l26.66-53.33L160 80l-53.34-26.67L80 0 53.34 53.33 0 80l53.34 26.67L80 160zm352 128l-26.66 53.33L352 368l53.34 26.67L432 448l26.66-53.33L512 368l-53.34-26.67L432 288zm70.62-193.77L417.77 9.38C411.53 3.12 403.34 0 395.15 0c-8.19 0-16.38 3.12-22.63 9.38L9.38 372.52c-12.5 12.5-12.5 32.76 0 45.25l84.85 84.85c6.25 6.25 14.44 9.37 22.62 9.37 8.19 0 16.38-3.12 22.63-9.37l363.14-363.15c12.5-12.48 12.5-32.75 0-45.24zM359.45 203.46l-50.91-50.91 86.6-86.6 50.91 50.91-86.6 86.6z"></path></svg><p class="chakra-text css-wjomy9">AI 洞见驱动</p><p class="chakra-text css-jneyc">运用先进的大语言模型技术，深入分析主题，提供独特见解。</p></div><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="m19 9 1.25-2.75L23 5l-2.75-1.25L19 1l-1.25 2.75L15 5l2.75 1.25L19 9zm-7.5.5L9 4 6.5 9.5 1 12l5.5 2.5L9 20l2.5-5.5L17 12l-5.5-2.5zM19 15l-1.25 2.75L15 19l2.75 1.25L19 23l1.25-2.75L23 19l-2.75-1.25L19 15z"></path></svg><p class="chakra-text css-wjomy9">创意灵感加速器</p><p class="chakra-text css-jneyc">突破思维局限，获得新颖视角，激发无限创意灵感。</p></div><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M96.06 454.35c.01 6.29 1.87 12.45 5.36 17.69l17.09 25.69a31.99 31.99 0 0 0 26.64 14.28h61.71a31.99 31.99 0 0 0 26.64-14.28l17.09-25.69a31.989 31.989 0 0 0 5.36-17.69l.04-38.35H96.01l.05 38.35zM0 176c0 44.37 16.45 84.85 43.56 115.78 16.52 18.85 42.36 58.23 52.21 91.45.04.26.07.52.11.78h160.24c.04-.26.07-.51.11-.78 9.85-33.22 35.69-72.6 52.21-91.45C335.55 260.85 352 220.37 352 176 352 78.61 272.91-.3 175.45 0 73.44.31 0 82.97 0 176zm176-80c-44.11 0-80 35.89-80 80 0 8.84-7.16 16-16 16s-16-7.16-16-16c0-61.76 50.24-112 112-112 8.84 0 16 7.16 16 16s-7.16 16-16 16z"></path></svg><p class="chakra-text css-wjomy9">幽默与智慧并存</p><p class="chakra-text css-jneyc">深刻见解与幽默表达的完美结合，让思考充满乐趣。</p></div><div class="chakra-stack css-1afqsa"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" focusable="false" class="chakra-icon css-o7r789" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M352 320c-22.608 0-43.387 7.819-59.79 20.895l-102.486-64.054a96.551 96.551 0 0 0 0-41.683l102.486-64.054C308.613 184.181 329.392 192 352 192c53.019 0 96-42.981 96-96S405.019 0 352 0s-96 42.981-96 96c0 7.158.79 14.13 2.276 20.841L155.79 180.895C139.387 167.819 118.608 160 96 160c-53.019 0-96 42.981-96 96s42.981 96 96 96c22.608 0 43.387-7.819 59.79-20.895l102.486 64.054A96.301 96.301 0 0 0 256 416c0 53.019 42.981 96 96 96s96-42.981 96-96-42.981-96-96-96z"></path></svg><p class="chakra-text css-wjomy9">趣味图卡，轻松分享</p><p class="chakra-text css-jneyc">生成吸引眼球的内容图卡，在社交平台上轻松脱颖而出。</p></div></div></div></div><div class="css-v3gv3t"><div class="chakra-stack container css-1vr2anf"><h2 class="chakra-heading css-3bip5u">AI图形与传统设计比较</h2><style data-emotion="css 170k2wx">.css-170k2wx{max-width:var(--chakra-sizes-4xl);width:100%;border-width:1px;border-radius:var(--chakra-radii-lg);overflow:hidden;background:var(--chakra-colors-white);}</style><div class="css-170k2wx"><style data-emotion="css w6hrdg">.css-w6hrdg{display:grid;grid-template-columns:repeat(3, 1fr);background:var(--chakra-colors-gray-50);padding:var(--chakra-space-4);}</style><div class="css-w6hrdg"><div class="css-0"><style data-emotion="css 722v25">.css-722v25{font-weight:var(--chakra-fontWeights-bold);}</style><p class="chakra-text css-722v25">功能</p></div><div class="css-0"><p class="chakra-text css-722v25">传统设计</p></div><div class="css-0"><style data-emotion="css vhmouc">.css-vhmouc{font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-blue-500);}</style><p class="chakra-text css-vhmouc">AI图形</p></div></div><style data-emotion="css h94677">.css-h94677{padding:var(--chakra-space-4);}</style><div class="css-h94677"><style data-emotion="css 13cg038">.css-13cg038{border-bottom-width:1px;padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);}</style><div class="css-13cg038"><style data-emotion="css 1j9zy8k">.css-1j9zy8k{display:grid;grid-template-columns:repeat(3, 1fr);}</style><div class="css-1j9zy8k"><div class="css-0"><style data-emotion="css 1bsgmhw">.css-1bsgmhw{font-weight:var(--chakra-fontWeights-medium);}</style><p class="chakra-text css-1bsgmhw">创建时间</p></div><div class="css-0"><div class="css-70qvj9"><style data-emotion="css q9k0mw">.css-q9k0mw{color:var(--chakra-colors-gray-500);}</style><p class="chakra-text css-q9k0mw">数小时到数天</p></div></div><div class="css-0"><div class="css-70qvj9"><style data-emotion="css 1hlyvp">.css-1hlyvp{color:var(--chakra-colors-blue-500);font-weight:var(--chakra-fontWeights-bold);}</style><p class="chakra-text css-1hlyvp">几分钟</p></div></div></div></div><div class="css-13cg038"><div class="css-1j9zy8k"><div class="css-0"><p class="chakra-text css-1bsgmhw">所需技能</p></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-q9k0mw">高级设计专业知识</p></div></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-1hlyvp">无需特殊技能</p></div></div></div></div><div class="css-13cg038"><div class="css-1j9zy8k"><div class="css-0"><p class="chakra-text css-1bsgmhw">设计质量</p></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-q9k0mw">根据技能水平而变</p></div></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-1hlyvp">始终如一的专业水准</p></div></div></div></div><div class="css-13cg038"><div class="css-1j9zy8k"><div class="css-0"><p class="chakra-text css-1bsgmhw">视觉多样性</p></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-q9k0mw">受时间和专业知识限制</p></div></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-1hlyvp">无限变化</p></div></div></div></div><style data-emotion="css egtwzq">.css-egtwzq{border-bottom-width:0px;padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);}</style><div class="css-egtwzq"><div class="css-1j9zy8k"><div class="css-0"><p class="chakra-text css-1bsgmhw">迭代速度</p></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-q9k0mw">缓慢的手动过程</p></div></div><div class="css-0"><div class="css-70qvj9"><p class="chakra-text css-1hlyvp">即时更新</p></div></div></div></div></div></div></div></div><div class="css-1udq1c6"><div class="chakra-stack container css-1vr2anf"><h2 class="chakra-heading css-3bip5u">适合所有人的优势</h2><div class="css-1whttwt"><style data-emotion="css 1nkbhsn">.css-1nkbhsn{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-4);padding:var(--chakra-space-6);background:var(--chakra-colors-white);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;}</style><div class="chakra-stack css-1nkbhsn"><style data-emotion="css 151qoav">.css-151qoav{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-xl);line-height:1.2;color:var(--chakra-colors-blue-500);}</style><h2 class="chakra-heading css-151qoav">对专业人士</h2><ul role="list" class="css-1xa84ef"><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">节省日常设计任务的时间</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">创建一致的品牌视觉效果</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">生成多种设计变体</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">专注于策略，而非执行</p></li></ul></div><div class="chakra-stack css-1nkbhsn"><h2 class="chakra-heading css-151qoav">对企业</h2><ul role="list" class="css-1xa84ef"><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">提高演示质量</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">增强受众参与度</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">有效沟通复杂想法</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">保持品牌一致性</p></li></ul></div></div></div></div><div class="css-v3gv3t"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 1urha0v">.css-1urha0v{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-6);}</style><div class="chakra-stack css-1urha0v"><style data-emotion="css 1jz38zz">.css-1jz38zz{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-2xl);line-height:1.33;text-align:center;background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-purple-400));color:transparent;-webkit-background-clip:text;background-clip:text;}@media screen and (min-width: 48em){.css-1jz38zz{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;}}</style><h2 class="chakra-heading css-1jz38zz">热门使用场景</h2><style data-emotion="css 15fngqx">.css-15fngqx{font-size:var(--chakra-fontSizes-md);color:var(--chakra-colors-gray-600);text-align:center;max-width:var(--chakra-sizes-2xl);margin-bottom:var(--chakra-space-4);}</style><p class="chakra-text css-15fngqx">我们的AI图形工具足够灵活，可以处理跨行业和应用的各种可视化需求。</p><div class="css-1whttwt"><style data-emotion="css 13nb31d">.css-13nb31d{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-4);padding:var(--chakra-space-6);background:var(--chakra-colors-gray-50);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;}</style><div class="chakra-stack css-13nb31d"><h2 class="chakra-heading css-151qoav">常见场景</h2><ul role="list" class="css-1xa84ef"><li class="css-0"><p class="chakra-text css-722v25">商业演示</p><p class="chakra-text css-jneyc">创建具有数据可视化的高影响力幻灯片，让您的观点更加明确。</p></li><li class="css-0"><p class="chakra-text css-722v25">社交媒体内容</p><p class="chakra-text css-jneyc">设计可分享的信息图表，提高参与度并解释复杂主题。</p></li><li class="css-0"><p class="chakra-text css-722v25">项目文档</p><p class="chakra-text css-jneyc">为团队开发清晰的流程图和文档可视化。</p></li><li class="css-0"><p class="chakra-text css-722v25">教育材料</p><p class="chakra-text css-jneyc">构建学习辅助工具，使复杂概念更容易理解和记忆。</p></li></ul></div><div class="chakra-stack css-13nb31d"><h2 class="chakra-heading css-151qoav">谁在使用我们的工具</h2><ul role="list" class="css-1xa84ef"><li class="css-0"><p class="chakra-text css-722v25">营销人员与内容创作者</p><p class="chakra-text css-jneyc">为活动、社交媒体和博客内容创建引人入胜的视觉效果。</p></li><li class="css-0"><p class="chakra-text css-722v25">商业专业人士</p><p class="chakra-text css-jneyc">用专业质量的图形增强演示和报告。</p></li><li class="css-0"><p class="chakra-text css-722v25">教育工作者与培训师</p><p class="chakra-text css-jneyc">开发有影响力的视觉学习材料和课程内容。</p></li><li class="css-0"><p class="chakra-text css-722v25">企业家与初创公司</p><p class="chakra-text css-jneyc">在没有设计团队的情况下，为推介和营销构建专业视觉效果。</p></li></ul></div></div></div></div></div><style data-emotion="css 1qmc08l">.css-1qmc08l{background:var(--chakra-colors-purple-50);padding-top:4rem;padding-bottom:4rem;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}</style><div class="css-1qmc08l"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 1dn7332">.css-1dn7332{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-2xl);line-height:1.33;margin-bottom:var(--chakra-space-6);text-align:center;color:var(--chakra-colors-blue-600);}@media screen and (min-width: 48em){.css-1dn7332{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;}}</style><h3 class="chakra-heading css-1dn7332">趣味、新颖、启发思考的社交工具</h3><style data-emotion="css 1ljgkw5">.css-1ljgkw5{font-size:var(--chakra-fontSizes-lg);margin-bottom:var(--chakra-space-8);text-align:center;}</style><p class="chakra-text css-1ljgkw5">FunBlocks AI Graphics不仅是一个内容生成工具，更是你的思维催化器、社交助推器：</p><style data-emotion="css 1spku6t">.css-1spku6t{display:grid;grid-gap:var(--chakra-space-8);grid-template-columns:repeat(1, minmax(0, 1fr));}@media screen and (min-width: 48em){.css-1spku6t{grid-template-columns:repeat(2, minmax(0, 1fr));}}</style><div class="css-1spku6t"><style data-emotion="css t91vwm">.css-t91vwm{background:var(--chakra-colors-white);padding:var(--chakra-space-5);border-radius:var(--chakra-radii-md);box-shadow:var(--chakra-shadows-md);}</style><div class="css-t91vwm"><style data-emotion="css 1khs3ah">.css-1khs3ah{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:var(--chakra-space-3);}</style><div class="css-1khs3ah"><style data-emotion="css rrshvt">.css-rrshvt{display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-purple-400);width:var(--chakra-sizes-6);height:var(--chakra-sizes-6);margin-right:var(--chakra-space-3);}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 496 512" focusable="false" class="chakra-icon css-rrshvt" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm80 168c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm-160 0c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm194.8 170.2C334.3 380.4 292.5 400 248 400s-86.3-19.6-114.8-53.8c-13.6-16.3 11-36.7 24.6-20.5 22.4 26.9 55.2 42.2 90.2 42.2s67.8-15.4 90.2-42.2c13.4-16.2 38.1 4.2 24.6 20.5z"></path></svg><style data-emotion="css 18j379d">.css-18j379d{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-xl);line-height:1.2;}</style><h4 class="chakra-heading css-18j379d">乐趣无穷</h4></div><p class="chakra-text css-0">每一次使用都是一次充满惊喜的探索，让思考变得有趣又好玩。</p></div><div class="css-t91vwm"><div class="css-1khs3ah"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-rrshvt" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M497.9 142.1l-46.1 46.1c-4.7 4.7-12.3 4.7-17 0l-111-111c-4.7-4.7-4.7-12.3 0-17l46.1-46.1c18.7-18.7 49.1-18.7 67.9 0l60.1 60.1c18.8 18.7 18.8 49.1 0 67.9zM284.2 99.8L21.6 362.4.4 483.9c-2.9 16.4 11.4 30.6 27.8 27.8l121.5-21.3 262.6-262.6c4.7-4.7 4.7-12.3 0-17l-111-111c-4.8-4.7-12.4-4.7-17.1 0zM124.1 339.9c-5.5-5.5-5.5-14.3 0-19.8l154-154c5.5-5.5 14.3-5.5 19.8 0s5.5 14.3 0 19.8l-154 154c-5.5 5.5-14.3 5.5-19.8 0zM88 424h48v36.3l-64.5 11.3-31.1-31.1L51.7 376H88v48z"></path></svg><h4 class="chakra-heading css-18j379d">新颖简洁的形式</h4></div><p class="chakra-text css-0">精致的卡片格式，简明清晰却内容丰富，一眼就懂。</p></div><div class="css-t91vwm"><div class="css-1khs3ah"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-rrshvt" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"></path></svg><h4 class="chakra-heading css-18j379d">激发思维</h4></div><p class="chakra-text css-0">突破传统思维框架，开启创新视角，让你的想法更有色彩。</p></div><div class="css-t91vwm"><div class="css-1khs3ah"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" focusable="false" class="chakra-icon css-rrshvt" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"></path></svg><h4 class="chakra-heading css-18j379d">增强社交互动</h4></div><p class="chakra-text css-0">分享独特见解，成为朋友圈中的思维领袖，为社交互动增添深度。</p></div></div></div></div><div id="case-studies" class="css-1udq1c6"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 12rqbx6">.css-12rqbx6{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-6);width:100%;-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);}@media screen and (min-width: 48em){.css-12rqbx6{gap:var(--chakra-space-8);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><div class="chakra-stack css-12rqbx6"><style data-emotion="css 1i2nykd">.css-1i2nykd{display:inline-block;white-space:nowrap;vertical-align:middle;text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-green-100);--badge-color:var(--chakra-colors-green-800);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);padding-top:var(--chakra-space-0-5);padding-bottom:var(--chakra-space-0-5);border-radius:var(--chakra-radii-full);}.chakra-ui-dark .css-1i2nykd:not([data-theme]),[data-theme=dark] .css-1i2nykd:not([data-theme]),.css-1i2nykd[data-theme=dark]{--badge-bg:rgba(154, 230, 180, 0.16);--badge-color:var(--chakra-colors-green-200);}@media screen and (min-width: 48em){.css-1i2nykd{font-size:var(--chakra-fontSizes-md);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);}}</style><span class="chakra-badge css-1i2nykd">实际应用案例</span><style data-emotion="css 1gw4p6n">.css-1gw4p6n{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);text-align:center;background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-green-400));color:transparent;-webkit-background-clip:text;background-clip:text;-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);line-height:1.3;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1gw4p6n{font-size:var(--chakra-fontSizes-xl);line-height:1.2;}}@media screen and (min-width: 48em){.css-1gw4p6n{font-size:var(--chakra-fontSizes-2xl);line-height:1.2;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}@media screen and (min-width: 48em){.css-1gw4p6n{font-size:var(--chakra-fontSizes-3xl);}}}</style><h2 class="chakra-heading css-1gw4p6n">成功案例：AI Graphics Generator 在实践中的应用</h2><style data-emotion="css 17x1or7">.css-17x1or7{font-size:var(--chakra-fontSizes-sm);color:var(--chakra-colors-gray-600);text-align:center;max-width:var(--chakra-sizes-2xl);margin-bottom:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);line-height:1.6;}@media screen and (min-width: 48em){.css-17x1or7{font-size:var(--chakra-fontSizes-lg);margin-bottom:var(--chakra-space-4);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><p class="chakra-text css-17x1or7">了解不同行业的专业人士如何使用我们的AI图形生成器解决实际沟通挑战并取得可衡量的成果。</p><style data-emotion="css uumacn">.css-uumacn{display:grid;grid-gap:var(--chakra-space-5);grid-template-columns:repeat(1, minmax(0, 1fr));width:100%;}@media screen and (min-width: 48em){.css-uumacn{grid-gap:var(--chakra-space-8);grid-template-columns:repeat(2, minmax(0, 1fr));}}</style><div class="css-uumacn"><style data-emotion="css lawe5c">.css-lawe5c{background:var(--chakra-colors-white);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;overflow:hidden;height:100%;-webkit-transition:-webkit-transform 0.3s,box-shadow 0.3s;transition:transform 0.3s,box-shadow 0.3s;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}.css-lawe5c:hover,.css-lawe5c[data-hover]{-webkit-transform:translateY(-5px);-moz-transform:translateY(-5px);-ms-transform:translateY(-5px);transform:translateY(-5px);box-shadow:var(--chakra-shadows-lg);}</style><div role="group" touchAction="manipulation" class="css-lawe5c"><style data-emotion="css 1k5c19z">.css-1k5c19z{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:var(--chakra-space-4);-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-bottom-width:1px;border-bottom-color:var(--chakra-colors-gray-100);background:var(--chakra-colors-blue-50);-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;gap:var(--chakra-space-2);}@media screen and (min-width: 48em){.css-1k5c19z{padding:var(--chakra-space-5);gap:0px;}}</style><div class="css-1k5c19z"><style data-emotion="css dbnm4z">.css-dbnm4z{display:inline-block;line-height:1em;color:var(--chakra-colors-blue-500);width:var(--chakra-sizes-5);height:var(--chakra-sizes-5);margin-right:var(--chakra-space-3);-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-transition:-webkit-transform 0.2s;transition:transform 0.2s;}@media screen and (min-width: 48em){.css-dbnm4z{width:var(--chakra-sizes-6);height:var(--chakra-sizes-6);}}[role=group]:hover .css-dbnm4z,[role=group][data-hover] .css-dbnm4z,[data-group]:hover .css-dbnm4z,[data-group][data-hover] .css-dbnm4z,.group:hover .css-dbnm4z,.group[data-hover] .css-dbnm4z{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1);}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-dbnm4z" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM464 96H345.94c-21.38 0-32.09 25.85-16.97 40.97l32.4 32.4L288 242.75l-73.37-73.37c-12.5-12.5-32.76-12.5-45.25 0l-68.69 68.69c-6.25 6.25-6.25 16.38 0 22.63l22.62 22.62c6.25 6.25 16.38 6.25 22.63 0L192 237.25l73.37 73.37c12.5 12.5 32.76 12.5 45.25 0l96-96 32.4 32.4c15.12 15.12 40.97 4.41 40.97-16.97V112c.01-8.84-7.15-16-15.99-16z"></path></svg><div class="css-0"><style data-emotion="css ziy7n">.css-ziy7n{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);line-height:var(--chakra-lineHeights-shorter);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-ziy7n{font-size:var(--chakra-fontSizes-md);line-height:1.2;}}@media screen and (min-width: 48em){.css-ziy7n{font-size:var(--chakra-fontSizes-xl);line-height:1.2;}}</style><h2 class="chakra-heading css-ziy7n">为高管报告进行数据可视化</h2><style data-emotion="css v8fzjo">.css-v8fzjo{display:inline-block;white-space:nowrap;vertical-align:middle;text-transform:uppercase;border-radius:var(--chakra-radii-sm);font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-purple-100);--badge-color:var(--chakra-colors-purple-800);margin-top:var(--chakra-space-1);font-size:var(--chakra-fontSizes-xs);-webkit-padding-start:var(--chakra-space-1-5);padding-inline-start:var(--chakra-space-1-5);-webkit-padding-end:var(--chakra-space-1-5);padding-inline-end:var(--chakra-space-1-5);padding-top:var(--chakra-space-0-5);padding-bottom:var(--chakra-space-0-5);}.chakra-ui-dark .css-v8fzjo:not([data-theme]),[data-theme=dark] .css-v8fzjo:not([data-theme]),.css-v8fzjo[data-theme=dark]{--badge-bg:rgba(214, 188, 250, 0.16);--badge-color:var(--chakra-colors-purple-200);}@media screen and (min-width: 48em){.css-v8fzjo{font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);}}</style><span class="chakra-badge css-v8fzjo">商业智能</span></div></div><style data-emotion="css prr2ti">.css-prr2ti{padding:var(--chakra-space-4);-webkit-flex:1;-ms-flex:1;flex:1;}@media screen and (min-width: 48em){.css-prr2ti{padding:var(--chakra-space-5);}}</style><div class="css-prr2ti"><style data-emotion="css 1dhraxy">.css-1dhraxy{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-3);}@media screen and (min-width: 48em){.css-1dhraxy{gap:var(--chakra-space-4);}}</style><div class="chakra-stack css-1dhraxy"><div class="css-0"><style data-emotion="css s3n549">.css-s3n549{font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-red-500);font-size:var(--chakra-fontSizes-sm);}@media screen and (min-width: 48em){.css-s3n549{font-size:var(--chakra-fontSizes-md);}}</style><p class="chakra-text css-s3n549">Challenge:</p><style data-emotion="css o9o72d">.css-o9o72d{color:var(--chakra-colors-gray-600);font-size:var(--chakra-fontSizes-sm);line-height:1.6;}@media screen and (min-width: 48em){.css-o9o72d{font-size:var(--chakra-fontSizes-md);}}</style><p class="chakra-text css-o9o72d">一家财富500强公司需要将复杂的季度绩效数据转化为易于理解的视觉效果用于高管会议，但他们的设计团队已经被大量请求压得喘不过气来。</p></div><div class="css-0"><style data-emotion="css n252d8">.css-n252d8{font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-blue-500);font-size:var(--chakra-fontSizes-sm);}@media screen and (min-width: 48em){.css-n252d8{font-size:var(--chakra-fontSizes-md);}}</style><p class="chakra-text css-n252d8">Solution:</p><p class="chakra-text css-o9o72d">使用我们的AI图形生成器，他们只需几分钟就创建了专业的数据图表和信息图，以最少的努力突出关键指标和趋势。</p></div></div></div><style data-emotion="css 1ok8moy">.css-1ok8moy{padding:var(--chakra-space-3);border-top-width:1px;border-top-color:var(--chakra-colors-gray-100);background:var(--chakra-colors-gray-50);-webkit-transition:background 0.2s;transition:background 0.2s;}@media screen and (min-width: 48em){.css-1ok8moy{padding:var(--chakra-space-4);}}[role=group]:hover .css-1ok8moy,[role=group][data-hover] .css-1ok8moy,[data-group]:hover .css-1ok8moy,[data-group][data-hover] .css-1ok8moy,.group:hover .css-1ok8moy,.group[data-hover] .css-1ok8moy{background:var(--chakra-colors-blue-50);}</style><div class="css-1ok8moy"><style data-emotion="css 1bvc4cc">.css-1bvc4cc{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;}</style><div class="css-1bvc4cc"><style data-emotion="css lktl0q">.css-lktl0q{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);color:var(--chakra-colors-blue-600);background:var(--chakra-colors-transparent);height:36px;-webkit-transition:all 0.2s;transition:all 0.2s;}.css-lktl0q:focus-visible,.css-lktl0q[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-lktl0q:disabled,.css-lktl0q[disabled],.css-lktl0q[aria-disabled=true],.css-lktl0q[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-lktl0q:hover,.css-lktl0q[data-hover]{background:var(--chakra-colors-blue-50);}.css-lktl0q:hover:disabled,.css-lktl0q[data-hover]:disabled,.css-lktl0q:hover[disabled],.css-lktl0q[data-hover][disabled],.css-lktl0q:hover[aria-disabled=true],.css-lktl0q[data-hover][aria-disabled=true],.css-lktl0q:hover[data-disabled],.css-lktl0q[data-hover][data-disabled]{background:initial;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-lktl0q{height:var(--chakra-sizes-8);min-width:var(--chakra-sizes-8);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);}}@media screen and (min-width: 48em){.css-lktl0q{height:40px;min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-md);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}}.css-lktl0q:active,.css-lktl0q[data-active]{background:var(--chakra-colors-blue-100);}[role=group]:hover .css-lktl0q,[role=group][data-hover] .css-lktl0q,[data-group]:hover .css-lktl0q,[data-group][data-hover] .css-lktl0q,.group:hover .css-lktl0q,.group[data-hover] .css-lktl0q{background:var(--chakra-colors-blue-100);-webkit-transform:translateX(4px);-moz-transform:translateX(4px);-ms-transform:translateX(4px);transform:translateX(4px);}</style><button type="button" class="chakra-button css-lktl0q">Try it yourself<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button></div></div></div><div role="group" touchAction="manipulation" class="css-lawe5c"><div class="css-1k5c19z"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" focusable="false" class="chakra-icon css-dbnm4z" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M622.34 153.2L343.4 67.5c-15.2-4.67-31.6-4.67-46.79 0L17.66 153.2c-23.54 7.23-23.54 38.36 0 45.59l48.63 14.94c-10.67 13.19-17.23 29.28-17.88 46.9C38.78 266.15 32 276.11 32 288c0 10.78 5.68 19.85 13.86 25.65L20.33 428.53C18.11 438.52 25.71 448 35.94 448h56.11c10.24 0 17.84-9.48 15.62-19.47L82.14 313.65C90.32 307.85 96 298.78 96 288c0-11.57-6.47-21.25-15.66-26.87.76-15.02 8.44-28.3 20.69-36.72L296.6 284.5c9.06 2.78 26.44 6.25 46.79 0l278.95-85.7c23.55-7.24 23.55-38.36 0-45.6zM352.79 315.09c-28.53 8.76-52.84 3.92-65.59 0l-145.02-44.55L128 384c0 35.35 85.96 64 192 64s192-28.65 192-64l-14.18-113.47-145.03 44.56z"></path></svg><div class="css-0"><h2 class="chakra-heading css-ziy7n">教育概念可视化</h2><span class="chakra-badge css-v8fzjo">教育</span></div></div><div class="css-prr2ti"><div class="chakra-stack css-1dhraxy"><div class="css-0"><p class="chakra-text css-s3n549">Challenge:</p><p class="chakra-text css-o9o72d">一位大学教授在向本科生解释复杂的科学过程时遇到困难，发现传统教科书解释对视觉学习者来说不够充分。</p></div><div class="css-0"><p class="chakra-text css-n252d8">Solution:</p><p class="chakra-text css-o9o72d">使用我们的AI工具创建了一系列基于流程的信息图，将复杂概念以清晰、有序的步骤和视觉提示直观地分解。</p></div></div></div><div class="css-1ok8moy"><div class="css-1bvc4cc"><button type="button" class="chakra-button css-lktl0q">Try it yourself<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button></div></div></div><div role="group" touchAction="manipulation" class="css-lawe5c"><div class="css-1k5c19z"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" focusable="false" class="chakra-icon css-dbnm4z" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M384 320H256c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h128c17.67 0 32-14.33 32-32V352c0-17.67-14.33-32-32-32zM192 32c0-17.67-14.33-32-32-32H32C14.33 0 0 14.33 0 32v128c0 17.67 14.33 32 32 32h95.72l73.16 128.04C211.98 300.98 232.4 288 256 288h.28L192 175.51V128h224V64H192V32zM608 0H480c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h128c17.67 0 32-14.33 32-32V32c0-17.67-14.33-32-32-32z"></path></svg><div class="css-0"><h2 class="chakra-heading css-ziy7n">软件架构文档</h2><span class="chakra-badge css-v8fzjo">软件开发</span></div></div><div class="css-prr2ti"><div class="chakra-stack css-1dhraxy"><div class="css-0"><p class="chakra-text css-s3n549">Challenge:</p><p class="chakra-text css-o9o72d">一家科技初创公司需要为新开发人员记录其复杂的微服务架构，但缺乏专门的技术作者或设计师。</p></div><div class="css-0"><p class="chakra-text css-n252d8">Solution:</p><p class="chakra-text css-o9o72d">使用AI流程图生成器创建了全面的系统图，以最少的技术输入显示服务关系、数据流和集成点。</p></div></div></div><div class="css-1ok8moy"><div class="css-1bvc4cc"><button type="button" class="chakra-button css-lktl0q">Try it yourself<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button></div></div></div><div role="group" touchAction="manipulation" class="css-lawe5c"><div class="css-1k5c19z"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-dbnm4z" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M576 240c0-23.63-12.95-44.04-32-55.12V32.01C544 23.26 537.02 0 512 0c-7.12 0-14.19 2.38-19.98 7.02l-85.03 68.03C364.28 109.19 310.66 128 256 128H64c-35.35 0-64 28.65-64 64v96c0 35.35 28.65 64 64 64h33.7c-1.39 10.48-2.18 21.14-2.18 32 0 39.77 9.26 77.35 25.56 110.94 5.19 10.69 16.52 17.06 28.4 17.06h74.28c26.05 0 41.69-29.84 25.9-50.56-16.4-21.52-26.15-48.36-26.15-77.44 0-11.11 1.62-21.79 4.41-32H256c54.66 0 108.28 18.81 150.98 52.95l85.03 68.03a32.023 32.023 0 0 0 19.98 7.02c24.92 0 32-22.78 32-32V295.13C563.05 284.04 576 263.63 576 240zm-96 141.42l-33.05-26.44C392.95 311.78 325.12 288 256 288v-96c69.12 0 136.95-23.78 190.95-66.98L480 98.58v282.84z"></path></svg><div class="css-0"><h2 class="chakra-heading css-ziy7n">社交媒体内容策略</h2><span class="chakra-badge css-v8fzjo">数字营销</span></div></div><div class="css-prr2ti"><div class="chakra-stack css-1dhraxy"><div class="css-0"><p class="chakra-text css-s3n549">Challenge:</p><p class="chakra-text css-o9o72d">一家数字营销机构需要为社交媒体活动创建引人入胜、数据丰富的视觉效果，但受到有限的设计资源和紧迫的截止日期的限制。</p></div><div class="css-0"><p class="chakra-text css-n252d8">Solution:</p><p class="chakra-text css-o9o72d">实施我们的AI图形生成器，快速生成根据每个客户的品牌和活动目标定制的信息图和数据可视化。</p></div></div></div><div class="css-1ok8moy"><div class="css-1bvc4cc"><button type="button" class="chakra-button css-lktl0q">Try it yourself<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button></div></div></div></div><style data-emotion="css 1wb9qce">.css-1wb9qce{text-align:center;margin-top:var(--chakra-space-4);width:100%;-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);}@media screen and (min-width: 48em){.css-1wb9qce{margin-top:var(--chakra-space-6);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><div class="css-1wb9qce"><style data-emotion="css 1bthhhk">.css-1bthhhk{font-size:var(--chakra-fontSizes-md);font-weight:var(--chakra-fontWeights-medium);margin-bottom:var(--chakra-space-3);color:var(--chakra-colors-gray-700);}@media screen and (min-width: 48em){.css-1bthhhk{font-size:var(--chakra-fontSizes-lg);margin-bottom:var(--chakra-space-4);}}</style><p class="chakra-text css-1bthhhk">准备好创造属于你的成功故事了吗？</p><style data-emotion="css c85jcz">.css-c85jcz{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);background:var(--chakra-colors-blue-500);color:var(--chakra-colors-white);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);padding-top:var(--chakra-space-6);padding-bottom:var(--chakra-space-6);height:48px;border-radius:var(--chakra-radii-full);-webkit-transition:all 0.3s;transition:all 0.3s;}.css-c85jcz:focus-visible,.css-c85jcz[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-c85jcz:disabled,.css-c85jcz[disabled],.css-c85jcz[aria-disabled=true],.css-c85jcz[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-c85jcz{height:var(--chakra-sizes-10);min-width:var(--chakra-sizes-10);font-size:var(--chakra-fontSizes-md);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}}@media screen and (min-width: 48em){.css-c85jcz{height:auto;min-width:var(--chakra-sizes-12);font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:var(--chakra-space-8);padding-inline-start:var(--chakra-space-8);-webkit-padding-end:var(--chakra-space-8);padding-inline-end:var(--chakra-space-8);padding-top:var(--chakra-space-6);padding-bottom:var(--chakra-space-6);}}.css-c85jcz:active,.css-c85jcz[data-active]{background:var(--chakra-colors-blue-700);}.css-c85jcz:hover,.css-c85jcz[data-hover]{-webkit-transform:translateY(-2px);-moz-transform:translateY(-2px);-ms-transform:translateY(-2px);transform:translateY(-2px);}</style><button type="button" class="chakra-button css-c85jcz">立即开始创作<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 448 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"></path></svg></span></button></div></div></div></div><style data-emotion="css sv29pi">.css-sv29pi{background:var(--chakra-colors-blue-50);padding-top:4rem;padding-bottom:4rem;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}</style><div class="css-sv29pi"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css 1j64nac">.css-1j64nac{width:100%;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;max-width:var(--chakra-sizes-container-xl);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);}@media screen and (min-width: 48em){.css-1j64nac{-webkit-padding-start:var(--chakra-space-6);padding-inline-start:var(--chakra-space-6);-webkit-padding-end:var(--chakra-space-6);padding-inline-end:var(--chakra-space-6);}}</style><div class="chakra-container css-1j64nac"><style data-emotion="css 1ohk080">.css-1ohk080{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-6);margin-bottom:var(--chakra-space-8);}@media screen and (min-width: 48em){.css-1ohk080{gap:var(--chakra-space-8);margin-bottom:var(--chakra-space-10);}}</style><div class="chakra-stack css-1ohk080"><style data-emotion="css 1g84tsp">.css-1g84tsp{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);text-align:center;background-image:linear-gradient(to right, var(--chakra-colors-blue-400), var(--chakra-colors-purple-500));color:transparent;-webkit-background-clip:text;background-clip:text;-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);line-height:1.3;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1g84tsp{font-size:var(--chakra-fontSizes-2xl);line-height:1.33;}@media screen and (min-width: 48em){.css-1g84tsp{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;}}}@media screen and (min-width: 48em){.css-1g84tsp{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}@media screen and (min-width: 48em){.css-1g84tsp{font-size:var(--chakra-fontSizes-4xl);}}}</style><h2 class="chakra-heading css-1g84tsp">我们的比较优势</h2><style data-emotion="css 1b89gva">.css-1b89gva{font-size:var(--chakra-fontSizes-sm);text-align:center;max-width:var(--chakra-sizes-3xl);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);color:var(--chakra-colors-gray-600);line-height:1.6;}@media screen and (min-width: 48em){.css-1b89gva{font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><p class="chakra-text css-1b89gva">了解FunBlocks AI图形生成器与传统设计工具和市场上其他解决方案的比较。</p><style data-emotion="css n08hsx">.css-n08hsx{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;gap:var(--chakra-space-3);-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;width:100%;}@media screen and (min-width: 48em){.css-n08hsx{gap:var(--chakra-space-4);}}</style><div class="css-n08hsx"><style data-emotion="css 1948gew">.css-1948gew{display:inline-block;white-space:nowrap;vertical-align:middle;-webkit-padding-start:var(--chakra-space-1);padding-inline-start:var(--chakra-space-1);-webkit-padding-end:var(--chakra-space-1);padding-inline-end:var(--chakra-space-1);text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-purple-100);--badge-color:var(--chakra-colors-purple-800);padding:var(--chakra-space-1-5);border-radius:var(--chakra-radii-md);font-size:var(--chakra-fontSizes-xs);text-align:center;}.chakra-ui-dark .css-1948gew:not([data-theme]),[data-theme=dark] .css-1948gew:not([data-theme]),.css-1948gew[data-theme=dark]{--badge-bg:rgba(214, 188, 250, 0.16);--badge-color:var(--chakra-colors-purple-200);}@media screen and (min-width: 48em){.css-1948gew{padding:var(--chakra-space-2);font-size:var(--chakra-fontSizes-md);}}</style><span class="chakra-badge css-1948gew">最佳整体解决方案</span><style data-emotion="css ppnqfg">.css-ppnqfg{display:inline-block;white-space:nowrap;vertical-align:middle;-webkit-padding-start:var(--chakra-space-1);padding-inline-start:var(--chakra-space-1);-webkit-padding-end:var(--chakra-space-1);padding-inline-end:var(--chakra-space-1);text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-blue-100);--badge-color:var(--chakra-colors-blue-800);padding:var(--chakra-space-1-5);border-radius:var(--chakra-radii-md);font-size:var(--chakra-fontSizes-xs);text-align:center;}.chakra-ui-dark .css-ppnqfg:not([data-theme]),[data-theme=dark] .css-ppnqfg:not([data-theme]),.css-ppnqfg[data-theme=dark]{--badge-bg:rgba(144, 205, 244, 0.16);--badge-color:var(--chakra-colors-blue-200);}@media screen and (min-width: 48em){.css-ppnqfg{padding:var(--chakra-space-2);font-size:var(--chakra-fontSizes-md);}}</style><span class="chakra-badge css-ppnqfg">最佳性价比</span></div></div><style data-emotion="css 1ferztz">.css-1ferztz{overflow-x:auto;width:100%;-webkit-overflow-scrolling:touch;scrollbar-width:thin;}.css-1ferztz::-webkit-scrollbar{width:6px;height:6px;}.css-1ferztz::-webkit-scrollbar-thumb{background-color:rgba(0,0,0,0.2);border-radius:3px;}</style><div class="css-1ferztz"><style data-emotion="css 1ys81qj">.css-1ys81qj{font-variant-numeric:lining-nums tabular-nums;border-collapse:collapse;width:var(--chakra-sizes-full);border-width:1px;border-color:var(--chakra-colors-gray-200);border-radius:var(--chakra-radii-lg);}</style><table class="chakra-table css-1ys81qj" style="min-width:650px"><thead class="css-0"><style data-emotion="css 1qq0grb">.css-1qq0grb{background:var(--chakra-colors-purple-100);}</style><tr class="css-1qq0grb"><style data-emotion="css oikisq">.css-oikisq{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);text-align:start;color:var(--chakra-colors-gray-600);border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);border-bottom-width:2px;font-size:var(--chakra-fontSizes-xs);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-oikisq{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-xs);}}@media screen and (min-width: 48em){.css-oikisq{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-4);padding-bottom:var(--chakra-space-4);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-sm);}}.css-oikisq[data-is-numeric=true]{text-align:end;}</style><th class="css-oikisq">Feature</th><style data-emotion="css 1gg7q5q">.css-1gg7q5q{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);border-bottom-width:2px;background:var(--chakra-colors-purple-100);color:var(--chakra-colors-purple-800);font-size:var(--chakra-fontSizes-xs);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);text-align:center;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1gg7q5q{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-xs);}}@media screen and (min-width: 48em){.css-1gg7q5q{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-4);padding-bottom:var(--chakra-space-4);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-sm);}}.css-1gg7q5q[data-is-numeric=true]{text-align:end;}</style><th class="css-1gg7q5q">FunBlocks AI图形</th><style data-emotion="css fmwm82">.css-fmwm82{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);color:var(--chakra-colors-gray-600);border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);border-bottom-width:2px;font-size:var(--chakra-fontSizes-xs);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);text-align:center;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-fmwm82{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-xs);}}@media screen and (min-width: 48em){.css-fmwm82{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-4);padding-bottom:var(--chakra-space-4);line-height:var(--chakra-lineHeights-4);font-size:var(--chakra-fontSizes-sm);}}.css-fmwm82[data-is-numeric=true]{text-align:end;}</style><th class="css-fmwm82">传统设计工具</th><th class="css-fmwm82">其他AI工具</th><th class="css-fmwm82">模板服务</th></tr></thead><tbody class="css-0"><style data-emotion="css 1ijwdse">.css-1ijwdse{background:var(--chakra-colors-white);-webkit-transition:background 0.2s;transition:background 0.2s;}.css-1ijwdse:hover,.css-1ijwdse[data-hover]{background:var(--chakra-colors-gray-100);}</style><tr class="css-1ijwdse"><style data-emotion="css 19t1fda">.css-19t1fda{text-align:start;border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);font-weight:var(--chakra-fontWeights-medium);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);font-size:var(--chakra-fontSizes-xs);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-19t1fda{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);font-size:var(--chakra-fontSizes-sm);line-height:var(--chakra-lineHeights-4);}}@media screen and (min-width: 48em){.css-19t1fda{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);line-height:var(--chakra-lineHeights-5);font-size:var(--chakra-fontSizes-sm);}}.css-19t1fda[data-is-numeric=true]{text-align:end;}</style><td class="css-19t1fda"><style data-emotion="css 19rvtdi">.css-19rvtdi{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:var(--chakra-space-1);}@media screen and (min-width: 48em){.css-19rvtdi{gap:var(--chakra-space-2);}}</style><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">无需设计技能</p><span><style data-emotion="css fftmgw">.css-fftmgw{width:var(--chakra-sizes-3);height:var(--chakra-sizes-3);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-gray-400);}@media screen and (min-width: 48em){.css-fftmgw{width:var(--chakra-sizes-4);height:var(--chakra-sizes-4);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><style data-emotion="css 1kbk5rl">.css-1kbk5rl{border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);background:var(--chakra-colors-purple-50);color:var(--chakra-colors-purple-800);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);text-align:center;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1kbk5rl{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);font-size:var(--chakra-fontSizes-sm);line-height:var(--chakra-lineHeights-4);}}@media screen and (min-width: 48em){.css-1kbk5rl{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);line-height:var(--chakra-lineHeights-5);font-size:var(--chakra-fontSizes-sm);}}.css-1kbk5rl[data-is-numeric=true]{text-align:end;}</style><td class="css-1kbk5rl"><style data-emotion="css 1oe7tfs">.css-1oe7tfs{width:var(--chakra-sizes-4);height:var(--chakra-sizes-4);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-green-500);}@media screen and (min-width: 48em){.css-1oe7tfs{width:var(--chakra-sizes-5);height:var(--chakra-sizes-5);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><style data-emotion="css 14i0mcw">.css-14i0mcw{border-bottom:var(--chakra-borders-1px);border-color:var(--chakra-colors-gray-100);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);text-align:center;}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-14i0mcw{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);font-size:var(--chakra-fontSizes-sm);line-height:var(--chakra-lineHeights-4);}}@media screen and (min-width: 48em){.css-14i0mcw{-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-3);padding-bottom:var(--chakra-space-3);line-height:var(--chakra-lineHeights-5);font-size:var(--chakra-fontSizes-sm);}}.css-14i0mcw[data-is-numeric=true]{text-align:end;}</style><td class="css-14i0mcw"><style data-emotion="css wo6tpv">.css-wo6tpv{width:var(--chakra-sizes-4);height:var(--chakra-sizes-4);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-red-500);}@media screen and (min-width: 48em){.css-wo6tpv{width:var(--chakra-sizes-5);height:var(--chakra-sizes-5);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr><style data-emotion="css 1g5m6j1">.css-1g5m6j1{background:var(--chakra-colors-gray-50);-webkit-transition:background 0.2s;transition:background 0.2s;}.css-1g5m6j1:hover,.css-1g5m6j1[data-hover]{background:var(--chakra-colors-gray-100);}</style><tr class="css-1g5m6j1"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">一键生成</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td></tr><tr class="css-1ijwdse"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">内容定制</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td></tr><tr class="css-1g5m6j1"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">信息图生成</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td></tr><tr class="css-1ijwdse"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">流程图创建</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr><tr class="css-1g5m6j1"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">数据可视化</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr><tr class="css-1ijwdse"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">SVG代码生成</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td></tr><tr class="css-1g5m6j1"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">学习曲线</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr><tr class="css-1ijwdse"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">创建速度</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr><tr class="css-1g5m6j1"><td class="css-19t1fda"><div class="chakra-stack css-19rvtdi"><p class="chakra-text css-0">应用设计原则</p><span><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-fftmgw" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg></span></div></td><td class="css-1kbk5rl"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-wo6tpv" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg></td><td class="css-14i0mcw"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1oe7tfs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"></path></svg></td></tr></tbody></table></div></div></div></div><div class="css-1udq1c6"><div class="chakra-stack container css-1vr2anf"><div class="chakra-container css-1j64nac"><style data-emotion="css 1aoj7ki">.css-1aoj7ki{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-6);margin-bottom:var(--chakra-space-8);}@media screen and (min-width: 48em){.css-1aoj7ki{gap:var(--chakra-space-8);margin-bottom:var(--chakra-space-12);}}</style><div class="chakra-stack css-1aoj7ki"><h2 class="chakra-heading css-1g84tsp">基于研究的可视化</h2><p class="chakra-text css-1b89gva">我们的AI图形生成器建立在认知心理学、数据可视化和信息设计领域的坚实科学基础和循证原则之上。</p><style data-emotion="css 1soczx2">.css-1soczx2{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;gap:var(--chakra-space-2);-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;width:100%;-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);}@media screen and (min-width: 48em){.css-1soczx2{gap:var(--chakra-space-4);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><div class="css-1soczx2"><style data-emotion="css 13cqs2n">.css-13cqs2n{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);border:1px solid;border-color:currentColor;color:var(--chakra-colors-blue-600);background:var(--chakra-colors-transparent);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);border-radius:var(--chakra-radii-full);font-weight:var(--chakra-fontWeights-medium);-webkit-transition:all 0.2s;transition:all 0.2s;}.css-13cqs2n:focus-visible,.css-13cqs2n[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-13cqs2n:disabled,.css-13cqs2n[disabled],.css-13cqs2n[aria-disabled=true],.css-13cqs2n[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-13cqs2n{height:var(--chakra-sizes-6);min-width:var(--chakra-sizes-6);font-size:var(--chakra-fontSizes-xs);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);}}@media screen and (min-width: 48em){.css-13cqs2n{height:var(--chakra-sizes-8);min-width:var(--chakra-sizes-8);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);}}.chakra-button__group[data-attached][data-orientation=horizontal]>.css-13cqs2n:not(:last-of-type){-webkit-margin-end:-1px;margin-inline-end:-1px;}.chakra-button__group[data-attached][data-orientation=vertical]>.css-13cqs2n:not(:last-of-type){margin-bottom:-1px;}.css-13cqs2n:active,.css-13cqs2n[data-active]{background:var(--chakra-colors-blue-100);}.css-13cqs2n:hover,.css-13cqs2n[data-hover]{-webkit-transform:translateY(-2px);-moz-transform:translateY(-2px);-ms-transform:translateY(-2px);transform:translateY(-2px);background:var(--chakra-colors-blue-50);}</style><button type="button" class="chakra-button css-13cqs2n"><style data-emotion="css 1wh2kri">.css-1wh2kri{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-margin-end:0.5rem;margin-inline-end:0.5rem;}</style><span class="chakra-button__icon css-1wh2kri"><style data-emotion="css kluav9">.css-kluav9{width:var(--chakra-sizes-3);height:var(--chakra-sizes-3);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;}@media screen and (min-width: 48em){.css-kluav9{width:var(--chakra-sizes-4);height:var(--chakra-sizes-4);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-kluav9" aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"></path></svg></span>循证设计</button><style data-emotion="css 5pu6rz">.css-5pu6rz{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);border:1px solid;border-color:currentColor;color:var(--chakra-colors-green-600);background:var(--chakra-colors-transparent);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-1);border-radius:var(--chakra-radii-full);font-weight:var(--chakra-fontWeights-medium);-webkit-transition:all 0.2s;transition:all 0.2s;}.css-5pu6rz:focus-visible,.css-5pu6rz[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-5pu6rz:disabled,.css-5pu6rz[disabled],.css-5pu6rz[aria-disabled=true],.css-5pu6rz[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-5pu6rz{height:var(--chakra-sizes-6);min-width:var(--chakra-sizes-6);font-size:var(--chakra-fontSizes-xs);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);}}@media screen and (min-width: 48em){.css-5pu6rz{height:var(--chakra-sizes-8);min-width:var(--chakra-sizes-8);font-size:var(--chakra-fontSizes-sm);-webkit-padding-start:var(--chakra-space-3);padding-inline-start:var(--chakra-space-3);-webkit-padding-end:var(--chakra-space-3);padding-inline-end:var(--chakra-space-3);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);}}.chakra-button__group[data-attached][data-orientation=horizontal]>.css-5pu6rz:not(:last-of-type){-webkit-margin-end:-1px;margin-inline-end:-1px;}.chakra-button__group[data-attached][data-orientation=vertical]>.css-5pu6rz:not(:last-of-type){margin-bottom:-1px;}.css-5pu6rz:active,.css-5pu6rz[data-active]{background:var(--chakra-colors-green-100);}.css-5pu6rz:hover,.css-5pu6rz[data-hover]{-webkit-transform:translateY(-2px);-moz-transform:translateY(-2px);-ms-transform:translateY(-2px);transform:translateY(-2px);background:var(--chakra-colors-green-50);}</style><button type="button" class="chakra-button css-5pu6rz"><span class="chakra-button__icon css-1wh2kri"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-kluav9" aria-hidden="true" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM464 96H345.94c-21.38 0-32.09 25.85-16.97 40.97l32.4 32.4L288 242.75l-73.37-73.37c-12.5-12.5-32.76-12.5-45.25 0l-68.69 68.69c-6.25 6.25-6.25 16.38 0 22.63l22.62 22.62c6.25 6.25 16.38 6.25 22.63 0L192 237.25l73.37 73.37c12.5 12.5 32.76 12.5 45.25 0l96-96 32.4 32.4c15.12 15.12 40.97 4.41 40.97-16.97V112c.01-8.84-7.15-16-15.99-16z"></path></svg></span>数据可视化研究</button></div></div><style data-emotion="css 17ytf6g">.css-17ytf6g{display:grid;grid-gap:var(--chakra-space-4);grid-template-columns:repeat(1, minmax(0, 1fr));width:100%;}@media screen and (min-width: 30em){.css-17ytf6g{grid-template-columns:repeat(2, minmax(0, 1fr));}}@media screen and (min-width: 48em){.css-17ytf6g{grid-gap:var(--chakra-space-6);}}@media screen and (min-width: 62em){.css-17ytf6g{grid-template-columns:repeat(4, minmax(0, 1fr));}}</style><div class="css-17ytf6g"><style data-emotion="css 1ximgjy">.css-1ximgjy{position:relative;min-width:0px;word-wrap:break-word;--card-bg:var(--chakra-colors-chakra-body-bg);background-color:var(--card-bg);color:var(--chakra-colors-chakra-body-text);--card-radius:var(--chakra-radii-md);--card-padding:var(--chakra-space-5);--card-shadow:var(--chakra-shadows-base);background:var(--chakra-colors-white);border-width:1px;border-color:var(--chakra-colors-gray-200);border-radius:var(--chakra-radii-lg);overflow:hidden;box-shadow:var(--chakra-shadows-md);height:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-transition:all 0.3s;transition:all 0.3s;}.chakra-ui-dark .css-1ximgjy:not([data-theme]),[data-theme=dark] .css-1ximgjy:not([data-theme]),.css-1ximgjy[data-theme=dark]{--card-bg:var(--chakra-colors-gray-700);}.css-1ximgjy:hover,.css-1ximgjy[data-hover]{-webkit-transform:translateY(-4px);-moz-transform:translateY(-4px);-ms-transform:translateY(-4px);transform:translateY(-4px);box-shadow:var(--chakra-shadows-lg);border-color:var(--chakra-colors-blue-200);}</style><div class="chakra-card css-1ximgjy" role="group"><style data-emotion="css 1gba6f1">.css-1gba6f1{padding:var(--card-padding);padding-bottom:0px;padding-top:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}@media screen and (min-width: 48em){.css-1gba6f1{padding-top:var(--chakra-space-5);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__header css-1gba6f1"><style data-emotion="css 1s3b2wv">.css-1s3b2wv{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:var(--chakra-space-3);-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;gap:var(--chakra-space-2);}@media screen and (min-width: 30em){.css-1s3b2wv{-webkit-box-flex-wrap:nowrap;-webkit-flex-wrap:nowrap;-ms-flex-wrap:nowrap;flex-wrap:nowrap;gap:0px;}}</style><div class="css-1s3b2wv"><style data-emotion="css mpmdsc">.css-mpmdsc{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;width:var(--chakra-sizes-10);height:var(--chakra-sizes-10);border-radius:var(--chakra-radii-full);background:var(--chakra-colors-blue-100);color:var(--chakra-colors-blue-500);margin-right:var(--chakra-space-3);-webkit-transition:all 0.2s;transition:all 0.2s;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}@media screen and (min-width: 48em){.css-mpmdsc{width:var(--chakra-sizes-12);height:var(--chakra-sizes-12);margin-right:var(--chakra-space-4);}}[role=group]:hover .css-mpmdsc,[role=group][data-hover] .css-mpmdsc,[data-group]:hover .css-mpmdsc,[data-group][data-hover] .css-mpmdsc,.group:hover .css-mpmdsc,.group[data-hover] .css-mpmdsc{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05);background:var(--chakra-colors-blue-200);}</style><div class="css-mpmdsc"><style data-emotion="css u41wpb">.css-u41wpb{width:var(--chakra-sizes-5);height:var(--chakra-sizes-5);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;}@media screen and (min-width: 48em){.css-u41wpb{width:var(--chakra-sizes-6);height:var(--chakra-sizes-6);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-u41wpb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"></path></svg></div><style data-emotion="css 1xyhy5h">.css-1xyhy5h{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-semibold);color:var(--chakra-colors-gray-800);line-height:var(--chakra-lineHeights-shorter);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1xyhy5h{font-size:var(--chakra-fontSizes-md);line-height:1.2;}}@media screen and (min-width: 48em){.css-1xyhy5h{font-size:var(--chakra-fontSizes-xl);line-height:1.2;}}</style><h2 class="chakra-heading css-1xyhy5h">视觉信息处理</h2></div></div><style data-emotion="css 1da2yuw">.css-1da2yuw{padding:var(--card-padding);padding-top:var(--chakra-space-1);padding-bottom:var(--chakra-space-3);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);-webkit-flex:1;-ms-flex:1;flex:1;}@media screen and (min-width: 48em){.css-1da2yuw{padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__body css-1da2yuw"><style data-emotion="css 19vwill">.css-19vwill{font-size:var(--chakra-fontSizes-xs);color:var(--chakra-colors-gray-600);line-height:1.6;}@media screen and (min-width: 48em){.css-19vwill{font-size:var(--chakra-fontSizes-sm);}}</style><p class="chakra-text css-19vwill">我们的AI图形生成器利用了人类处理视觉信息比文本更高效的研究成果。研究表明，视觉信息的处理速度比文本快60,000倍，并能将理解力提高400%。</p></div><style data-emotion="css 74yj0q">.css-74yj0q{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:var(--card-padding);padding-top:0px;padding-bottom:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);background:var(--chakra-colors-blue-50);border-top:1px solid;border-color:var(--chakra-colors-blue-100);}@media screen and (min-width: 48em){.css-74yj0q{padding-bottom:var(--chakra-space-5);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__footer css-74yj0q"><style data-emotion="css i5u8sx">.css-i5u8sx{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:start;-webkit-box-align:start;-ms-flex-align:start;align-items:start;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-1);width:100%;}@media screen and (min-width: 48em){.css-i5u8sx{gap:var(--chakra-space-1-5);}}</style><div class="chakra-stack css-i5u8sx"><style data-emotion="css 1o0houg">.css-1o0houg{font-size:var(--chakra-fontSizes-2xs);font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-blue-700);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);}@media screen and (min-width: 48em){.css-1o0houg{font-size:var(--chakra-fontSizes-xs);}}</style><p class="chakra-text css-1o0houg">Research Citations:</p><style data-emotion="css 1lgeeww">.css-1lgeeww{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:var(--chakra-space-1);font-size:var(--chakra-fontSizes-2xs);color:var(--chakra-colors-gray-600);}@media screen and (min-width: 48em){.css-1lgeeww{gap:var(--chakra-space-2);font-size:var(--chakra-fontSizes-xs);}}</style><div class="chakra-stack css-1lgeeww"><style data-emotion="css yikk2y">.css-yikk2y{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--chakra-line-clamp);--chakra-line-clamp:1;max-width:90%;}@media screen and (min-width: 48em){.css-yikk2y{max-width:90%;}}</style><p class="chakra-text css-yikk2y">Mayer, R. E. (2020). Multimedia learning. Cambridge University Press.</p><style data-emotion="css pap52p">.css-pap52p{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:var(--chakra-colors-blue-500);-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}.css-pap52p:focus-visible,.css-pap52p[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-pap52p:hover,.css-pap52p[data-hover]{-webkit-text-decoration:none;text-decoration:none;color:var(--chakra-colors-blue-600);}</style><a target="_blank" rel="noopener" class="chakra-link css-pap52p" href="https://www.cambridge.org/highereducation/books/multimedia-learning/FB7E79A165D24D47CEACEB4D2C426ECD#overview" aria-label="View research citation"><style data-emotion="css 1odg8xl">.css-1odg8xl{width:var(--chakra-sizes-2-5);height:var(--chakra-sizes-2-5);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;-webkit-transition:-webkit-transform 0.2s;transition:transform 0.2s;}@media screen and (min-width: 48em){.css-1odg8xl{width:var(--chakra-sizes-3);height:var(--chakra-sizes-3);}}.css-1odg8xl:hover,.css-1odg8xl[data-hover]{-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-ms-transform:scale(1.2);transform:scale(1.2);}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Mayer, R. E., &amp; Moreno, R. (2003). Nine ways to reduce cognitive load in multimedia learning. Educational Psychologist, 38(1), 43-52.</p><a target="_blank" rel="noopener" class="chakra-link css-pap52p" href="https://www.uky.edu/~gmswan3/544/9_ways_to_reduce_CL.pdf" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div></div></div></div><style data-emotion="css vy9tml">.css-vy9tml{position:relative;min-width:0px;word-wrap:break-word;--card-bg:var(--chakra-colors-chakra-body-bg);background-color:var(--card-bg);color:var(--chakra-colors-chakra-body-text);--card-radius:var(--chakra-radii-md);--card-padding:var(--chakra-space-5);--card-shadow:var(--chakra-shadows-base);background:var(--chakra-colors-white);border-width:1px;border-color:var(--chakra-colors-gray-200);border-radius:var(--chakra-radii-lg);overflow:hidden;box-shadow:var(--chakra-shadows-md);height:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-transition:all 0.3s;transition:all 0.3s;}.chakra-ui-dark .css-vy9tml:not([data-theme]),[data-theme=dark] .css-vy9tml:not([data-theme]),.css-vy9tml[data-theme=dark]{--card-bg:var(--chakra-colors-gray-700);}.css-vy9tml:hover,.css-vy9tml[data-hover]{-webkit-transform:translateY(-4px);-moz-transform:translateY(-4px);-ms-transform:translateY(-4px);transform:translateY(-4px);box-shadow:var(--chakra-shadows-lg);border-color:var(--chakra-colors-purple-200);}</style><div class="chakra-card css-vy9tml" role="group"><div class="chakra-card__header css-1gba6f1"><div class="css-1s3b2wv"><style data-emotion="css 1jzlx78">.css-1jzlx78{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;width:var(--chakra-sizes-10);height:var(--chakra-sizes-10);border-radius:var(--chakra-radii-full);background:var(--chakra-colors-purple-100);color:var(--chakra-colors-purple-500);margin-right:var(--chakra-space-3);-webkit-transition:all 0.2s;transition:all 0.2s;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}@media screen and (min-width: 48em){.css-1jzlx78{width:var(--chakra-sizes-12);height:var(--chakra-sizes-12);margin-right:var(--chakra-space-4);}}[role=group]:hover .css-1jzlx78,[role=group][data-hover] .css-1jzlx78,[data-group]:hover .css-1jzlx78,[data-group][data-hover] .css-1jzlx78,.group:hover .css-1jzlx78,.group[data-hover] .css-1jzlx78{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05);background:var(--chakra-colors-purple-200);}</style><div class="css-1jzlx78"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-u41wpb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M332.8 320h38.4c6.4 0 12.8-6.4 12.8-12.8V172.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V76.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-288 0h38.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zM496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z"></path></svg></div><h2 class="chakra-heading css-1xyhy5h">数据可视化原则</h2></div></div><div class="chakra-card__body css-1da2yuw"><p class="chakra-text css-19vwill">我们的AI实施了来自Edward Tufte和Stephen Few等领先研究者的既定数据可视化原则，确保复杂数据以清晰、精确和最小认知负荷的方式呈现。</p></div><style data-emotion="css p0aodr">.css-p0aodr{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:var(--card-padding);padding-top:0px;padding-bottom:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);background:var(--chakra-colors-purple-50);border-top:1px solid;border-color:var(--chakra-colors-purple-100);}@media screen and (min-width: 48em){.css-p0aodr{padding-bottom:var(--chakra-space-5);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__footer css-p0aodr"><div class="chakra-stack css-i5u8sx"><style data-emotion="css 1ofiavy">.css-1ofiavy{font-size:var(--chakra-fontSizes-2xs);font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-purple-700);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);}@media screen and (min-width: 48em){.css-1ofiavy{font-size:var(--chakra-fontSizes-xs);}}</style><p class="chakra-text css-1ofiavy">Research Citations:</p><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Tufte, E. R. (2001). The Visual Display of Quantitative Information (2nd ed.). Graphics Press.</p><style data-emotion="css d9wjin">.css-d9wjin{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:var(--chakra-colors-purple-500);-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}.css-d9wjin:focus-visible,.css-d9wjin[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-d9wjin:hover,.css-d9wjin[data-hover]{-webkit-text-decoration:none;text-decoration:none;color:var(--chakra-colors-purple-600);}</style><a target="_blank" rel="noopener" class="chakra-link css-d9wjin" href="https://www.edwardtufte.com/tufte/books_vdqi" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Cairo, A. (2012). The Functional Art: An introduction to information graphics and visualization. New Riders.</p><a target="_blank" rel="noopener" class="chakra-link css-d9wjin" href="https://www.amazon.com/Functional-Art-introduction-information-visualization/dp/0321834739" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div></div></div></div><style data-emotion="css fonoug">.css-fonoug{position:relative;min-width:0px;word-wrap:break-word;--card-bg:var(--chakra-colors-chakra-body-bg);background-color:var(--card-bg);color:var(--chakra-colors-chakra-body-text);--card-radius:var(--chakra-radii-md);--card-padding:var(--chakra-space-5);--card-shadow:var(--chakra-shadows-base);background:var(--chakra-colors-white);border-width:1px;border-color:var(--chakra-colors-gray-200);border-radius:var(--chakra-radii-lg);overflow:hidden;box-shadow:var(--chakra-shadows-md);height:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-transition:all 0.3s;transition:all 0.3s;}.chakra-ui-dark .css-fonoug:not([data-theme]),[data-theme=dark] .css-fonoug:not([data-theme]),.css-fonoug[data-theme=dark]{--card-bg:var(--chakra-colors-gray-700);}.css-fonoug:hover,.css-fonoug[data-hover]{-webkit-transform:translateY(-4px);-moz-transform:translateY(-4px);-ms-transform:translateY(-4px);transform:translateY(-4px);box-shadow:var(--chakra-shadows-lg);border-color:var(--chakra-colors-green-200);}</style><div class="chakra-card css-fonoug" role="group"><div class="chakra-card__header css-1gba6f1"><div class="css-1s3b2wv"><style data-emotion="css 1o59het">.css-1o59het{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;width:var(--chakra-sizes-10);height:var(--chakra-sizes-10);border-radius:var(--chakra-radii-full);background:var(--chakra-colors-green-100);color:var(--chakra-colors-green-500);margin-right:var(--chakra-space-3);-webkit-transition:all 0.2s;transition:all 0.2s;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}@media screen and (min-width: 48em){.css-1o59het{width:var(--chakra-sizes-12);height:var(--chakra-sizes-12);margin-right:var(--chakra-space-4);}}[role=group]:hover .css-1o59het,[role=group][data-hover] .css-1o59het,[data-group]:hover .css-1o59het,[data-group][data-hover] .css-1o59het,.group:hover .css-1o59het,.group[data-hover] .css-1o59het{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05);background:var(--chakra-colors-green-200);}</style><div class="css-1o59het"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-u41wpb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"></path></svg></div><h2 class="chakra-heading css-1xyhy5h">认知负荷理论</h2></div></div><div class="chakra-card__body css-1da2yuw"><p class="chakra-text css-19vwill">我们的AI图形生成器应用认知负荷理论创建视觉效果，减少外部认知负荷，让用户能够专注于理解内容而不是解读复杂的呈现方式。</p></div><style data-emotion="css 1jv3pqu">.css-1jv3pqu{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:var(--card-padding);padding-top:0px;padding-bottom:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);background:var(--chakra-colors-green-50);border-top:1px solid;border-color:var(--chakra-colors-green-100);}@media screen and (min-width: 48em){.css-1jv3pqu{padding-bottom:var(--chakra-space-5);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__footer css-1jv3pqu"><div class="chakra-stack css-i5u8sx"><style data-emotion="css 11k343f">.css-11k343f{font-size:var(--chakra-fontSizes-2xs);font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-green-700);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);}@media screen and (min-width: 48em){.css-11k343f{font-size:var(--chakra-fontSizes-xs);}}</style><p class="chakra-text css-11k343f">Research Citations:</p><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Sweller, J. (2011). Cognitive load theory. Psychology of Learning and Motivation, 55, 37-76.</p><style data-emotion="css 18z9w4k">.css-18z9w4k{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:var(--chakra-colors-green-500);-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}.css-18z9w4k:focus-visible,.css-18z9w4k[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-18z9w4k:hover,.css-18z9w4k[data-hover]{-webkit-text-decoration:none;text-decoration:none;color:var(--chakra-colors-green-600);}</style><a target="_blank" rel="noopener" class="chakra-link css-18z9w4k" href="https://www.researchgate.net/publication/279149314_Cognitive_Load_Theory" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Mayer, R. E., &amp; Moreno, R. (2003). Nine ways to reduce cognitive load in multimedia learning. Educational Psychologist, 38(1), 43-52.</p><a target="_blank" rel="noopener" class="chakra-link css-18z9w4k" href="https://www.uky.edu/~gmswan3/544/9_ways_to_reduce_CL.pdf" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div></div></div></div><style data-emotion="css 1wb6ouh">.css-1wb6ouh{position:relative;min-width:0px;word-wrap:break-word;--card-bg:var(--chakra-colors-chakra-body-bg);background-color:var(--card-bg);color:var(--chakra-colors-chakra-body-text);--card-radius:var(--chakra-radii-md);--card-padding:var(--chakra-space-5);--card-shadow:var(--chakra-shadows-base);background:var(--chakra-colors-white);border-width:1px;border-color:var(--chakra-colors-gray-200);border-radius:var(--chakra-radii-lg);overflow:hidden;box-shadow:var(--chakra-shadows-md);height:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-transition:all 0.3s;transition:all 0.3s;}.chakra-ui-dark .css-1wb6ouh:not([data-theme]),[data-theme=dark] .css-1wb6ouh:not([data-theme]),.css-1wb6ouh[data-theme=dark]{--card-bg:var(--chakra-colors-gray-700);}.css-1wb6ouh:hover,.css-1wb6ouh[data-hover]{-webkit-transform:translateY(-4px);-moz-transform:translateY(-4px);-ms-transform:translateY(-4px);transform:translateY(-4px);box-shadow:var(--chakra-shadows-lg);border-color:var(--chakra-colors-orange-200);}</style><div class="chakra-card css-1wb6ouh" role="group"><div class="chakra-card__header css-1gba6f1"><div class="css-1s3b2wv"><style data-emotion="css 7pw82d">.css-7pw82d{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;width:var(--chakra-sizes-10);height:var(--chakra-sizes-10);border-radius:var(--chakra-radii-full);background:var(--chakra-colors-orange-100);color:var(--chakra-colors-orange-500);margin-right:var(--chakra-space-3);-webkit-transition:all 0.2s;transition:all 0.2s;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}@media screen and (min-width: 48em){.css-7pw82d{width:var(--chakra-sizes-12);height:var(--chakra-sizes-12);margin-right:var(--chakra-space-4);}}[role=group]:hover .css-7pw82d,[role=group][data-hover] .css-7pw82d,[data-group]:hover .css-7pw82d,[data-group][data-hover] .css-7pw82d,.group:hover .css-7pw82d,.group[data-hover] .css-7pw82d{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05);background:var(--chakra-colors-orange-200);}</style><div class="css-7pw82d"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 640 512" focusable="false" class="chakra-icon css-u41wpb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M384 320H256c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h128c17.67 0 32-14.33 32-32V352c0-17.67-14.33-32-32-32zM192 32c0-17.67-14.33-32-32-32H32C14.33 0 0 14.33 0 32v128c0 17.67 14.33 32 32 32h95.72l73.16 128.04C211.98 300.98 232.4 288 256 288h.28L192 175.51V128h224V64H192V32zM608 0H480c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h128c17.67 0 32-14.33 32-32V32c0-17.67-14.33-32-32-32z"></path></svg></div><h2 class="chakra-heading css-1xyhy5h">信息设计</h2></div></div><div class="chakra-card__body css-1da2yuw"><p class="chakra-text css-19vwill">我们的系统融合了信息设计和视觉层次原则，确保最重要的信息突出显示，复杂关系清晰传达。</p></div><style data-emotion="css 1vo4eo2">.css-1vo4eo2{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:var(--card-padding);padding-top:0px;padding-bottom:var(--chakra-space-4);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);background:var(--chakra-colors-orange-50);border-top:1px solid;border-color:var(--chakra-colors-orange-100);}@media screen and (min-width: 48em){.css-1vo4eo2{padding-bottom:var(--chakra-space-5);-webkit-padding-start:var(--chakra-space-5);padding-inline-start:var(--chakra-space-5);-webkit-padding-end:var(--chakra-space-5);padding-inline-end:var(--chakra-space-5);}}</style><div class="chakra-card__footer css-1vo4eo2"><div class="chakra-stack css-i5u8sx"><style data-emotion="css 1hzt8e3">.css-1hzt8e3{font-size:var(--chakra-fontSizes-2xs);font-weight:var(--chakra-fontWeights-bold);color:var(--chakra-colors-orange-700);text-transform:uppercase;letter-spacing:var(--chakra-letterSpacings-wider);}@media screen and (min-width: 48em){.css-1hzt8e3{font-size:var(--chakra-fontSizes-xs);}}</style><p class="chakra-text css-1hzt8e3">Research Citations:</p><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Cairo, A. (2012). The Functional Art: An introduction to information graphics and visualization. New Riders.</p><style data-emotion="css 20e080">.css-20e080{transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-fast);transition-timing-function:var(--chakra-transition-easing-ease-out);cursor:pointer;-webkit-text-decoration:none;text-decoration:none;outline:2px solid transparent;outline-offset:2px;color:var(--chakra-colors-orange-500);-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}.css-20e080:focus-visible,.css-20e080[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-20e080:hover,.css-20e080[data-hover]{-webkit-text-decoration:none;text-decoration:none;color:var(--chakra-colors-orange-600);}</style><a target="_blank" rel="noopener" class="chakra-link css-20e080" href="https://www.amazon.com/Functional-Art-introduction-information-visualization/dp/0321834739" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div><div class="chakra-stack css-1lgeeww"><p class="chakra-text css-yikk2y">Krum, R. (2013). Cool Infographics: Effective Communication with Data Visualization and Design. Wiley.</p><a target="_blank" rel="noopener" class="chakra-link css-20e080" href="https://coolinfographics.com/book" aria-label="View research citation"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1odg8xl" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"></path></svg></a></div></div></div></div></div></div></div></div><div class="css-v3gv3t"><div class="chakra-stack container css-1vr2anf"><div class="chakra-container css-1j64nac"><div class="chakra-stack css-1aoj7ki"><h2 class="chakra-heading css-1g84tsp">用户反馈</h2><style data-emotion="css 1f3jjns">.css-1f3jjns{font-size:var(--chakra-fontSizes-md);text-align:center;max-width:var(--chakra-sizes-3xl);-webkit-padding-start:var(--chakra-space-2);padding-inline-start:var(--chakra-space-2);-webkit-padding-end:var(--chakra-space-2);padding-inline-end:var(--chakra-space-2);color:var(--chakra-colors-gray-600);}@media screen and (min-width: 48em){.css-1f3jjns{font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;}}</style><p class="chakra-text css-1f3jjns">加入数千名已经通过FunBlocks AI改变体验的用户。</p><style data-emotion="css 17fbd2p">.css-17fbd2p{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;gap:var(--chakra-space-3);width:100%;-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;}@media screen and (min-width: 30em){.css-17fbd2p{-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;}}@media screen and (min-width: 48em){.css-17fbd2p{gap:var(--chakra-space-4);}}</style><div spacing="[object Object]" class="css-17fbd2p"><style data-emotion="css q4k5wb">.css-q4k5wb{display:inline-block;white-space:nowrap;vertical-align:middle;-webkit-padding-start:var(--chakra-space-1);padding-inline-start:var(--chakra-space-1);-webkit-padding-end:var(--chakra-space-1);padding-inline-end:var(--chakra-space-1);text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-green-100);--badge-color:var(--chakra-colors-green-800);padding:var(--chakra-space-1-5);border-radius:var(--chakra-radii-md);font-size:var(--chakra-fontSizes-sm);width:var(--chakra-sizes-full);text-align:center;}.chakra-ui-dark .css-q4k5wb:not([data-theme]),[data-theme=dark] .css-q4k5wb:not([data-theme]),.css-q4k5wb[data-theme=dark]{--badge-bg:rgba(154, 230, 180, 0.16);--badge-color:var(--chakra-colors-green-200);}@media screen and (min-width: 30em){.css-q4k5wb{width:auto;}}@media screen and (min-width: 48em){.css-q4k5wb{padding:var(--chakra-space-2);font-size:var(--chakra-fontSizes-md);}}</style><span class="chakra-badge css-q4k5wb"><style data-emotion="css kv5mw5">.css-kv5mw5{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:0.5rem;}@media screen and (min-width: 30em){.css-kv5mw5{-webkit-box-pack:start;-ms-flex-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start;}}</style><div class="chakra-stack css-kv5mw5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-kluav9" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><p class="chakra-text css-0">平均评分4.7</p></div></span><style data-emotion="css 1busupc">.css-1busupc{display:inline-block;white-space:nowrap;vertical-align:middle;-webkit-padding-start:var(--chakra-space-1);padding-inline-start:var(--chakra-space-1);-webkit-padding-end:var(--chakra-space-1);padding-inline-end:var(--chakra-space-1);text-transform:uppercase;font-weight:var(--chakra-fontWeights-bold);background:var(--badge-bg);color:var(--badge-color);box-shadow:var(--badge-shadow);--badge-bg:var(--chakra-colors-blue-100);--badge-color:var(--chakra-colors-blue-800);padding:var(--chakra-space-1-5);border-radius:var(--chakra-radii-md);font-size:var(--chakra-fontSizes-sm);width:var(--chakra-sizes-full);text-align:center;}.chakra-ui-dark .css-1busupc:not([data-theme]),[data-theme=dark] .css-1busupc:not([data-theme]),.css-1busupc[data-theme=dark]{--badge-bg:rgba(144, 205, 244, 0.16);--badge-color:var(--chakra-colors-blue-200);}@media screen and (min-width: 30em){.css-1busupc{width:auto;}}@media screen and (min-width: 48em){.css-1busupc{padding:var(--chakra-space-2);font-size:var(--chakra-fontSizes-md);}}</style><span class="chakra-badge css-1busupc">15,000+活跃用户</span></div></div><style data-emotion="css f4v059">.css-f4v059{display:grid;grid-gap:var(--chakra-space-5);grid-template-columns:repeat(1, minmax(0, 1fr));width:100%;}@media screen and (min-width: 48em){.css-f4v059{grid-gap:var(--chakra-space-8);grid-template-columns:repeat(2, minmax(0, 1fr));}}@media screen and (min-width: 62em){.css-f4v059{grid-template-columns:repeat(3, minmax(0, 1fr));}}</style><div class="css-f4v059"><style data-emotion="css 8k16en">.css-8k16en{background:var(--chakra-colors-white);padding:var(--chakra-space-4);border-radius:var(--chakra-radii-lg);border-width:1px;border-color:var(--chakra-colors-gray-200);box-shadow:var(--chakra-shadows-md);position:relative;height:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-transition:all 0.3s;transition:all 0.3s;}@media screen and (min-width: 30em){.css-8k16en{padding:var(--chakra-space-5);}}@media screen and (min-width: 48em){.css-8k16en{padding:var(--chakra-space-6);}}.css-8k16en:hover,.css-8k16en[data-hover]{-webkit-transform:translateY(-4px);-moz-transform:translateY(-4px);-ms-transform:translateY(-4px);transform:translateY(-4px);box-shadow:var(--chakra-shadows-lg);}</style><div class="css-8k16en"><style data-emotion="css 1bz9hb3">.css-1bz9hb3{width:var(--chakra-sizes-6);height:var(--chakra-sizes-6);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-purple-100);position:absolute;top:var(--chakra-space-3);left:var(--chakra-space-3);opacity:0.6;}@media screen and (min-width: 48em){.css-1bz9hb3{top:var(--chakra-space-4);left:var(--chakra-space-4);width:var(--chakra-sizes-8);height:var(--chakra-sizes-8);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><style data-emotion="css rjoygm">.css-rjoygm{margin-bottom:var(--chakra-space-4);padding-top:var(--chakra-space-5);padding-left:var(--chakra-space-5);-webkit-flex:1;-ms-flex:1;flex:1;}@media screen and (min-width: 48em){.css-rjoygm{margin-bottom:var(--chakra-space-6);padding-top:var(--chakra-space-6);padding-left:var(--chakra-space-6);}}</style><div class="css-rjoygm"><style data-emotion="css fil0ch">.css-fil0ch{font-size:var(--chakra-fontSizes-sm);font-style:italic;color:var(--chakra-colors-gray-600);line-height:1.6;}@media screen and (min-width: 48em){.css-fil0ch{font-size:var(--chakra-fontSizes-md);}}</style><p class="chakra-text css-fil0ch">&quot;<!-- -->作为一名没有设计背景的营销经理，我一直在为我们的活动创建专业信息图表而苦苦挣扎。AI图形生成器彻底改变了一切！现在我可以在几分钟内创建出令人惊叹的视觉效果，这些效果过去需要我们的设计团队数天时间。自从我们开始使用这些图形以来，我们的参与率提高了45%。<!-- -->&quot;</p></div><style data-emotion="css 492x6i">.css-492x6i{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;gap:var(--chakra-space-2);}@media screen and (min-width: 30em){.css-492x6i{-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}}@media screen and (min-width: 48em){.css-492x6i{gap:var(--chakra-space-3);}}</style><div spacing="[object Object]" class="css-492x6i"><style data-emotion="css 6yvt4u">.css-6yvt4u{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:var(--chakra-space-3);-webkit-flex:1;-ms-flex:1;flex:1;min-width:0px;}@media screen and (min-width: 48em){.css-6yvt4u{gap:var(--chakra-space-4);}}</style><div class="chakra-stack css-6yvt4u"><style data-emotion="css qt8js">.css-qt8js{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-white);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-qt8js:not([data-loaded]){--avatar-bg:#03249b;}.chakra-ui-dark .css-qt8js:not([data-theme]),[data-theme=dark] .css-qt8js:not([data-theme]),.css-qt8js[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-qt8js{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-qt8js{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-qt8js"><style data-emotion="css 1ebyn6">.css-1ebyn6{font-size:var(--avatar-font-size);line-height:1;}</style><div role="img" aria-label="Sarah Chen" class="chakra-avatar__initials css-1ebyn6">SC</div></span><style data-emotion="css sz3opf">.css-sz3opf{min-width:0px;}</style><div class="css-sz3opf"><style data-emotion="css 18398hb">.css-18398hb{font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-sm);color:var(--chakra-colors-gray-800);overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--chakra-line-clamp);--chakra-line-clamp:1;}@media screen and (min-width: 48em){.css-18398hb{font-size:var(--chakra-fontSizes-md);}}</style><p class="chakra-text css-18398hb">Sarah Chen</p><style data-emotion="css cmmax5">.css-cmmax5{font-size:var(--chakra-fontSizes-xs);color:var(--chakra-colors-gray-500);overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--chakra-line-clamp);--chakra-line-clamp:1;}@media screen and (min-width: 48em){.css-cmmax5{font-size:var(--chakra-fontSizes-sm);}}</style><p class="chakra-text css-cmmax5">营销经理</p><style data-emotion="css mfd9ip">.css-mfd9ip{font-size:var(--chakra-fontSizes-2xs);color:var(--chakra-colors-gray-500);overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:var(--chakra-line-clamp);--chakra-line-clamp:1;}@media screen and (min-width: 48em){.css-mfd9ip{font-size:var(--chakra-fontSizes-xs);}}</style><p class="chakra-text css-mfd9ip">TechCorp Inc.</p></div></div><style data-emotion="css jmwje4">.css-jmwje4{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:start;-ms-flex-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;gap:var(--chakra-space-1);-webkit-align-self:flex-start;-ms-flex-item-align:flex-start;align-self:flex-start;margin-top:var(--chakra-space-1);}@media screen and (min-width: 30em){.css-jmwje4{-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;margin-top:0px;}}</style><div class="chakra-stack css-jmwje4"><style data-emotion="css 14b709o">.css-14b709o{width:var(--chakra-sizes-3);height:var(--chakra-sizes-3);display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:var(--chakra-colors-yellow-400);}@media screen and (min-width: 48em){.css-14b709o{width:var(--chakra-sizes-4);height:var(--chakra-sizes-4);}}</style><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg></div></div></div><div class="css-8k16en"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><div class="css-rjoygm"><p class="chakra-text css-fil0ch">&quot;<!-- -->流程图生成器对我们的开发团队来说是一个游戏规则改变者。我们过去花费数小时创建系统架构图，但现在我们可以在几分钟内生成它们。AI似乎能从文本描述中准确理解我们的需求。这就像拥有一个全天候待命的专业设计师。<!-- -->&quot;</p></div><div spacing="[object Object]" class="css-492x6i"><div class="chakra-stack css-6yvt4u"><style data-emotion="css 1377h0t">.css-1377h0t{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-white);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-1377h0t:not([data-loaded]){--avatar-bg:#dc1490;}.chakra-ui-dark .css-1377h0t:not([data-theme]),[data-theme=dark] .css-1377h0t:not([data-theme]),.css-1377h0t[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1377h0t{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-1377h0t{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-1377h0t"><div role="img" aria-label="David Martinez" class="chakra-avatar__initials css-1ebyn6">DM</div></span><div class="css-sz3opf"><p class="chakra-text css-18398hb">David Martinez</p><p class="chakra-text css-cmmax5">软件架构师</p><p class="chakra-text css-mfd9ip">Innovate Solutions</p></div></div><div class="chakra-stack css-jmwje4"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg></div></div></div><div class="css-8k16en"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><div class="css-rjoygm"><p class="chakra-text css-fil0ch">&quot;<!-- -->作为一名数据分析师，我需要定期向非技术利益相关者展示复杂的发现。AI图形生成器帮助我将枯燥的电子表格转化为引人入胜的数据可视化，讲述一个故事。我的演示现在能够从高管那里获得更多的参与和理解。<!-- -->&quot;</p></div><div spacing="[object Object]" class="css-492x6i"><div class="chakra-stack css-6yvt4u"><style data-emotion="css 13l33n8">.css-13l33n8{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-gray-800);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-13l33n8:not([data-loaded]){--avatar-bg:#68e242;}.chakra-ui-dark .css-13l33n8:not([data-theme]),[data-theme=dark] .css-13l33n8:not([data-theme]),.css-13l33n8[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-13l33n8{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-13l33n8{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-13l33n8"><div role="img" aria-label="Michael Rodriguez" class="chakra-avatar__initials css-1ebyn6">MR</div></span><div class="css-sz3opf"><p class="chakra-text css-18398hb">Michael Rodriguez</p><p class="chakra-text css-cmmax5">数据分析师</p><p class="chakra-text css-mfd9ip">Global Finance Group</p></div></div><div class="chakra-stack css-jmwje4"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 536 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M508.55 171.51L362.18 150.2 296.77 17.81C290.89 5.98 279.42 0 267.95 0c-11.4 0-22.79 5.9-28.69 17.81l-65.43 132.38-146.38 21.29c-26.25 3.8-36.77 36.09-17.74 54.59l105.89 103-25.06 145.48C86.98 495.33 103.57 512 122.15 512c4.93 0 10-1.17 14.87-3.75l130.95-68.68 130.94 68.7c4.86 2.55 9.92 3.71 14.83 3.71 18.6 0 35.22-16.61 31.66-37.4l-25.03-145.49 105.91-102.98c19.04-18.5 8.52-50.8-17.73-54.6zm-121.74 123.2l-18.12 17.62 4.28 24.88 19.52 113.45-102.13-53.59-22.38-11.74.03-317.19 51.03 103.29 11.18 22.63 25.01 3.64 114.23 16.63-82.65 80.38z"></path></svg></div></div></div><div class="css-8k16en"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><div class="css-rjoygm"><p class="chakra-text css-fil0ch">&quot;<!-- -->我使用AI图形生成器制作课堂材料，我的学生非常喜欢！这些视觉效果以文本无法做到的方式解释复杂概念。最棒的是我可以为每节课快速创建定制图形 - 过去需要数小时的工作现在只需几分钟。<!-- -->&quot;</p></div><div spacing="[object Object]" class="css-492x6i"><div class="chakra-stack css-6yvt4u"><style data-emotion="css 9gdsrj">.css-9gdsrj{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-white);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-9gdsrj:not([data-loaded]){--avatar-bg:#55a01f;}.chakra-ui-dark .css-9gdsrj:not([data-theme]),[data-theme=dark] .css-9gdsrj:not([data-theme]),.css-9gdsrj[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-9gdsrj{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-9gdsrj{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-9gdsrj"><div role="img" aria-label="Emily Johnson" class="chakra-avatar__initials css-1ebyn6">EJ</div></span><div class="css-sz3opf"><p class="chakra-text css-18398hb">Emily Johnson</p><p class="chakra-text css-cmmax5">高中教师</p><p class="chakra-text css-mfd9ip">Westlake High School</p></div></div><div class="chakra-stack css-jmwje4"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg></div></div></div><div class="css-8k16en"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><div class="css-rjoygm"><p class="chakra-text css-fil0ch">&quot;<!-- -->SVG代码生成器对我们的Web开发团队来说非常出色。我们可以快速创建轻量级且响应式的自定义图形。代码干净且结构良好，易于集成到我们的项目中。这个工具显著减少了我们对库存图形的依赖。<!-- -->&quot;</p></div><div spacing="[object Object]" class="css-492x6i"><div class="chakra-stack css-6yvt4u"><style data-emotion="css 1o22qgp">.css-1o22qgp{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-gray-800);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-1o22qgp:not([data-loaded]){--avatar-bg:#e591d9;}.chakra-ui-dark .css-1o22qgp:not([data-theme]),[data-theme=dark] .css-1o22qgp:not([data-theme]),.css-1o22qgp[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-1o22qgp{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-1o22qgp{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-1o22qgp"><div role="img" aria-label="Jennifer Park" class="chakra-avatar__initials css-1ebyn6">JP</div></span><div class="css-sz3opf"><p class="chakra-text css-18398hb">Jennifer Park</p><p class="chakra-text css-cmmax5">前端开发者</p><p class="chakra-text css-mfd9ip">WebTech Solutions</p></div></div><div class="chakra-stack css-jmwje4"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 536 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M508.55 171.51L362.18 150.2 296.77 17.81C290.89 5.98 279.42 0 267.95 0c-11.4 0-22.79 5.9-28.69 17.81l-65.43 132.38-146.38 21.29c-26.25 3.8-36.77 36.09-17.74 54.59l105.89 103-25.06 145.48C86.98 495.33 103.57 512 122.15 512c4.93 0 10-1.17 14.87-3.75l130.95-68.68 130.94 68.7c4.86 2.55 9.92 3.71 14.83 3.71 18.6 0 35.22-16.61 31.66-37.4l-25.03-145.49 105.91-102.98c19.04-18.5 8.52-50.8-17.73-54.6zm-121.74 123.2l-18.12 17.62 4.28 24.88 19.52 113.45-102.13-53.59-22.38-11.74.03-317.19 51.03 103.29 11.18 22.63 25.01 3.64 114.23 16.63-82.65 80.38z"></path></svg></div></div></div><div class="css-8k16en"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" focusable="false" class="chakra-icon css-1bz9hb3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M464 256h-80v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8c-88.4 0-160 71.6-160 160v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zm-288 0H96v-64c0-35.3 28.7-64 64-64h8c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24h-8C71.6 32 0 103.6 0 192v240c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48z"></path></svg><div class="css-rjoygm"><p class="chakra-text css-fil0ch">&quot;<!-- -->我们的研究团队使用AI图形生成器为科学出版物创建可视化。图形的质量和准确性令人印象深刻，它们节省了我们无数小时，现在我们可以将这些时间用于实际研究而不是创建图表。这是任何研究小组必备的工具。<!-- -->&quot;</p></div><div spacing="[object Object]" class="css-492x6i"><div class="chakra-stack css-6yvt4u"><style data-emotion="css 10hf0rt">.css-10hf0rt{border-radius:var(--chakra-radii-full);display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;text-align:center;text-transform:uppercase;font-weight:var(--chakra-fontWeights-medium);position:relative;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;background:var(--avatar-bg);font-size:var(--avatar-font-size);color:var(--chakra-colors-gray-800);border-color:var(--avatar-border-color);vertical-align:top;width:var(--avatar-size);height:var(--avatar-size);--avatar-border-color:var(--chakra-colors-white);box-shadow:var(--chakra-shadows-sm);}.css-10hf0rt:not([data-loaded]){--avatar-bg:#f47054;}.chakra-ui-dark .css-10hf0rt:not([data-theme]),[data-theme=dark] .css-10hf0rt:not([data-theme]),.css-10hf0rt[data-theme=dark]{--avatar-border-color:var(--chakra-colors-gray-800);}@media screen and (min-width: 0em) and (max-width: 47.98em){.css-10hf0rt{--avatar-size:2rem;--avatar-font-size:calc(2rem / 2.5);}}@media screen and (min-width: 48em){.css-10hf0rt{--avatar-size:3rem;--avatar-font-size:calc(3rem / 2.5);}}</style><span class="chakra-avatar css-10hf0rt"><div role="img" aria-label="Dr. James Wilson" class="chakra-avatar__initials css-1ebyn6">DW</div></span><div class="css-sz3opf"><p class="chakra-text css-18398hb">Dr. James Wilson</p><p class="chakra-text css-cmmax5">研究主管</p><p class="chakra-text css-mfd9ip">National Research Institute</p></div></div><div class="chakra-stack css-jmwje4"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 576 512" focusable="false" class="chakra-icon css-14b709o" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"></path></svg></div></div></div></div></div></div></div><div class="css-sv29pi"><div class="chakra-stack container css-1vr2anf"><h2 class="chakra-heading css-3bip5u">如何使用 AI Infographic Creator？</h2><style data-emotion="css wt9ys5">.css-wt9ys5{font-size:var(--chakra-fontSizes-md);color:var(--chakra-colors-gray-600);text-align:center;max-width:var(--chakra-sizes-2xl);margin-bottom:var(--chakra-space-12);}</style><p class="chakra-text css-wt9ys5">只需几个简单步骤，将信息转化为精美的可视化图表</p><style data-emotion="css 14dm56q">.css-14dm56q{width:100%;max-width:var(--chakra-sizes-4xl);-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:relative;-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}</style><div class="css-14dm56q"><style data-emotion="css 197kuol">.css-197kuol{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;position:relative;width:100%;margin-bottom:var(--chakra-space-8);}</style><div class="css-197kuol"><style data-emotion="css oeincs">.css-oeincs{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;min-width:60px;height:60px;background:var(--chakra-colors-purple-100);color:var(--chakra-colors-purple-700);border-radius:var(--chakra-radii-full);font-size:var(--chakra-fontSizes-xl);font-weight:var(--chakra-fontWeights-bold);z-index:1;box-shadow:var(--chakra-shadows-md);}</style><div class="css-oeincs">1</div><style data-emotion="css 19xdms5">.css-19xdms5{-webkit-flex:1;-ms-flex:1;flex:1;margin-left:0px;margin-top:var(--chakra-space-4);padding:var(--chakra-space-6);background:var(--chakra-colors-white);border-radius:var(--chakra-radii-xl);box-shadow:var(--chakra-shadows-md);border-width:1px;position:relative;}</style><div class="css-19xdms5"><style data-emotion="css c9kt49">.css-c9kt49{font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-lg);margin-bottom:var(--chakra-space-2);}</style><p class="chakra-text css-c9kt49">输入内容</p><p class="chakra-text css-jneyc">在生成界面输入主题或粘贴文本，快速生成信息图</p></div></div><div class="css-197kuol"><div class="css-oeincs">2</div><div class="css-19xdms5"><p class="chakra-text css-c9kt49">自定义设置</p><p class="chakra-text css-jneyc">根据需要调整生成语言（默认英文）及图表风格等选项</p></div></div><div class="css-197kuol"><div class="css-oeincs">3</div><div class="css-19xdms5"><p class="chakra-text css-c9kt49">一键生成信息图</p><p class="chakra-text css-jneyc">点击生成按钮，AI 将自动为你创建富有洞见的信息图</p></div></div><div class="css-197kuol"><div class="css-oeincs">4</div><div class="css-19xdms5"><p class="chakra-text css-c9kt49">下载或分享</p><p class="chakra-text css-jneyc">点击下载保存信息图，或使用分享功能快速分发给他人</p></div></div></div><style data-emotion="css 1knpngl">.css-1knpngl{margin-top:var(--chakra-space-12);padding:var(--chakra-space-6);background:var(--chakra-colors-yellow-50);border-radius:var(--chakra-radii-xl);width:100%;max-width:var(--chakra-sizes-3xl);border-width:1px;border-color:var(--chakra-colors-blue-200);}</style><div class="css-1knpngl"><div class="chakra-stack css-1cggwyz"><div class="chakra-stack css-1igwmid"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 352 512" focusable="false" class="chakra-icon css-ntclmb" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M176 80c-52.94 0-96 43.06-96 96 0 8.84 7.16 16 16 16s16-7.16 16-16c0-35.3 28.72-64 64-64 8.84 0 16-7.16 16-16s-7.16-16-16-16zM96.06 459.17c0 3.15.93 6.22 2.68 8.84l24.51 36.84c2.97 4.46 7.97 7.14 13.32 7.14h78.85c5.36 0 10.36-2.68 13.32-7.14l24.51-36.84c1.74-2.62 2.67-5.7 2.68-8.84l.05-43.18H96.02l.04 43.18zM176 0C73.72 0 0 82.97 0 176c0 44.37 16.45 84.85 43.56 115.78 16.64 18.99 42.74 58.8 52.42 92.16v.06h48v-.12c-.01-4.77-.72-9.51-2.15-14.07-5.59-17.81-22.82-64.77-62.17-109.67-20.54-23.43-31.52-53.15-31.61-84.14-.2-73.64 59.67-128 127.95-128 70.58 0 128 57.42 128 128 0 30.97-11.24 60.85-31.65 84.14-39.11 44.61-56.42 91.47-62.1 109.46a47.507 47.507 0 0 0-2.22 14.3v.1h48v-.05c9.68-33.37 35.78-73.18 52.42-92.16C335.55 260.85 352 220.37 352 176 352 78.8 273.2 0 176 0z"></path></svg><h2 class="chakra-heading css-dt342z">制作优质信息图的小贴士</h2></div><ul role="list" class="css-1xa84ef"><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">聚焦清晰简洁的信息，提升视觉表达效果</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">尝试不同的视觉风格，找到最适合展现数据的方式</p></li><li class="css-70qvj9"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" focusable="false" class="chakra-icon css-169zzeb" role="presentation" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><p class="chakra-text css-0">对于复杂主题，可以拆分成多个信息图，分别呈现不同方面</p></li></ul></div></div></div></div><div class="css-v3gv3t"><div class="chakra-stack container css-1vr2anf"><style data-emotion="css wxc8ok">.css-wxc8ok{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-2xl);line-height:1.33;margin-bottom:var(--chakra-space-4);text-align:center;}@media screen and (min-width: 48em){.css-wxc8ok{font-size:var(--chakra-fontSizes-3xl);line-height:1.2;}}</style><h2 class="chakra-heading css-wxc8ok">常见问题</h2><style data-emotion="css qjedf">.css-qjedf{width:100%;max-width:var(--chakra-sizes-3xl);}</style><div class="chakra-accordion css-qjedf"><style data-emotion="css 17mg6aq">.css-17mg6aq{border-top-width:1px;border-color:inherit;overflow-anchor:none;}.css-17mg6aq:last-of-type{border-bottom-width:1px;}</style><div class="chakra-accordion__item css-17mg6aq"><style data-emotion="css uttm9k">.css-uttm9k{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;outline:2px solid transparent;outline-offset:2px;transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);font-size:var(--chakra-fontSizes-md);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-top:var(--chakra-space-2);padding-bottom:var(--chakra-space-2);}.css-uttm9k:focus-visible,.css-uttm9k[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-uttm9k:hover,.css-uttm9k[data-hover]{background:var(--chakra-colors-blackAlpha-50);}.css-uttm9k:disabled,.css-uttm9k[disabled],.css-uttm9k[aria-disabled=true],.css-uttm9k[data-disabled]{opacity:0.4;cursor:not-allowed;}</style><button type="button" id="accordion-button-:Rban9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rban9355cbq6:" class="chakra-accordion__button css-uttm9k"><style data-emotion="css 1eziwv">.css-1eziwv{-webkit-flex:1;-ms-flex:1;flex:1;text-align:left;}</style><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">什么是 FunBlocks AI Graphics？</p></div><style data-emotion="css 186l2rg">.css-186l2rg{width:1em;height:1em;display:inline-block;line-height:1em;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;color:currentColor;opacity:1;-webkit-transition:-webkit-transform 0.2s;transition:transform 0.2s;transform-origin:center;font-size:1.25em;vertical-align:middle;}</style><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><style data-emotion="css 1hb5ode">.css-1hb5ode{padding-top:var(--chakra-space-2);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);padding-bottom:var(--chakra-space-4);}</style><div role="region" id="accordion-panel-:Rban9355cbq6:" aria-labelledby="accordion-button-:Rban9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">FunBlocks AI Graphics是一款利用大语言模型（LLM）技术生成有趣且富有洞见的 SVG 信息图卡的工具。它可以帮助用户创建适合社交媒体、演示或个人笔记的可分享内容。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:Rjan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rjan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">FunBlocks AI Graphics能做什么？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:Rjan9355cbq6:" aria-labelledby="accordion-button-:Rjan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">它可以生成适用于社交媒体、演示、个人笔记，甚至仅为乐趣而制作的有趣且富有洞见的 SVG 信息图卡。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:Rran9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rran9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">使用这个工具需要设计技能吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:Rran9355cbq6:" aria-labelledby="accordion-button-:Rran9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">完全不需要！我们的 AI 会负责设计部分。你只需描述你想要的内容，AI 就会为你生成专业的图卡。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R13an9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R13an9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">我可以制作哪些类型的图形？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R13an9355cbq6:" aria-labelledby="accordion-button-:R13an9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">你可以制作多种类型的可视化内容，包括信息图、流程图、步骤图、数据图表、时间线、对比表格等。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1ban9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1ban9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">图表的数据要怎么输入？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1ban9355cbq6:" aria-labelledby="accordion-button-:R1ban9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">你可以直接在提示中输入或粘贴你的数据。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1jan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1jan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">我可以保存我生成的图卡吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1jan9355cbq6:" aria-labelledby="accordion-button-:R1jan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">可以，所有你创建的内容都会保存在你的账户中，随时访问、下载和分享。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1ran9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1ran9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">图卡的生成数量有限制吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1ran9355cbq6:" aria-labelledby="accordion-button-:R1ran9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">免费账户有每日生成限制，付费用户则拥有更高配额或无限制使用权限。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:Rdan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rdan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">为什么选择 SVG 信息图卡？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:Rdan9355cbq6:" aria-labelledby="accordion-button-:Rdan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">信息图卡易于理解、便于分享，极具启发性。SVG 格式灵活多变，而大语言模型擅长发散性思维，最终生成的图卡富有灵感，有助于学习、分享和激发创意。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:Rlan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rlan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">如何使用 AI 图卡？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:Rlan9355cbq6:" aria-labelledby="accordion-button-:Rlan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">使用非常简单：输入一个主题或问题，选择图卡类型，AI 就会为你生成内容。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:Rtan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:Rtan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">三种模式有什么区别？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:Rtan9355cbq6:" aria-labelledby="accordion-button-:Rtan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">三种模式的主要区别在于使用场景不同。

**聊天图卡模式** 主要用于对话式生成图卡，类似 ChatGPT 或 Claude，但最终以图卡形式呈现内容。

**图转图模式** 用于将已有的文字、文件或网页内容转化为信息图。

**图卡大师模式** 结合强大的提示词，将大模型的洞察力应用到各种场景，生成高度有趣且富有洞见的图卡。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R15an9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R15an9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">生成的内容可以编辑吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R15an9355cbq6:" aria-labelledby="accordion-button-:R15an9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">不能编辑，但你可以重新生成。这确保了所有内容均由 AI 自动生成。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1dan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1dan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">我可以分享我制作的图卡吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1dan9355cbq6:" aria-labelledby="accordion-button-:R1dan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">当然可以！你可以轻松分享生成的图卡。我们提供多种分享方式，支持社交媒体平台或直接分享链接。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1lan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1lan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">AI 图卡生成的内容是真的吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1lan9355cbq6:" aria-labelledby="accordion-button-:R1lan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">AI 图卡基于大语言模型生成内容，虽然不能保证完全真实，但具备一定参考价值。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R1tan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R1tan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">AI 图卡生成的内容具有原创性吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R1tan9355cbq6:" aria-labelledby="accordion-button-:R1tan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">内容基于大语言模型生成，虽然无法百分百保证原创性，但由于其生成方式，大多数内容可视为原创。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R25an9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R25an9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">ChatGPT、Claude、Gemini 等 AI 能生成类似图卡吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R25an9355cbq6:" aria-labelledby="accordion-button-:R25an9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">可以，这些 AI 工具也能生成类似图卡，但你需要手动编写提示词，甚至自己写代码来处理和渲染图形。而 FunBlocks AI Graphics是自动生成的。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R2dan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R2dan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">FunBlocks AI Graphics和 ChatGPT、Claude、Gemini 有什么不同？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R2dan9355cbq6:" aria-labelledby="accordion-button-:R2dan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">FunBlocks AI Graphics是自动生成图卡的工具。无需写提示词，也无需处理代码，只需享受生成过程即可。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R2lan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R2lan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">FunBlocks AI Graphics和 ChatGPT、Claude 有哪些共同点？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R2lan9355cbq6:" aria-labelledby="accordion-button-:R2lan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">它们都基于大语言模型（LLM）生成内容，能响应用户请求。但 FunBlocks AI Graphics以 SVG 图卡形式呈现内容，更直观、更具启发性，有助于学习、分享和激发创意，而 ChatGPT 主要输出文本，适用于更多类型的场景。</p></div></div></div><div class="chakra-accordion__item css-17mg6aq"><button type="button" id="accordion-button-:R2tan9355cbq6:" aria-expanded="false" aria-controls="accordion-panel-:R2tan9355cbq6:" class="chakra-accordion__button css-uttm9k"><div class="css-1eziwv"><p class="chakra-text css-1bsgmhw">使用 AI 图卡需要付费吗？</p></div><svg viewBox="0 0 24 24" focusable="false" class="chakra-icon chakra-accordion__icon css-186l2rg" aria-hidden="true"><path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg></button><div class="chakra-collapse" style="overflow:hidden;display:none;opacity:0;height:0px"><div role="region" id="accordion-panel-:R2tan9355cbq6:" aria-labelledby="accordion-button-:R2tan9355cbq6:" class="chakra-accordion__panel css-1hb5ode"><p class="chakra-text css-jneyc">我们提供免费和付费方案。你可以免费试用，也可以订阅付费计划以享受高级功能和更高配额。详情请参阅我们的价格页面。</p></div></div></div></div></div></div><style data-emotion="css 1o40fu3">.css-1o40fu3{width:100%;background-image:linear-gradient(to right, var(--chakra-colors-blue-500), var(--chakra-colors-purple-500));padding-top:var(--chakra-space-16);padding-bottom:var(--chakra-space-16);-webkit-padding-start:var(--chakra-space-4);padding-inline-start:var(--chakra-space-4);-webkit-padding-end:var(--chakra-space-4);padding-inline-end:var(--chakra-space-4);}</style><div class="css-1o40fu3"><style data-emotion="css 8jzuam">.css-8jzuam{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:var(--chakra-space-6);max-width:var(--chakra-sizes-3xl);-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;text-align:center;}</style><div class="chakra-stack css-8jzuam"><style data-emotion="css y5314g">.css-y5314g{font-family:var(--chakra-fonts-heading);font-weight:var(--chakra-fontWeights-bold);font-size:var(--chakra-fontSizes-3xl);line-height:1.33;color:var(--chakra-colors-white);}@media screen and (min-width: 48em){.css-y5314g{font-size:var(--chakra-fontSizes-4xl);line-height:1.2;}}</style><h2 class="chakra-heading css-y5314g">今天开始创建精美图形</h2><style data-emotion="css oelkby">.css-oelkby{color:var(--chakra-colors-whiteAlpha-900);font-size:var(--chakra-fontSizes-lg);}</style><p class="chakra-text css-oelkby">加入数千名使用我们的AI图形工具节省时间并创建精美视觉效果的专业人士。</p><style data-emotion="css 2eylfl">.css-2eylfl{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;white-space:nowrap;vertical-align:middle;outline:2px solid transparent;outline-offset:2px;line-height:1.2;border-radius:var(--chakra-radii-md);font-weight:var(--chakra-fontWeights-semibold);transition-property:var(--chakra-transition-property-common);transition-duration:var(--chakra-transition-duration-normal);height:var(--chakra-sizes-12);min-width:var(--chakra-sizes-12);font-size:var(--chakra-fontSizes-lg);-webkit-padding-start:var(--chakra-space-6);padding-inline-start:var(--chakra-space-6);-webkit-padding-end:var(--chakra-space-6);padding-inline-end:var(--chakra-space-6);background:var(--chakra-colors-whiteAlpha-500);color:var(--chakra-colors-white);}.css-2eylfl:focus-visible,.css-2eylfl[data-focus-visible]{box-shadow:var(--chakra-shadows-outline);}.css-2eylfl:disabled,.css-2eylfl[disabled],.css-2eylfl[aria-disabled=true],.css-2eylfl[data-disabled]{opacity:0.4;cursor:not-allowed;box-shadow:var(--chakra-shadows-none);}.css-2eylfl:hover,.css-2eylfl[data-hover]{background:var(--chakra-colors-whiteAlpha-600);}.css-2eylfl:hover:disabled,.css-2eylfl[data-hover]:disabled,.css-2eylfl:hover[disabled],.css-2eylfl[data-hover][disabled],.css-2eylfl:hover[aria-disabled=true],.css-2eylfl[data-hover][aria-disabled=true],.css-2eylfl:hover[data-disabled],.css-2eylfl[data-hover][data-disabled]{background:var(--chakra-colors-whiteAlpha-500);}.css-2eylfl:active,.css-2eylfl[data-active]{background:var(--chakra-colors-whiteAlpha-700);}</style><button type="button" class="chakra-button css-2eylfl">创建您的第一个图形<span class="chakra-button__icon css-1hzyiq5"><svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" aria-hidden="true" focusable="false" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M505.12019,19.09375c-1.18945-5.53125-6.65819-11-12.207-12.1875C460.716,0,435.507,0,410.40747,0,307.17523,0,245.26909,55.20312,199.05238,128H94.83772c-16.34763.01562-35.55658,11.875-42.88664,26.48438L2.51562,253.29688A28.4,28.4,0,0,0,0,264a24.00867,24.00867,0,0,0,24.00582,24H127.81618l-22.47457,22.46875c-11.36521,11.36133-12.99607,32.25781,0,45.25L156.24582,406.625c11.15623,11.1875,32.15619,13.15625,45.27726,0l22.47457-22.46875V488a24.00867,24.00867,0,0,0,24.00581,24,28.55934,28.55934,0,0,0,10.707-2.51562l98.72834-49.39063c14.62888-7.29687,26.50776-26.5,26.50776-42.85937V312.79688c72.59753-46.3125,128.03493-108.40626,128.03493-211.09376C512.07526,76.5,512.07526,51.29688,505.12019,19.09375ZM384.04033,168A40,40,0,1,1,424.05,128,40.02322,40.02322,0,0,1,384.04033,168Z"></path></svg></span></button></div></div></div><footer class="Footer_footer__GeF4n"><div class="container"><div class="Footer_footerContainer__SNoPe"><div class="Footer_footerLinks__ylCiD" style="margin-right:20px"><span class="footer-logo">FunBlocks</span><p data-i18n="footer.description" style="color:#bbb">An AI-powered platform for visualization-enhanced thinking and productivity.</p></div><div class="Footer_footerLinks__ylCiD"><h4 data-i18n="footer.product">FunBlocks AI Products</h4><ul style="margin-left:14px"><li><a href="/aiflow">FunBlocks AI Flow</a></li><li><a href="/aitools">FunBlocks AI Tools</a></li><li><a href="/welcome_extension">FunBlocks AI Extension</a></li><li><a href="/slides">FunBlocks AI Slides</a></li><li><a href="/aidocs">FunBlocks AI Docs</a></li></ul></div><div class="Footer_footerLinks__ylCiD"><h4 data-i18n="footer.resources">Resources</h4><ul style="margin-left:14px"><li><a href="/docs">FunBlocks AI Tutorials</a></li><li><a href="/blog">FunBlocks AI Blog</a></li><li><a href="https://app.funblocks.net/shares">FunBlocks AI Generated Content</a></li><li><a href="https://www.funblocks.net/aitools/collections/Reading">Classic Book Mindmaps</a></li><li><a href="https://www.funblocks.net/aitools/collections/Movie">Classic Movie Mindmaps</a></li><li><a href="/thinking-matters/behind-aiflow">Thinking Matters</a></li><li><a href="/thinking-matters/category/classic-mental-models">Mental Models</a></li></ul></div><div class="Footer_footerLinks__ylCiD"><h4 data-i18n="footer.company">Company</h4><ul style="margin-left:14px"><li><a href="https://discord.gg/XtdZFBy4uR" target="_blank">Contact Us</a></li></ul></div></div><div class="Footer_footerContainer__SNoPe"><div class="Footer_footerLinks__ylCiD"><h4 data-i18n="footer.resources">FunBlocks AI Tools</h4><div class="Footer_toolsGrid__YVg3f"><a href="https://www.funblocks.net/aitools/mindmap" target="_blank">AI Mindmap</a><a href="https://www.funblocks.net/aitools/slides" target="_blank">AI Slides</a><a href="https://www.funblocks.net/aitools/graphics" target="_blank">AI Graphics</a><a href="https://www.funblocks.net/aitools/brainstorming" target="_blank">AI Brainstorming</a><a href="https://www.funblocks.net/aitools/mindkit" target="_blank">AI MindKit</a><a href="https://www.funblocks.net/aitools/youtube" target="_blank">AI Youtube Summarizer</a><a href="https://www.funblocks.net/aitools/critical-thinking" target="_blank">AI Critical Analysis</a><a href="https://www.funblocks.net/aitools/refine-question" target="_blank">AI Question Craft</a><a href="https://www.funblocks.net/aitools/bias" target="_blank">AI LogicLens</a><a href="https://www.funblocks.net/aitools/reflection" target="_blank">AI Reflection</a><a href="https://www.funblocks.net/aitools/decision" target="_blank">AI Decision Analyzer</a><a href="https://www.funblocks.net/aitools/okr" target="_blank">AI OKR Assistant</a><a href="https://www.funblocks.net/aitools/startupmentor" target="_blank">AI Startup Mentor</a><a href="https://www.funblocks.net/aitools/businessmodel" target="_blank">AI Business Model Analyzer</a><a href="https://www.funblocks.net/aitools/planner" target="_blank">AI Task Planner</a><a href="https://www.funblocks.net/aitools/counselor" target="_blank">AI Counselor</a><a href="https://www.funblocks.net/aitools/dreamlens" target="_blank">AI DreamLens</a><a href="https://www.funblocks.net/aitools/horoscope" target="_blank">AI Horoscope</a><a href="https://www.funblocks.net/aitools/art" target="_blank">AI Art Insight</a><a href="https://www.funblocks.net/aitools/photo" target="_blank">AI Photo Coach</a><a href="https://www.funblocks.net/aitools/poetic" target="_blank">AI Poetic Lens</a><a href="https://www.funblocks.net/aitools/avatar" target="_blank">AI Avatar Studio</a><a href="https://www.funblocks.net/aitools/erase" target="_blank">AI Watermarks Remover</a><a href="https://www.funblocks.net/aitools/reading" target="_blank">AI Reading Map</a><a href="https://www.funblocks.net/aitools/movie" target="_blank">AI CineMap</a><a href="https://www.funblocks.net/aitools/feynman" target="_blank">AI Feynman</a><a href="https://www.funblocks.net/aitools/marzano" target="_blank">AI Marzano Taxonomy</a><a href="https://www.funblocks.net/aitools/bloom" target="_blank">AI Bloom Taxonomy</a><a href="https://www.funblocks.net/aitools/solo" target="_blank">AI SOLO Taxonomy</a><a href="https://www.funblocks.net/aitools/dok" target="_blank">AI DOK Taxonomy</a><a href="https://www.funblocks.net/aitools/layered-explanation" target="_blank">AI MindLadder</a><a href="https://www.funblocks.net/aitools/infographic" target="_blank">AI Infographic</a><a href="https://www.funblocks.net/aitools/insightcards" target="_blank">AI InsightCards</a><a href="https://www.funblocks.net/aitools/mindsnap" target="_blank">AI MindSnap</a><a href="https://www.funblocks.net/aitools/one-page-slide" target="_blank">AI SlideGenius</a></div></div></div><div class="Footer_copyright__fqH5S"><p data-i18n="footer.copyright">© 2025 FunBlocks AI. All rights reserved.</p></div></div></footer></div></div><span></span><span id="__chakra_env" hidden=""></span></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"_nextI18Next":{"initialI18nStore":{"zh":{"common":{"login":"登录","logout":"登出","pricing":"定价","why_funblocks_ai":"为什么选择 FunBlocks AI {{app}}?","try_now":"立即体验","generate_card":"AI 生成","english":"英语","chinese":"中文","load_more":"加载更多","delete":"删除","like":"喜欢","unlike":"取消喜欢","share_to_showcase":"分享到精彩卡片区，获得更多点赞，可获得相应免费生成次数","username_password_required":"用户名和密码不能为空","check_credentials":"请检查您的用户名和密码","username":"用户名","enter_username":"输入用户名","password":"密码","enter_password":"输入密码","logging_in":"登录中...","no_cards_yet":"您还没有创建任何卡片。","app_slogan_graphics":"智能视觉，轻松创造","app_one_sentence_graphics":"AI将文本和数据转换为引人注目的信息图，根据主题创作聪明的知识卡，无需设计技能。","app_slogan_mindmap":"化繁为简，AI思维导图让知识触手可及","app_one_sentence_mindmap":"让AI当你的知识向导，探索知识世界","app_slogan_brainstorming":"AI助力头脑风暴，激发无限创意","app_one_sentence_brainstorming":"让AI成为你的创意伙伴，突破思维局限，发现更多可能性","app_slogan_decision":"AI 决策分析助手","app_one_sentence_decision":"借助 AI 驱动的多维度分析，考虑多个角度、风险和结果，帮助您做出理性决策","app_slogan_planner":"AI 任务规划助手 - 化繁为简的智能规划工具","app_one_sentence_planner":"运用人工智能将复杂任务分解为可执行计划，通过智能任务管理提升您的工作效率","app_slogan_slides":"AI 驱动的专业演示文稿工具","app_one_sentence_slides":"一款智能演示文稿工具，帮助专业人士更高效地创建优质演示文稿。","app_slogan_youtube":"将YouTube视频转化为互动知识导图","app_one_sentence_youtube":"即时解锁YouTube视频中的知识。我们的AI工具将冗长的视频转化为简洁的互动摘要和知识导图。","app_slogan_dreamlens":"AI DreamLens","app_one_sentence_dreamlens":"通过AI解锁您梦境的隐藏意义","app_slogan_art":"AI 赋能，艺术瞬解","app_one_sentence_art":"你的 AI 艺术鉴赏助手","app_slogan_photo":"让每个镜头都成为成长的旅程","app_one_sentence_photo":"你的 AI 摄影导师，提供专业反馈，助你持续进步","app_slogan_reading":"读得更智慧，悟得更深刻","app_one_sentence_reading":"AI 思维导图，提升阅读体验","app_slogan_startupmentor":"初创企业的AI导师","app_one_sentence_startupmentor":"提升您的初创企业，加速您的成功。","app_slogan_businessmodel":"先进的AI驱动战略分析工具","app_one_sentence_businessmodel":"提供全面的商业模型分析和创新建议，瞬间完成。","app_slogan_okr":"精准目标，无限潜力","app_one_sentence_okr":"将愿望转化为成就","app_slogan_poetic":"让图片诉说诗意故事","app_one_sentence_poetic":"一款能将普通图片转化为富有文化韵味、充满智慧洞见的诗意评论的 AI 工具。","app_slogan_onepageslide":"AI SlideGenius - 秒级创建专业演示文稿","app_one_sentence_onepageslide":"利用AI的力量将您的文本内容转化为视觉震撼的幻灯片。","app_slogan_infographic":"AI驱动的信息图生成","app_one_sentence_infographic":"在几秒钟内将您的文本内容转化为美丽的信息图","app_slogan_mindkit":"思维模型与AI的结合","app_one_sentence_mindkit":"通过模块化思维框架解决复杂问题","app_slogan_mindsnap":"AI与思维模型相遇，可视化复杂想法","app_one_sentence_mindsnap":"在几秒钟内用清晰、结构化和富有洞察力的信息图探索您的主题或问题","ai_mindsnap_desc":"使用AI驱动的思维模型分析探索任何主题，并即时创建美丽的信息图","app_slogan_insightcards":"用AI驱动的洞见卡片转变您的思维","app_one_sentence_insightcards":"创建视觉吸引力强的洞见卡片，揭示任何主题的隐藏联系、矛盾和多种视角","ai_insightcards_desc":"生成视觉洞见卡片，通过顿悟洞见、悖论解读等框架提供独特视角","app_slogan_horoscope":"由AI解码您的命运","app_one_sentence_horoscope":"古老智慧与现代智能的结合 - 由先进AI驱动的个性化星象洞察","app_slogan_counselor":"与AI对话，遇见更好的自己","app_one_sentence_counselor":"专业的心理分析，温暖的倾听陪伴，帮助您探索内心、化解困扰","app_slogan_criticalthinking":"打造你的思维操作系统","app_one_sentence_criticalthinking":"AI赋能的批判性思维培养工具，助你建立专业的分析框架，获得清晰的决策思路","app_slogan_reflection":"超越初步思考","app_one_sentence_reflection":"通过一个挑战您思维的AI教练，培养更强的推理能力和更深的理解力","app_slogan_movie":"用 AI 思维导图，解构经典电影艺术","app_one_sentence_movie":"深入探索电影的每一个维度，发现隐藏的主题脉络，感受光影艺术的无限魅力","app_slogan_refinequestion":"更聪明地提问，深入思考","app_one_sentence_refinequestion":"利用AI驱动的批判性思维，提出精准而有力的问题，以解锁更深刻的见解和更好的解决方案","app_slogan_bias":"揭示论证背后的真相","app_one_sentence_bias":"利用AI分析认知偏见和逻辑谬误，培养更清晰的思维","to_original_page":"打开原网页","gender":"性别","male":"男","female":"女","horoscope_range":"运势范围","horoscope_today":"今日运势","horoscope_all":"全部运势","mindmap":"思维导图","graphics":"信息图","mine":"我的","my_generations":"我的作品","please_login_to_view_mine":"请登录以查看您的{{app}}","generate_new_card":"生成新卡片","please_select_card_type":"请选择卡片类型","please_enter_valid_message":"请输入有效的消息","please_select_language":"请选择语言","please_enter_valid_url":"请输入有效的网页链接","please_enter_valid_video_url":"请输入有效的 Youtube 视频链接","enter_webpage_url":"请输入或粘贴链接","output_language":"输出语言","card_generation_success":"生成成功","card_generation_error":"生成卡片时发生错误，请稍后再试","free_trial_exceeded":"您的免费试用次数已用完，请前往升级","daily_quota_exceeded":"您的每日生成次数已用完，请前往升","upgrade":"前往升级","generating":"生成中...","share_to_social_platforms":"分享到社交平台","title_and_link_copied":"标题和链接已复制","copy":"复制","copied":"已复制到剪贴板","copy_failed":"复制失败","save_image_and_paste_to_instagram":"请保存图片并粘贴标题和链接到Instagram上分享","share_to_twitter":"分享到 X/Twitter","share_to_facebook":"分享到 Facebook","share_to_linkedin":"分享到 LinkedIn","share_to_whatsapp":"分享到 WhatsApp","share_to_instagram":"分享到 Instagram","share_via_email":"通过邮件分享","copy_title_and_link":"复制标题和链接","download_image":"下载图片","email_verification_login":"邮箱验证码登录","please_complete_captcha":"请完成人机验证","please_enter_valid_email":"请输入有效的邮箱地址","failed_to_send_verification_code":"发送验证码失败","verification_failed":"验证失败","email_address":"邮箱地址","enter_your_email":"请输入您的邮箱地址","sending":"发送中...","send_verification_code":"发送验证码","verifying":"验证中...","verify_and_login":"验证并登录","select_card":"选择AI大师","select_graph_type":"图类型","error_loading_svg":"无法加载SVG内容","loading":"加载中...","fullscreen_display":"全屏显示","fullscreen_tooltip":"全屏显示","exit_fullscreen_tooltip":"退出全屏显示","download":"下载","share":"分享","save_svg":"保存SVG到本地","ai_insights":"FunBlocks AI Graphics","find_more_cards_or_create_your_own":"浏览更多或免费试用","close_modal":"关闭模态框","login_success":"登录成功","login_failed":"登录失败","login_error":"登录时发生错误","login_with_google":"使用 Google 登录","login_modal_instruction":"请在新标签页中完成登录，然后点击\"已完成登录\"进行确认。","open_login_page":"打开登录界面","completed_login":"已完成登录","checking":"检查中...","not_logged_in_yet":"尚未登录","please_complete_login_first":"请先完成登录流程","check_login_failed":"检查登录状态失败","login_detected":"检测到登录","login_successful_auto_close":"登录成功，弹窗将自动关闭","auto_check_login_status":"我们会自动检测您是否完成登录","example_wise_insight":"幸福的本质","example_fun_fact":"关于太空的趣事实","example_creative_idea":"应对气候变化的创新解决方案","given_text":"输入或粘贴文本...","examples":"示例","your_input":"您的输入...","your_question":"您的问题...","input_length_warning":"输入长度: {{current}}/{{max}}","provide_text_or_webpage":"提供文本或网页链接","url_content_fetch_failed":"获取网页内容失败","enter_your_topic":"请输入你的主题...","enter_your_ideas":"请输入你的想法...","please_enter":"请输入...","ai_actions":"AI 操作","fix_codes_bug":"修复代码问题","improve_codes":"继续优化","user_input":"用户输入","confirm":"确认","cancel":"取消","please_enter_your_requirements":"请输入您的修改要求...","input_hint":"提示：按 Ctrl+Enter 快速确认，按 Esc 取消","ai_action_success":"AI 操作完成","ai_action_error":"AI 操作失败，请重试","fun_and_informative":"有趣、有料，不同凡想","explore_ai_thoughts_and_humor":"探索AI的深度思考与幽默感，让FunBlocks AI Graphics成为你的创意助手。","generate_insightful_and_fun_cards":"生成富有洞察力和趣味性的信息图和卡片，轻松获得新视角，在社交中脱颖而出。","deep_informative_content":"深度、有料、不同凡想的内容","funblocks_ai_insights_unique":"FunBlocks AI Graphics 不是一个普通的内容生成工具。它能够创造出真正有深度、有价值、与众不同的内容：","why_choose_funblocks_ai_insights":"为什么选择 FunBlocks AI Graphics？","funblocks_ai_insights_combines_llm_and_card_features":"FunBlocks AI Graphics 结合了LLM的强大知识与推理能力，以及卡片的易读性和分享特性。无论是用于社交媒体、演讲还是个人笔记，我们都能为您提供独特的视角和有价值的内容。让AI成为你的思考伙伴，一起探索创意的无限可能！","fun_social_tool":"有趣、新颖、激发思维的社交利器","funblocks_ai_insights_thought_stimulator":"FunBlocks AI Graphics 不仅仅是一个内容生成工具，它是你思维的催化剂，社交的助推器：","faq":"常见问题","one_click_joy":"一键生成的快乐","experience_ai_insights_now":"立即体验AI洞察的魔力，让创意和欢乐触手可及！","start_generating":"开始生成","graphics_feature_1_text":"根据您提供的主题生成个性化洞察。","graphics_feature_2_text":"只需点击一下，即可创建精彩的、可分享的卡片。","graphics_feature_3_text":"通过 AI 驱动的内容探索新想法和观点。","meta":{"title":"FunBlocks AI洞察 - AI生成的趣味洞察卡片","description":"使用FunBlocks AI洞察生成有趣而富有见地的AI卡片。探索、创建和分享独特的AI生成内容。","keywords":"AI, 洞察, 卡片生成器, FunBlocks, 人工智能, 创意内容, 数据可视化, AI 生成卡片, 趣味洞察, 社交分享, AI 头脑风暴, AI 创意助手","og_title":"FunBlocks AI洞察 - 创建独特的AI生成卡片","og_description":"探索FunBlocks AI洞察：生成、探索和分享AI驱动的卡片，融合创意与数据驱动的见解。"},"graph_chat":"图表对话","make_graph":"制作图表","card_guru":"洞见卡片","image_insights":"品物志","graph_chat_placeholder":"与AI对话生成信息图...","make_graph_placeholder":"输入文本、上传文件或提供网站URL...","generate":"生成","create_artwork":"创建艺术作品","three_generation_modes":"多种信息图可选","graph_chat_description":"体验信息获取的新效率，超越传统的文本对话，如ChatGPT，清晰而简单。","card_guru_description":"通过精心设计的提示，解锁更深层次的见解和享受，引导LLM以增强理解。","make_graph_description":"将文本或网页内容转换为视觉上引人注目的信息图，易于理解和分享。","image_insights_description":"为图片生成风趣、有哲理的评论小品文，以不同的视角看世界","explore_other_products":"探索其他 FunBlocks AI 产品","boost_productivity":"使用 AI 驱动的工具提升您的生产力，实现无缝的网络交互。","unleash_creativity":"通过 AI 驱动的头脑风暴和思维导图释放您的创造力。","create_stunning_presentations":"使用 AI 驱动的幻灯片生成轻松创建令人惊叹的演示文稿。","transform_workflow":"使用全方位的 AI 助手改变您的工作流程，增强创造力和效率。","contact_us":"联系我们","image":"图片","art":"经典作品","upload_image":"上传图片","choose_image":"选择图片","uploaded_image":"已上传图片","image_url":"图片地址","paste_image_url":"在此粘贴图片地址","or":"或","please_upload_or_provide_image_url":"请上传图片或提供图片地址","upload_failed":"图片上传失败，请重试。","image_load_error":"图片加载失败","wonderlens_tip":"根据图片生成趣味评论小品","zoom_in":"放大","zoom_out":"缩小","book":"书籍","movie":"电影","video":"Youtube 视频","link":"网页","topic":"主题","doc":"文档","others":"其他","book_name":"书名（可包含作者等信息）","movie_name":"电影名","video_url":"Youtube 视频 Url 地址","webpage_url":"网址","to_aiflow":"深入探索","to_aiflow_tips":"进入 AIFlow 继续探索","deep_dive_to_topic":"生成对该主题的详细介绍","expand_ideas":"分解主题或生成更多想法","mental_model":"思维模型","educational_model":"教学模型","upgrade_to_vip":"升级为会员","upgrade_to_vip_msg":"您今天的免费 AI 生成次数已用完。请升级为 FunBlocks AI 会员，以便无限制地使用所有 FunBlocks AI 产品和服务，继续享受我们的优质服务。","task_analysis":"任务分析","task_breakdown":"任务分解","task_priority":"调整任务优先级","thinking_model":"思维模型","swot_analysis":"SWOT分析","first_principle":"第一性原理","business_model_canvas":"商业模式画布","fivew1h_method":"5W1H方法","scamper_method":"SCAMPER方法","six_thinking_hats":"六顶思考帽","pdca":"PDCA循环","systems_thinking":"系统思考","dialectical_thinking":"辩证思维","probabilistic_thinking":"概率思维","steep_analysis":"STEEP分析","five_forces":"五力模型分析","four_p":"4P营销组合","triz":"发明问题解决理论","rephrazing":"问题重新表述","learning_pyramid":"学习金字塔","kwl":"KWL图","changing_perspectives":"改变视角","reverse_thinking":"反向思维","role_playing":"角色扮演","mckinsey_7S_framework":"麦肯锡7S模型","value_proposition_canvas":"价值主张画布","pros_cons":"利弊分析","decision_tree":"决策树","decision_matrix":"决策矩阵","cost_benefit_analysis":"成本收益分析","casual_chain":"因果链","second_order_thinking":"二阶思维","inversion_thinking":"逆向思维","scientific_method":"科学方法","pyramid_principle":"金字塔原理","occams_razor":"奥卡姆剃刀","mece_principle":"MECE 原则","eisenhower_matrix":"艾森豪威尔矩阵","bloom":"布鲁姆","marzano":"马扎诺","addie":"ADDIE","5e":"5E教学法","gagne":"加涅","constructivist":"建构主义","auto":"智能选择","other":"其他","enter_mental_model":"输入模型名","select_none":"无","birthdate":"出生日期","platform_title":"AI驱动的创造力和生产力工具","platform_description":"通过可视化界面与AI互动——超越文本聊天框。","platform_description_1":"探索与AI互动的新方式，超越文本对话。","explore_tools":"探索AI工具","learn_more":"了解更多","platform_meta_title":"AI驱动的创造力和生产力工具 | AI思维导图、信息图、幻灯片等 | FunBlocks AIFlow","platform_meta_description":"探索FunBlocks AI的智能工具套件：AI图形、思维导图生成器、头脑风暴助手、演示文稿创建器、决策分析器和任务规划器。通过我们的可视化AI工具提升您的生产力和创造力。","ai_graphics_desc":"创建引人入胜的信息图和卡片，提供独特的AI驱动见解。","ai_mindmap_desc":"轻松从任何来源创建详细的思维导图，包括书籍、电影、视频和文档，或探索单一主题。","ai_brainstorming_desc":"与AI合作激发创意想法和解决方案，适合工作和学习。","ai_slides_desc":"只需一键即可轻松创建引人入胜的演示文稿。","ai_decision_desc":"获取理性分析和见解，以便做出更好的决策。","ai_planner_desc":"将复杂任务分解为可管理的步骤。","ai_youtube_desc":"总结YouTube视频并转化为互动知识地图。","ai_dreamlens_desc":"个性化梦境分析，提供多角度心理见解，帮助您解锁潜意识。","ai_art_desc":"即时生成专业的艺术欣赏思维导图，提供专家分析和更深的理解。","ai_photo_desc":"AI摄影导师，提供即时专业反馈和成长。","ai_reading_desc":"从书籍生成结构化思维导图，实现高效理解和知识内化。","ai_okr_desc":"专业的目标管理和战略规划工具，将宏伟愿景转化为具体、可衡量的战略目标。","ai_startupmentor_desc":"通过AI驱动的创业导师服务，转变您的创业旅程。获得即时的专家级分析和战略指导，助力您的商业冒险。","ai_businessmodel_desc":"提供全面的商业模型分析和创新建议，瞬间完成。","ai_poetic_desc":"将普通图片转化为富有文化韵味、充满智慧洞见的诗意评论。","ai_onepageslide_desc":"利用AI的力量将您的文本内容转化为视觉震撼的幻灯片。","ai_infographic_desc":"利用AI将您的文本内容转化为美丽的信息图","ai_mindkit_desc":"利用AI驱动的思维模型解决复杂问题，如SWOT分析、5W1H方法、SCAMPER方法等。","ai_horoscope_desc":"运势分析，由先进AI驱动的个性化星象洞察","ai_counselor_desc":"专业的心理分析，温暖的倾听陪伴，帮助您探索内心、化解困扰","ai_criticalthinking_desc":"AI赋能的批判性思维培养工具，助你建立专业的分析框架，获得清晰的决策思路","ai_reflection_desc":"通过一个挑战您思维的AI教练，培养更强的推理能力和更深的理解力","ai_bias_desc":"利用AI分析认知偏见和逻辑谬误，培养更清晰的思维","ai_movie_desc":"用 AI 思维导图，解构经典电影艺术","ai_refinequestion_desc":"利用AI驱动的批判性思维，提出精准而有力的问题，以解锁更深刻的见解和更好的解决方案","why_funblocks":"为什么选择FunBlocks AI?","get_started":"开始使用","ai_powered_intelligence":"先进的AI智能","ai_powered_intelligence_desc":"我们的工具由尖端的大型语言模型驱动，提供深刻的见解、创造性的解决方案和智能自动化。体验真正理解并提升您工作的AI。","integrated_toolset":"综合工具集","integrated_toolset_desc":"我们的AI工具套件，从思维导图到演示文稿创建，旨在满足特定的上下文和需求，简化使用并提高准确性，同时可以继续在FunBlocks AI平台上进行持续探索和创作。","user_friendly":"创新的AI交互体验","user_friendly_desc":"我们的工具重新定义了与AI的交互体验，提供可视化界面和结构化表达，操作简便，符合大脑对信息的处理和探索需求。复杂的AI功能和Prompt被简化为一键操作，使得高级功能对每位用户都触手可及。","mental_models_toolify":"经典思维模型的工具化","mental_models_toolify_desc":"我们将经典思维模型与AI相结合，提供卓越的思维教练和指导，让这些积累的人类智慧以工具的形式惠及每一个人。","why_not_chatgpt":"为什么不直接用ChatGPT？","chatgpt_comparison_intro":"虽然ChatGPT功能强大，但FunBlocks AI工具具有独特优势，能让信息处理和学习更加高效：","comparison_visual_title":"可视化学习 vs 文本信息过载","chatgpt_text_heavy":"生成大量文本，阅读和处理耗时费力","funblocks_visual_friendly":"创建思维导图、信息图和幻灯片，让理解更快、记忆更持久","comparison_exploration_title":"自由探索 vs 线性限制","chatgpt_linear_chat":"线性对话界面限制了问题探索的广度","funblocks_multi_perspective":"白板和思维导图界面支持多维度思考和探索","comparison_guidance_title":"主动引导 vs 被动响应","chatgpt_passive_waiting":"被动等待用户提问，而用户常常难以准确表达问题","funblocks_proactive_guide":"主动生成探索路径，引导用户深入复杂主题","comparison_learning_title":"能力培养 vs 快速答案","chatgpt_answer_only":"仅提供答案，没有培养思维能力","funblocks_thinking_process":"与用户协作探索解决方案，同时培养分析能力","mindmaps":"思维导图","slides":"幻灯片","infographics":"信息图","ai_tools_heading":"FunBlocks AI 工具集","ai_tools_description":"思维的入口，灵感的起点，解决方案的捷径","thinking_benefits_title":"赋能思维提升","deep_thinking":"深度思考","deep_thinking_desc":"分析认知偏见和逻辑谬误\n优化问题，提升解决方案\n数据驱动的决策分析\n建立系统的分析框架\nAI辅导强化推理能力","enhanced_productivity":"效率提升","enhanced_productivity_desc":"一键生成专业演示文稿\n快速创建精美信息图表\nAI驱动的复杂主题思维导图\n自动化任务规划与分解","boosted_creativity":"创造力激发","boosted_creativity_desc":"AI增强头脑风暴会话\n融合经典思维模型\n无限画布激发创意\n交互式思维导图\nAI洞察助力创意碰撞","ai_feynman_desc":"通过AI驱动的费曼学习技巧，将困惑转化为清晰，轻松将任何主题分解为直观的思维导图","app_slogan_feynman":"与您的AI费曼导师一起掌握复杂主题","app_one_sentence_feynman":"通过AI驱动的费曼学习技巧，将困惑转化为清晰，轻松将任何主题分解为直观的思维导图","ai_lessonplans_desc":"基于权威教育框架，只需输入主题即可生成专业完整的教学方案，让AI成为您的教学设计专家","app_slogan_lessonplans":"智能教学设计，让每堂课都精彩","app_one_sentence_lessonplans":"基于权威教育框架，只需输入主题即可生成专业完整的教学方案，让AI成为您的教学设计专家","ai_teachingslides_desc":"让每位教师都能设计出教育学专家级的教学内容，专注教学艺术而非制作繁琐","app_slogan_teachingslides":"让AI为您打造专业教学幻灯片","app_one_sentence_teachingslides":"让每位教师都能设计出教育学专家级的教学内容，专注教学艺术而非制作繁琐","ai_dokassessment_desc":"提供专业级评估工具，包含认知深度分析、实施指导和质量标准—全程仅需几分钟","app_slogan_dokassessment":"AI驱动的专家级教育评估创建工具","app_one_sentence_dokassessment":"提供专业级评估工具，包含认知深度分析、实施指导和质量标准—全程仅需几分钟","ai_bloom_desc":"借助布鲁姆分类法与AI技术，一键将任何主题转化为专业教学思维导图","app_slogan_bloom":"教学设计，从未如此智能","app_one_sentence_bloom":"借助布鲁姆分类法与AI技术，一键将任何主题转化为专业教学思维导图","ai_solo_desc":"用SOLO分类法构建结构化学习，将主题分解为渐进式认知层次，实现高效教学与学习","app_slogan_solo":"用SOLO分类法构建结构化学习，转化任何主题","app_one_sentence_solo":"AI驱动的教育规划工具，将主题分解为渐进式认知层次，实现高效教学与学习","ai_dok_desc":"使用我们基于韦布深度知识（DOK）模型的工具，在几秒钟内生成全面的课程思维导图","app_slogan_dok":"AI DOKBrain：深入学习背后的智慧","app_one_sentence_dok":"从回忆到创造，设计跨越所有认知层次的完整学习旅程","ai_dok_assessment_desc":"基于韦伯DOK框架创建综合性教育评估，具备AI驱动的分析和专业品质","app_slogan_dok_assessment":"AI DOK评估：智能评估设计，专业教学成果","app_one_sentence_dok_assessment":"将任何教学主题转化为具有DOK认知递进的专业多层次评估","app_slogan_marzano":"基于马扎诺分类学的智能学习设计","app_one_sentence_marzano":"通过多个认知维度，将任何主题转化为全面的学习旅程","ai_marzano_desc":"使用马扎诺教育目标分类学将任何主题转化为全面的学习设计","ai_layeredexplanation_desc":"通过AI MindLadder的渐进式知识层次，从直观基础到深刻洞察，攀登任何概念","app_slogan_layeredexplanation":"AI MindLadder：攀登至完全理解","app_one_sentence_layeredexplanation":"通过我们的8层次学习系统，逐层攀登任何概念的知识阶梯","erase_watermark":"去除水印","edit_image":"编辑图片","edit_instructions":"编辑指令","image_edit_placeholder":"描述您想要对图片进行的更改（例如：'删除背景'、'让它更亮'、'将天空改为日落颜色'）","ai_erase_desc":"一键去水印，恢复您图像的真实美感","app_slogan_erase":"AI Erase - 即刻去除水印，保留图像之美","app_one_sentence_erase":"先进的人工智能技术，智能识别并去除水印，同时保持图像完整性","ai_avatar_desc":"瞬间将您的照片转换为多种艺术风格的个性化头像","app_slogan_avatar":"AI头像工作室 | 将您的照片转变为惊艳的数字艺术！","app_one_sentence_avatar":"瞬间将您的照片转换为高质量的个性化头像，风格多样！","ai_imageeditor_desc":"使用自然语言指令编辑图片 - 无需Photoshop技能","app_slogan_imageeditor":"AI图片编辑器 - 用自然语言转换图片","app_one_sentence_imageeditor":"使用简单的文字指令编辑照片和图片 - 只需描述您想要的更改，我们的AI将完成其余工作！","ai_sketch_desc":"将您的简单绘画转化为令人惊叹的艺术作品，AI驱动的风格增强","app_slogan_sketch":"AI Sketch - 将您的绘画变成艺术","app_one_sentence_sketch":"绘制简单草图，观看AI将其转化为多种艺术风格的专业艺术作品","llm_support_title":"支持所有主要的大型语言模型","llm_support_desc":"在一个地方访问所有行业领先的模型，以提升您的学习和工作效率。无需在不同平台之间切换或为每个模型购买会员。","target_audience_title":"FunBlocks AI工具适合谁？","target_audience_desc":"FunBlocks AI工具旨在帮助任何希望通过AI驱动的工具提升学习、思维和生产力的人。","target_audience_students":"学生","target_audience_students_desc":"非常适合希望改善学习方法、创建更好笔记和理解复杂主题的学生。","target_audience_professionals":"专业人士","target_audience_professionals_desc":"理想的选择，适合需要组织想法、创建演示文稿和增强决策过程的专业人士。","target_audience_creatives":"创意工作者","target_audience_creatives_desc":"非常适合希望探索创意、生成内容和可视化概念的创意专业人士。","use_cases_title":"实际应用","use_cases_desc":"发现FunBlocks AI如何通过这些实际示例改变您的工作和学习体验。","use_case_1_title":"学术卓越","use_case_1_desc":"为复杂学科创建全面的思维导图，生成学习指南，提升对困难概念的理解。","use_case_2_title":"商业创新","use_case_2_desc":"开发商业策略，创建专业演示文稿，并利用AI驱动的洞察分析市场趋势。","use_case_3_title":"创意项目","use_case_3_desc":"将您的想法转化为视觉故事，生成创意内容，并在AI的帮助下探索新视角。","use_case_4_title":"深入研究与分析","use_case_4_desc":"从多个角度进行全面研究，深入探讨主题和问题，以获得全面理解。","use_case_5_title":"个人成长与发展","use_case_5_desc":"使用视觉框架绘制您的学习旅程。通过AI增强的洞察和多角度界面培养批判性思维。","use_case_6_title":"团队协作","use_case_6_desc":"创建共享知识图，促进头脑风暴会议，并通过可视化思维工具改善团队沟通。","platform_faq_1_q":"FunBlocks AI与其他AI平台有什么不同？","platform_faq_1_a":"FunBlocks AI通过专注于可视化思维而非仅仅是文本而脱颖而出。虽然大多数AI平台生成以文本为主的输出，但我们将想法转化为视觉图、信息图和演示文稿。我们的平台结合了多种AI模型，为不同思维风格提供专业工具，并设计了用户友好的界面，以增强您的创造性和分析过程。","platform_faq_2_q":"使用FunBlocks AI需要技术背景吗？","platform_faq_2_a":"完全不需要！FunBlocks AI的设计考虑了简单性。我们的用户友好界面通过清晰的说明引导您使用每个工具。只需输入您的想法或问题，我们的AI将处理组织和可视化信息的复杂工作。无论您是技术爱好者还是AI工具的新手，您都会发现FunBlocks易于接近且直观。","platform_faq_3_q":"FunBlocks支持哪些AI模型？","platform_faq_3_a":"FunBlocks AI集成了所有领先的语言模型，包括GPT-4、Claude、Gemini和其他尖端AI系统。我们不断添加对新模型的支持。您可以根据特定需求和偏好轻松切换不同的模型，所有这些都通过一个统一的界面完成。","platform_faq_4_q":"我可以将FunBlocks AI用于教育目的吗？","platform_faq_4_a":"当然可以！教育是我们核心关注领域之一。FunBlocks AI提供了像BloomBrain、MarzanoBrain、SOLOBrain和DOKBrain这样的专业教育框架，这些框架与已建立的学习分类法相一致。这些工具帮助学生组织复杂主题，创建学习指南，并通过可视化学习加深理解。教师可以使用我们的平台创建引人入胜的教学材料并评估学生的理解。","platform_faq_5_q":"FunBlocks AI如何帮助商业和专业工作？","platform_faq_5_a":"FunBlocks AI通过像OKR助手（用于目标设定）、商业模型分析器（用于战略开发）和AI任务规划器（用于项目管理）等工具改变商业思维。该平台还擅长创建专业演示文稿、组织竞争研究、促进团队头脑风暴和可视化复杂商业概念。这些工具帮助专业人士做出更好的决策，更有效地沟通，并推动创新。","platform_faq_6_q":"使用FunBlocks AI时我的数据安全吗？","platform_faq_6_a":"我们非常重视数据安全。所有传输到我们平台的数据都使用行业标准协议进行加密。我们不会在提供服务所需的时间之外存储您的内容，也不会在未获得明确同意的情况下使用您的数据来训练我们的模型。我们的隐私政策清楚地概述了我们如何保护您的信息以及我们采取的确保机密性的措施。","platform_faq_7_q":"FunBlocks AI的定价模型是什么？","platform_faq_7_a":"FunBlocks AI提供灵活的分层定价结构，以满足不同需求。我们提供一个免费层，访问基本工具和有限使用，非常适合刚入门的个人。我们的高级计划包括对所有工具的无限访问、优先处理和高级功能。请访问我们的定价页面以获取当前计划和定价的详细信息。","platform_faq_8_q":"我可以分享和协作使用FunBlocks AI创建的项目吗？","platform_faq_8_a":"可以！协作是FunBlocks AI的一个关键特性。您可以轻松与团队成员或同事分享您的思维导图、信息图和演示文稿。您可以将项目导出为各种格式，以便于演示、报告或在其他软件中进一步编辑。","platform_faq_9_q":"FunBlocks AI多久添加新功能和工具？","platform_faq_9_a":"我们在不断发展！我们的开发团队定期发布新工具和改进。我们密切关注用户反馈和行业趋势，以优先考虑最有价值的补充。主要功能更新通常每月进行，而较小的改进和修复每周部署。订阅我们的新闻通讯以了解最新的增强和平台新增功能。","platform_faq_10_q":"FunBlocks AI能帮助创意项目和内容创作吗？","platform_faq_10_a":"当然可以！FunBlocks AI通过像AI头脑风暴、AI思维工具和AI诗意视角等工具在提升创造力方面表现出色。这些工具帮助生成独特的想法，探索不同的视角，并可视化创意概念。内容创作者使用我们的平台来规划内容策略、开发故事情节、创建视觉故事板和组织创意项目——所有这些都在先进AI模型的帮助下，增强而非取代人类创造力。","platform_faq_11_q":"AI工具和AIFlow是如何协同工作的？","platform_faq_11_a":"AIFlow是支持所有功能的主要平台，而AI工具是易于使用的应用程序，帮助您快速访问AIFlow中的功能。这些工具专为特定任务设计，使您能够轻松探索和创建。","platform_faq_12_q":"如果我在AI工具中找不到所需的工具怎么办？","platform_faq_12_a":"如果您在AI工具中找不到特定工具，不用担心！您仍然可以直接使用AIFlow来完成任务。虽然AI工具很方便，但可能无法满足所有需求，而AIFlow具备您所需的所有功能。","platform_faq_13_q":"FunBlocks AI与其他视觉思维工具相比如何？","platform_faq_13_a":"与需要手动创建的传统视觉思维工具不同，FunBlocks AI根据您的输入自动生成视觉内容。我们的AI驱动方法节省时间，提供更深入的见解，并提供可能不会立即显现的多种视角。我们将AI语言模型的优势与视觉思维原则相结合，创造了一种独特的工具，可以增强创造力和分析思维。","platform_faq_14_q":"FunBlocks AI能帮助学习复杂主题吗？","platform_faq_14_a":"当然可以！FunBlocks AI擅长将复杂主题分解为可理解的视觉格式。我们的教育工具如MindLadder、BloomBrain和SOLOBrain专门设计用于根据已建立的教育框架组织信息，促进学习。这些工具帮助您可视化概念之间的联系，识别知识差距，并建立对任何主题的全面理解。","platform_faq_15_q":"FunBlocks AI如何确保生成内容的质量？","platform_faq_15_a":"我们采用多种策略来确保高质量的输出。首先，我们只使用最先进的AI模型。其次，我们开发了专门的提示技术，引导这些模型生成结构良好、准确且相关的内容。第三，我们的工具设计有特定框架，逻辑地组织信息。最后，我们根据用户反馈不断完善我们的系统，以随时间提高质量。","aiflow_and_aitools_title":"AI Tools 与 AIFlow 之间的关系是什么？","aitools_not_covered":"在 AI Tools 中找不到您需要的内容？没问题！直接使用 AIFlow。","try_aiflow_now":"立即尝试FunBlocks AIFlow","about_aiflow_title":"AIFlow：您的AI基础应用","about_aiflow_point1":"所有AI Tools的核心平台。","about_aiflow_point2":"一个灵活的用户界面，为您的创意提供无限的画布。","about_aiflow_point3":"直接访问您所需的一切。","about_aitools_title":"AI工具：易于使用的解决方案","about_aitools_point1":"现成的应用程序和演示，便于快速使用。","about_aitools_point2":"设计上注重易用性。","about_aitools_point3":"针对特定场景量身定制的解决方案。","continue_exploring_ai_content":"AI 内容生成后的探索之旅","what_after_generation":"AI 生成内容后，下一步是什么？","ai_tools_generation_explanation":"使用 AI Tools 一键生成内容后，您的探索之旅并不需要在此终止。","continue_with_aiflow":"使用 AIFlow 继续您的探索旅程","click_continue_to_explore":"点击\"Keep exploring with AIFlow\"按钮","access_funblocks_aiflow":"进入 FunBlocks AIFlow 界面","deep_dive_capabilities":"深入探索更多高级功能和可能性","hero_badge_1":"AI驱动的可视化工具","hero_badge_2":"创造力与生产力","hero_heading_1":"转变","hero_heading_2":"您的思维方式，通过","hero_heading_3":"可视化AI工具","hero_description":"通过AI驱动的可视化思维，将复杂概念可视化，增强学习效果，提高生产力。","discover_categories":"探索我们的AI工具类别","stat_tools":"AI工具","stat_thinking":"更快思考","stat_assistance":"全天候AI助手","model_openai":"OpenAI GPT","model_openai_desc":"利用GPT系列的力量进行创意内容生成和复杂问题解决。","model_anthropic":"Anthropic Claude","model_anthropic_desc":"使用Claude处理技术和科学内容，进行细致理解、推理和卓越编码能力。","model_google":"Google Gemini","model_google_desc":"访问Google的多模态AI能力，实现文本、图像和代码的综合理解。","model_deepseek":"Deepseek AI","model_deepseek_desc":"专门模型用于细致理解、推理和详细解释，尽量减少幻觉。","model_mistral":"Mistral AI","model_mistral_desc":"高效模型在日常任务和专业领域之间平衡性能和速度。","model_cohere":"Cohere","model_cohere_desc":"用于文本生成、摘要和语义搜索应用的高级模型。","infographic_howto_section_title":"如何使用 AI Infographic Creator？","infographic_howto_section_description":"只需几个简单步骤，将信息转化为精美的可视化图表","infographic_howto_step1_title":"输入内容","infographic_howto_step1_description":"在生成界面输入主题或粘贴文本，快速生成信息图","infographic_howto_step2_title":"自定义设置","infographic_howto_step2_description":"根据需要调整生成语言（默认英文）及图表风格等选项","infographic_howto_step3_title":"一键生成信息图","infographic_howto_step3_description":"点击生成按钮，AI 将自动为你创建富有洞见的信息图","infographic_howto_step4_title":"下载或分享","infographic_howto_step4_description":"点击下载保存信息图，或使用分享功能快速分发给他人","infographic_howto_tips_title":"制作优质信息图的小贴士","infographic_howto_tip_1":"聚焦清晰简洁的信息，提升视觉表达效果","infographic_howto_tip_2":"尝试不同的视觉风格，找到最适合展现数据的方式","infographic_howto_tip_3":"对于复杂主题，可以拆分成多个信息图，分别呈现不同方面","mindmap_howto_section_title":"如何使用 AI Mindmap Tool？","mindmap_howto_section_description":"轻松几步，制作信息丰富、视觉吸引的思维导图","mindmap_howto_step1_title":"输入主题","mindmap_howto_step1_description":"根据界面支持，输入主题、粘贴文本或提供网页、视频链接，开始绘制思维导图","mindmap_howto_step2_title":"自定义设置","mindmap_howto_step2_description":"可调整生成语言（默认英文）及思维模型，引导 AI 理解方向","mindmap_howto_step3_title":"一键生成思维导图","mindmap_howto_step3_description":"点击生成，AI 将自动扩展出多分支的完整思维导图","mindmap_howto_step4_title":"在 AIFlow 中探索","mindmap_howto_step4_description":"点击\"在 AIFlow 中探索\"，在 FunBlocks AIFlow 无限画布上继续编辑和深化你的导图","mindmap_howto_tips_title":"提升思维导图质量的小建议","mindmap_howto_tip_1":"聚焦具体细分主题，生成更有深度的导图","mindmap_howto_tip_2":"尝试不同设置（如思维模型），探索话题的多角度表达","mindmap_howto_tip_3":"对于复杂内容，建议拆分成多个导图，分别深入不同方面","slides_howto_section_title":"如何使用 FunBlocks AI Slides Creator？","slides_howto_section_description":"用简单步骤，让你的内容升级为引人入胜、视觉震撼的演示文稿","slides_howto_step1_title":"输入内容","slides_howto_step1_description":"在生成界面输入主题或粘贴文本，准备生成演示文稿","slides_howto_step2_title":"自定义设置","slides_howto_step2_description":"根据需要调整生成语言（默认英文）等选项","slides_howto_step3_title":"一键生成演示文稿","slides_howto_step3_description":"点击生成，AI 将自动为你构建完整的演示文稿","slides_howto_step4_title":"在线展示、分享或在 FunBlocks AI Slides 中继续编辑","slides_howto_step4_description":"生成后，你可以直接演示、在线分享，或使用 FunBlocks AI Slides 进一步个性化编辑","slides_howto_tips_title":"制作高效演示文稿的小建议","slides_howto_tip_1":"选择聚焦的主题或提供详细文本，生成更有价值的演示文稿","slides_howto_tip_2":"尝试不同设置，探索最佳表达方式","slides_howto_tip_3":"对于复杂话题，建议先用 AIFlow 思维导图发散，再生成演示文稿","photo_howto_section_title":"如何使用 FunBlocks AI Photo Tools？","photo_howto_section_description":"只需几步，通过 AI 个性化反馈提升你的摄影水平","photo_howto_step1_title":"上传照片","photo_howto_step1_description":"通过上传界面选择并上传想要获得反馈的照片","photo_howto_step2_title":"自定义设置","photo_howto_step2_description":"根据需要调整生成语言（默认英文）等选项","photo_howto_step3_title":"一键生成反馈","photo_howto_step3_description":"点击生成，AI 将基于你的照片生成详尽反馈或思维导图","photo_howto_step4_title":"在 AIFlow 中探索","photo_howto_step4_description":"点击\"在 AIFlow 中探索\"，在 FunBlocks AIFlow 无限画布上进一步延伸你的分析","photo_howto_tips_title":"获取更优质反馈的小贴士","photo_howto_tip_1":"上传高质量照片，获取更精准、细致的反馈","photo_howto_tip_2":"尝试不同类型的照片，了解自己多样化的摄影风格","photo_howto_tip_3":"保存反馈记录，持续追踪和提升摄影技能","video_howto_section_title":"如何使用 FunBlocks AI Video Summarization Tool？","video_howto_section_description":"只需几个简单步骤，轻松生成丰富的视频总结思维导图","video_howto_step1_title":"输入 YouTube 视频链接","video_howto_step1_description":"在生成界面粘贴你希望分析的 YouTube 视频链接","video_howto_step2_title":"自定义设置","video_howto_step2_description":"根据需要调整生成语言（默认英文）等选项","video_howto_step3_title":"一键生成思维导图","video_howto_step3_description":"点击生成，AI 将从视频内容中提取信息，扩展为完整的思维导图","video_howto_step4_title":"在 AIFlow 中探索","video_howto_step4_description":"点击\"在 AIFlow 中探索\"，在 FunBlocks AIFlow 无限画布上继续深化你的总结导图","mindsnap_howto_section_title":"如何使用 MindSnap？","mindsnap_howto_section_description":"通过这些简单步骤，使用思维模型将任何主题转化为视觉洞察","mindsnap_howto_step1_title":"输入您的主题","mindsnap_howto_step1_description":"输入任何您想通过思维模型分析的主题、问题或挑战","mindsnap_howto_step2_title":"选择思维模型（可选）","mindsnap_howto_step2_description":"选择一个特定的思维模型，或让 AI 为您的主题选择最合适的模型","mindsnap_howto_step3_title":"生成视觉洞察","mindsnap_howto_step3_description":"点击生成，观看 AI 通过选定的思维模型分析您的主题并创建精美的信息图","mindsnap_howto_step4_title":"下载或分享","mindsnap_howto_step4_description":"将您的信息图保存为 SVG 文件或直接与他人分享","mindsnap_howto_tips_title":"获得更好结果的小贴士","mindsnap_howto_tip_1":"具体描述您的主题，以获得更有针对性和有用的洞察","mindsnap_howto_tip_2":"尝试通过不同的思维模型分析同一主题，以获得多角度的视角","mindsnap_howto_tip_3":"对于复杂的主题，将其分解为更小、更集中的问题，以进行更深入的分析","insightcards_howto_section_title":"如何使用洞见卡片","insightcards_howto_section_description":"通过我们直观的流程，创建强大的洞见卡片非常简单：","insightcards_howto_step1_title":"输入您的主题","insightcards_howto_step1_description":"输入任何您想深入探索的概念、想法或问题。","insightcards_howto_step2_title":"选择卡片类型","insightcards_howto_step2_description":"根据您的学习目标从多种洞见框架中选择。","insightcards_howto_step3_title":"生成您的洞见卡片","insightcards_howto_step3_description":"我们的AI分析您的主题并创建视觉吸引力强的洞见卡片。","insightcards_howto_step4_title":"分享和学习","insightcards_howto_step4_description":"下载您的卡片，与他人分享，或保存到您的收藏中。","insightcards_howto_tips_title":"获得更好洞见卡片的小贴士","insightcards_howto_tip_1":"使用具体的主题以获得更有针对性和有意义的洞见","insightcards_howto_tip_2":"在同一主题上尝试不同的卡片类型，以获得多种视角","insightcards_howto_tip_3":"将生成的洞见作为深入探索和讨论的起点","app_slogan_promptoptimizer":"解锁AI潜力：优化您的提示词","app_one_sentence_promptoptimizer":"使用更清晰、更有效的提示词解锁AI的全部潜力。更快地获得更好的结果","ai_promptoptimizer_desc":"使用更清晰、更有效的提示解锁AI的全部潜力。更快地获得更好的结果","prompt-optimizer_howto_section_title":"轻松优化您的AI提示词","prompt-optimizer_howto_section_description":"了解如何通过这些简单的步骤来精炼您的AI互动：","prompt-optimizer_howto_step1_title":"输入初始提示词","prompt-optimizer_howto_step1_description":"从输入您想优化的原始提示或问题开始。这可以从简单的询问到复杂的指令。","prompt-optimizer_howto_step2_title":"选择语言","prompt-optimizer_howto_step2_description":"从我们提供的可用选项中选择优化后的提示语言。","prompt-optimizer_howto_step3_title":"获取优化后的提示","prompt-optimizer_howto_step3_description":"我们的AI提示优化器将分析您的输入，并为您生成一个精炼的提示。","prompt-optimizer_howto_step4_title":"测试并进一步精炼","prompt-optimizer_howto_step4_description":"使用您的AI系统测试优化后的提示，并根据结果进行必要的调整。","prompt-optimizer_howto_tips_title":"编写有效提示的提示","prompt-optimizer_howto_tip_1":"在初始提示中明确定义您所期望的结果或目标。","prompt-optimizer_howto_tip_2":"避免在初始提示中包含详细的步骤，以便充分发挥LLM的潜力。","prompt-optimizer_howto_tip_3":"使用生成的提示作为起点，并迭代以进行进一步的优化。","case_studies_badge":"实际应用案例","case_studies_title":"成功案例：{{appname}} 在实践中的应用","case_studies_cta_text":"准备好创造属于你的成功故事了吗？","case_studies_cta_button":"立即开始创作","testimonials_title":"用户反馈","testimonials_description":"加入数千名已经通过{{appname}}改变体验的用户。","testimonials_rating":"平均评分{{rating}}","testimonials_users":"{{users}}+活跃用户","comparison_winner":"最佳整体解决方案","comparison_value":"最佳性价比","case_study_1_title":"教育机构转型","case_study_1_industry":"高等教育","case_study_1_challenge":"一所领先的大学在帮助学生可视化跨学科的复杂概念方面遇到困难，导致理解力和记忆率较低。","case_study_1_solution":"在各部门实施了一整套FunBlocks AI工具：MarzanoBrain和BloomBrain用于创建结构化学习材料，Slides用于生成互动演示文稿，Brainstorming、Critical Thinking和Creative Thinking工具用于发展学生的认知能力。","case_study_1_results":"学生的理解力和记忆率显著提高。学生展现了更强的批判性思维能力，并能跨学科联系概念。教师报告课堂讨论更加生动，学生作品质量更高。","case_study_2_title":"创新思维提升","case_study_2_industry":"产品设计与营销","case_study_2_challenge":"一家跨国公司在激发产品设计和营销团队的创新思维方面遇到困难，导致解决方案缺乏创新，市场差异化下降。","case_study_2_solution":"将FunBlocks AI的Brainstorming、MindKit、MindSnap、OKR Assistant、Task Planner等工具整合到他们的创意过程中。团队利用这些工具探索多种视角，挑战假设，帮助可视化看似不相关的概念之间的联系。","case_study_2_results":"团队开发了更具创新性的产品设计和营销活动，赢得了客户的共鸣。公司报告创意产出增加，解决方案种类更多，跨团队合作更加顺畅。","case_studies_description":"查看不同行业的组织如何利用FunBlocks AI工具解决实际挑战并取得可衡量的成果。","research_title":"基于研究的方法","research_description":"我们的工具建立在坚实的科学基础和经过验证的认知原则上，旨在最大化学习和生产力。","research_area_1_title":"认知负荷理论","research_area_1_description":"我们的可视化工具通过空间组织信息来减少认知负荷，帮助用户更高效地处理复杂概念，同时最小化思维努力。","research_area_2_title":"视觉学习效能","research_area_2_description":"研究表明，与仅文字学习相比，视觉学习可以提高理解力高达400%，并使记忆率提高38%，从而使复杂信息更易于理解和记住。","research_area_3_title":"心理模型与框架","research_area_3_description":"我们的工具利用已建立的心理模型和教育框架，帮助结构化思维、提高问题解决能力，并增强跨学科的概念理解。","research_area_4_title":"AI增强学习","research_area_4_description":"研究表明，AI辅助的学习工具能够个性化教育体验，提供适应性反馈，并提高各类学习风格和环境中的学习成果。","testimonial_1_content":"MindLadder彻底改变了我学习医学院的方式。它将复杂的主题分解成渐进的层次，帮助我以教材无法做到的方式理解心血管生理。我将学习时间缩短了30%，同时提高了成绩！","testimonial_1_role":"医学生","testimonial_2_content":"作为一名产品经理，我需要将复杂的想法传达给不同的团队。AI信息图生成器已经成为我的秘密武器，帮助我在几分钟内创建出色的概念图和对比图，而不再需要数小时。","testimonial_2_role":"产品经理","testimonial_3_content":"我尝试过许多头脑风暴工具，但FunBlocks AI Brainstorming Assistant独树一帜。它不仅生成想法——它还帮助结构化并精炼这些想法，以我以前未曾考虑过的方式。这就像是24/7都能使用的创造性思维伙伴。","testimonial_3_role":"创意总监","common_questions":"常见问题","faq_description":"查找关于FunBlocks AI工具的常见问题解答，了解它们如何帮助您。","faq_category_general":"关于FunBlocks AI","faq_category_features":"工具和功能","faq_category_usage":"使用案例和应用","faq_category_technical":"技术和支持","faq_need_more_help":"需要更多帮助？","faq_support_text":"如果这里没有涵盖您的问题，我们的支持团队随时为您提供帮助。您也可以查看我们的详细文档以获取更多信息。","contact_support":"联系支持","view_documentation":"查看文档","llm_support_notes":"通过单一、统一的界面访问所有这些模型和更多模型。根据您的任务需求无缝切换模型。","category_mindmap_title":"思维导图","category_mindmap_desc":"AI 驱动的思维导图，用于学习、规划和创意发想","category_infographics_title":"信息图","category_infographics_desc":"借助 AI 将想法转化为精美的视觉内容","category_slides_title":"幻灯片","category_slides_desc":"使用 AI 秒级生成专业演示文稿","category_images_title":"图像","category_images_desc":"AI 图像生成与增强工具","examples_showcase_title":"AI 工具真实演示","examples_showcase_desc":"探索由我们的 AI 工具生成的真实示例作品","example_mindmap_title":"AI 思维导图示例","example_mindmap_desc":"使用 AI 思维导图工具可视化复杂主题。本示例为《了不起的盖茨比》的思维导图。","example_infographic_title":"AI 信息图示例","example_infographic_desc":"快速生成精美的信息图。本示例展示了一张由 InsightCards 生成的创意信息图。","example_slides_title":"AI 幻灯片示例","example_slides_desc":"几秒钟内生成专业演示文稿。本示例展示了一份竞品分析演示。","example_mindsnap_title":"AI MindSnap 示例","example_mindsnap_desc":"将主题转化为视觉化的思维模型。本示例展示了 SWOT 分析图。","lesson-plans_howto_section_title":"如何使用AI教学方案工具？","lesson-plans_howto_section_description":"通过这些简单步骤创建专业的、基于框架的教学方案","lesson-plans_howto_step1_title":"输入教学主题","lesson-plans_howto_step1_description":"输入您的课程主题、学科领域或学习目标，开始创建教学方案","lesson-plans_howto_step2_title":"选择教学框架","lesson-plans_howto_step2_description":"从经过验证的教学框架中选择，如布鲁姆分类法、马扎诺策略或ADDIE模型","lesson-plans_howto_step3_title":"自定义设置","lesson-plans_howto_step3_description":"调整教育层次、课程时长和您课堂环境的具体要求","lesson-plans_howto_step4_title":"生成和完善","lesson-plans_howto_step4_description":"获得包含目标、活动、评估和资源的全面教学方案，您可以进一步自定义","lesson-plans_howto_tips_title":"制作更好教学方案的小贴士","lesson-plans_howto_tip_1":"明确学习目标和目标受众，以获得更有针对性的教学方案","lesson-plans_howto_tip_2":"尝试不同的教学框架，找到最适合您内容的方法","lesson-plans_howto_tip_3":"在自定义生成的方案时，考虑您的课堂环境和可用资源","dok-assessment_howto_section_title":"如何使用FunBlocks AI评估","dok-assessment_howto_section_description":"只需几个简单步骤即可创建专业的基于DOK的评估。我们的AI处理复杂性，您专注于教学。","dok-assessment_howto_step1_title":"输入教学主题","dok-assessment_howto_step1_description":"输入任何您想要评估的学科、课程或学习目标。可以具体也可以概括。","dok-assessment_howto_step2_title":"AI分析内容","dok-assessment_howto_step2_description":"我们的AI识别关键概念，确定最优DOK分布，并规划全面的评估覆盖。","dok-assessment_howto_step3_title":"查看生成的评估","dok-assessment_howto_step3_description":"检查包含所有DOK层次题目、评分标准和实施指南的完整评估。","dok-assessment_howto_step4_title":"自定义和实施","dok-assessment_howto_step4_description":"修改题目，调整难度水平，并根据您的具体教学环境和标准调整评估。","dok-assessment_howto_tips_title":"更好评估的专业技巧","dok-assessment_howto_tip_1":"明确年级水平和学科领域以获得更有针对性的题目","dok-assessment_howto_tip_2":"指定评估类型（形成性/总结性）以获得适当的题目风格","dok-assessment_howto_tip_3":"包含学习目标以确保与您的课程完美对齐","example_try_tool":"试用此工具"},"graphics":{"hero_badge":"AI 图形生成器","hero_heading":"用人工智能将您的想法转化为精美图形","hero_description":"在几秒钟内创建令人惊叹的信息图表、流程图、数据图表和视觉内容 - 无需设计技能。","hero_start_button":"立即开始","hero_examples_button":"查看示例","hero_metric_1_number":"10倍","hero_metric_1_text":"比传统设计更快","hero_metric_2_number":"50+","hero_metric_2_text":"视觉风格和格式","hero_metric_3_number":"75%","hero_metric_3_text":"信息记忆能力提高","graphics_examples_title":"您可以创建什么","graphics_example_1_title":"数据可视化","graphics_example_1_description":"将复杂数据转化为直观、美观的图表，一目了然地讲述故事。","graphics_example_2_title":"流程示意图","graphics_example_2_description":"用清晰、引人入胜的视觉效果说明多步骤流程，提高理解度。","graphics_example_3_title":"概念流程图","graphics_example_3_description":"用专业外观的图表绘制关系、层次结构和决策流程。","graphics_example_4_title":"见解卡片","graphics_example_4_description":"用优雅的视觉效果呈现主题，突出核心见解。","examples_subtitle":"准备好创建您自己的精美图形了吗？","examples_button":"立即开始创建","graphics_intro_description":"FunBlocks AI图形将人工智能与设计原则相结合，帮助您在几分钟内（而不是几小时）创建专业质量的视觉内容。","comparison_title":"AI图形与传统设计比较","comparison_header_feature":"功能","comparison_header_traditional":"传统设计","comparison_header_ai":"AI图形","comparison_time":"创建时间","comparison_time_traditional":"数小时到数天","comparison_time_ai":"几分钟","comparison_skills":"所需技能","comparison_skills_traditional":"高级设计专业知识","comparison_skills_ai":"无需特殊技能","comparison_design":"设计质量","comparison_design_traditional":"根据技能水平而变","comparison_design_ai":"始终如一的专业水准","comparison_variety":"视觉多样性","comparison_variety_traditional":"受时间和专业知识限制","comparison_variety_ai":"无限变化","comparison_iteration":"迭代速度","comparison_iteration_traditional":"缓慢的手动过程","comparison_iteration_ai":"即时更新","graphics_benefits_title":"适合所有人的优势","graphics_benefits_pro_title":"对专业人士","graphics_benefit_pro_1":"节省日常设计任务的时间","graphics_benefit_pro_2":"创建一致的品牌视觉效果","graphics_benefit_pro_3":"生成多种设计变体","graphics_benefit_pro_4":"专注于策略，而非执行","graphics_benefits_business_title":"对企业","graphics_benefit_business_1":"提高演示质量","graphics_benefit_business_2":"增强受众参与度","graphics_benefit_business_3":"有效沟通复杂想法","graphics_benefit_business_4":"保持品牌一致性","graphics_use_cases_title":"热门使用场景","graphics_use_cases_description":"我们的AI图形工具足够灵活，可以处理跨行业和应用的各种可视化需求。","graphics_scenarios_title":"常见场景","graphics_scenario_1_title":"商业演示","graphics_scenario_1_text":"创建具有数据可视化的高影响力幻灯片，让您的观点更加明确。","graphics_scenario_2_title":"社交媒体内容","graphics_scenario_2_text":"设计可分享的信息图表，提高参与度并解释复杂主题。","graphics_scenario_3_title":"项目文档","graphics_scenario_3_text":"为团队开发清晰的流程图和文档可视化。","graphics_scenario_4_title":"教育材料","graphics_scenario_4_text":"构建学习辅助工具，使复杂概念更容易理解和记忆。","graphics_users_title":"谁在使用我们的工具","graphics_user_1_title":"营销人员与内容创作者","graphics_user_1_text":"为活动、社交媒体和博客内容创建引人入胜的视觉效果。","graphics_user_2_title":"商业专业人士","graphics_user_2_text":"用专业质量的图形增强演示和报告。","graphics_user_3_title":"教育工作者与培训师","graphics_user_3_text":"开发有影响力的视觉学习材料和课程内容。","graphics_user_4_title":"企业家与初创公司","graphics_user_4_text":"在没有设计团队的情况下，为推介和营销构建专业视觉效果。","cta_title":"今天开始创建精美图形","cta_description":"加入数千名使用我们的AI图形工具节省时间并创建精美视觉效果的专业人士。","cta_button":"创建您的第一个图形","graphics_faq_title":"常见问题","graphics_faq_1_q":"什么是 FunBlocks AI Graphics？","graphics_faq_1_a":"FunBlocks AI Graphics是一款利用大语言模型（LLM）技术生成有趣且富有洞见的 SVG 信息图卡的工具。它可以帮助用户创建适合社交媒体、演示或个人笔记的可分享内容。","graphics_faq_2_q":"FunBlocks AI Graphics能做什么？","graphics_faq_2_a":"它可以生成适用于社交媒体、演示、个人笔记，甚至仅为乐趣而制作的有趣且富有洞见的 SVG 信息图卡。","graphics_faq_3_q":"使用这个工具需要设计技能吗？","graphics_faq_3_a":"完全不需要！我们的 AI 会负责设计部分。你只需描述你想要的内容，AI 就会为你生成专业的图卡。","graphics_faq_4_q":"我可以制作哪些类型的图形？","graphics_faq_4_a":"你可以制作多种类型的可视化内容，包括信息图、流程图、步骤图、数据图表、时间线、对比表格等。","graphics_faq_5_q":"图表的数据要怎么输入？","graphics_faq_5_a":"你可以直接在提示中输入或粘贴你的数据。","graphics_faq_6_q":"我可以保存我生成的图卡吗？","graphics_faq_6_a":"可以，所有你创建的内容都会保存在你的账户中，随时访问、下载和分享。","graphics_faq_7_q":"图卡的生成数量有限制吗？","graphics_faq_7_a":"免费账户有每日生成限制，付费用户则拥有更高配额或无限制使用权限。","graphics_visual_types_title":"有洞见的信息图","graphics_visual_1_title":"聊天图卡","graphics_visual_2_title":"信息图","graphics_visual_3_title":"智慧图卡","graphics_visual_1_text":"体验前所未有的信息获取效率，比传统的文字对话更清晰简洁。","graphics_visual_2_text":"通过精心设计的提示词引导大模型，获得更深入的理解与乐趣。","graphics_visual_3_text":"深入分析给定主题，发现新的见解，并制作易于分享的有吸引力社交媒体卡片。","graphics_feature_1_title":"AI 洞见驱动","graphics_feature_1_text":"运用先进的大语言模型技术，深入分析主题，提供独特见解。","graphics_feature_2_title":"创意灵感加速器","graphics_feature_2_text":"突破思维局限，获得新颖视角，激发无限创意灵感。","graphics_feature_3_title":"幽默与智慧并存","graphics_feature_3_text":"深刻见解与幽默表达的完美结合，让思考充满乐趣。","graphics_feature_4_title":"趣味图卡，轻松分享","graphics_feature_4_text":"生成吸引眼球的内容图卡，在社交平台上轻松脱颖而出。","deep_informative_content":"深度、有料、非凡的内容","funblocks_ai_insights_unique":"FunBlocks AI Graphics不仅仅是普通的内容生成工具，它可以生成真正深刻、有价值、独特的内容：","deep_insights":"深刻洞见：AI 深度解析主题，带来独特观点","quality_content":"高质量内容：融合海量知识，输出优质内容","innovative_thinking":"创新思维：打破常规，激发新颖观点","humor_integration":"智慧幽默融合：巧妙结合幽默与智慧，让内容更有趣","fun_social_tool":"趣味、新颖、启发思考的社交工具","funblocks_ai_insights_thought_stimulator":"FunBlocks AI Graphics不仅是一个内容生成工具，更是你的思维催化器、社交助推器：","endless_fun":"乐趣无穷","endless_fun_desc":"每一次使用都是一次充满惊喜的探索，让思考变得有趣又好玩。","novel_format":"新颖简洁的形式","novel_format_desc":"精致的卡片格式，简明清晰却内容丰富，一眼就懂。","stimulate_thinking":"激发思维","stimulate_thinking_desc":"突破传统思维框架，开启创新视角，让你的想法更有色彩。","enhance_social_interaction":"增强社交互动","enhance_social_interaction_desc":"分享独特见解，成为朋友圈中的思维领袖，为社交互动增添深度。","why_infographic_cards":"为什么选择 SVG 信息图卡？","why_infographic_cards_answer":"信息图卡易于理解、便于分享，极具启发性。SVG 格式灵活多变，而大语言模型擅长发散性思维，最终生成的图卡富有灵感，有助于学习、分享和激发创意。","how_to_use_ai_insights":"如何使用 AI 图卡？","how_to_use_ai_insights_answer":"使用非常简单：输入一个主题或问题，选择图卡类型，AI 就会为你生成内容。","difference_of_three_modes":"三种模式有什么区别？","difference_of_three_modes_answer":"三种模式的主要区别在于使用场景不同。\n\n**聊天图卡模式** 主要用于对话式生成图卡，类似 ChatGPT 或 Claude，但最终以图卡形式呈现内容。\n\n**图转图模式** 用于将已有的文字、文件或网页内容转化为信息图。\n\n**图卡大师模式** 结合强大的提示词，将大模型的洞察力应用到各种场景，生成高度有趣且富有洞见的图卡。","can_content_be_edited":"生成的内容可以编辑吗？","can_content_be_edited_answer":"不能编辑，但你可以重新生成。这确保了所有内容均由 AI 自动生成。","can_i_share_cards":"我可以分享我制作的图卡吗？","can_i_share_cards_answer":"当然可以！你可以轻松分享生成的图卡。我们提供多种分享方式，支持社交媒体平台或直接分享链接。","is_content_true":"AI 图卡生成的内容是真的吗？","is_content_true_answer":"AI 图卡基于大语言模型生成内容，虽然不能保证完全真实，但具备一定参考价值。","is_content_original":"AI 图卡生成的内容具有原创性吗？","is_content_original_answer":"内容基于大语言模型生成，虽然无法百分百保证原创性，但由于其生成方式，大多数内容可视为原创。","can_other_ai_generate_cards":"ChatGPT、Claude、Gemini 等 AI 能生成类似图卡吗？","can_other_ai_generate_cards_answer":"可以，这些 AI 工具也能生成类似图卡，但你需要手动编写提示词，甚至自己写代码来处理和渲染图形。而 FunBlocks AI Graphics是自动生成的。","difference_from_other_ai":"FunBlocks AI Graphics和 ChatGPT、Claude、Gemini 有什么不同？","difference_from_other_ai_answer":"FunBlocks AI Graphics是自动生成图卡的工具。无需写提示词，也无需处理代码，只需享受生成过程即可。","in_common_as_chatgpt":"FunBlocks AI Graphics和 ChatGPT、Claude 有哪些共同点？","in_common_as_chatgpt_answer":"它们都基于大语言模型（LLM）生成内容，能响应用户请求。但 FunBlocks AI Graphics以 SVG 图卡形式呈现内容，更直观、更具启发性，有助于学习、分享和激发创意，而 ChatGPT 主要输出文本，适用于更多类型的场景。","is_ai_insights_free":"使用 AI 图卡需要付费吗？","is_ai_insights_free_answer":"我们提供免费和付费方案。你可以免费试用，也可以订阅付费计划以享受高级功能和更高配额。详情请参阅我们的价格页面。","case_studies_badge":"真实应用案例","case_studies_title":"成功案例：AI图形实战","case_studies_description":"了解不同行业的专业人士如何使用我们的AI图形生成器解决实际沟通挑战并取得可衡量的成果。","case_studies_cta_text":"准备创建您自己的成功案例？","case_studies_cta_button":"立即开始创建","case_study_1_title":"为高管报告进行数据可视化","case_study_1_industry":"商业智能","case_study_1_challenge":"一家财富500强公司需要将复杂的季度绩效数据转化为易于理解的视觉效果用于高管会议，但他们的设计团队已经被大量请求压得喘不过气来。","case_study_1_solution":"使用我们的AI图形生成器，他们只需几分钟就创建了专业的数据图表和信息图，以最少的努力突出关键指标和趋势。","case_study_1_results":"会议准备时间减少了60%，高管理解度提高了45%，设计团队得以专注于战略项目。","case_study_2_title":"教育概念可视化","case_study_2_industry":"教育","case_study_2_challenge":"一位大学教授在向本科生解释复杂的科学过程时遇到困难，发现传统教科书解释对视觉学习者来说不够充分。","case_study_2_solution":"使用我们的AI工具创建了一系列基于流程的信息图，将复杂概念以清晰、有序的步骤和视觉提示直观地分解。","case_study_2_results":"学生理解度提高了32%，考试成绩提高了27%，课程满意度评分从3.2/5上升到4.7/5。","case_study_3_title":"软件架构文档","case_study_3_industry":"软件开发","case_study_3_challenge":"一家科技初创公司需要为新开发人员记录其复杂的微服务架构，但缺乏专门的技术作者或设计师。","case_study_3_solution":"使用AI流程图生成器创建了全面的系统图，以最少的技术输入显示服务关系、数据流和集成点。","case_study_3_results":"新开发人员的入职时间减少了40%，文档维护简化，非技术团队对系统的理解也得到改善。","case_study_4_title":"社交媒体内容策略","case_study_4_industry":"数字营销","case_study_4_challenge":"一家数字营销机构需要为社交媒体活动创建引人入胜、数据丰富的视觉效果，但受到有限的设计资源和紧迫的截止日期的限制。","case_study_4_solution":"实施我们的AI图形生成器，快速生成根据每个客户的品牌和活动目标定制的信息图和数据可视化。","case_study_4_results":"内容制作时间减少了75%，与纯文本帖子相比，参与率提高了38%，客户满意度得分显著提高。","research_title":"基于研究的可视化","research_description":"我们的AI图形生成器建立在认知心理学、数据可视化和信息设计领域的坚实科学基础和循证原则之上。","research_button_science":"循证设计","research_button_data":"数据可视化研究","research_area_1_title":"视觉信息处理","research_area_1_description":"我们的AI图形生成器利用了人类处理视觉信息比文本更高效的研究成果。研究表明，视觉信息的处理速度比文本快60,000倍，并能将理解力提高400%。","research_area_2_title":"数据可视化原则","research_area_2_description":"我们的AI实施了来自Edward Tufte和Stephen Few等领先研究者的既定数据可视化原则，确保复杂数据以清晰、精确和最小认知负荷的方式呈现。","research_area_3_title":"认知负荷理论","research_area_3_description":"我们的AI图形生成器应用认知负荷理论创建视觉效果，减少外部认知负荷，让用户能够专注于理解内容而不是解读复杂的呈现方式。","research_area_4_title":"信息设计","research_area_4_description":"我们的系统融合了信息设计和视觉层次原则，确保最重要的信息突出显示，复杂关系清晰传达。","testimonials_title":"用户评价","testimonials_description":"加入数千名已经通过我们的AI图形生成器改变其视觉沟通方式的专业人士。","testimonials_rating":"4.8平均评分","testimonials_users":"15,000+活跃用户","testimonial_1_content":"作为一名没有设计背景的营销经理，我一直在为我们的活动创建专业信息图表而苦苦挣扎。AI图形生成器彻底改变了一切！现在我可以在几分钟内创建出令人惊叹的视觉效果，这些效果过去需要我们的设计团队数天时间。自从我们开始使用这些图形以来，我们的参与率提高了45%。","testimonial_1_role":"营销经理","testimonial_2_content":"流程图生成器对我们的开发团队来说是一个游戏规则改变者。我们过去花费数小时创建系统架构图，但现在我们可以在几分钟内生成它们。AI似乎能从文本描述中准确理解我们的需求。这就像拥有一个全天候待命的专业设计师。","testimonial_2_role":"软件架构师","testimonial_3_content":"作为一名数据分析师，我需要定期向非技术利益相关者展示复杂的发现。AI图形生成器帮助我将枯燥的电子表格转化为引人入胜的数据可视化，讲述一个故事。我的演示现在能够从高管那里获得更多的参与和理解。","testimonial_3_role":"数据分析师","testimonial_4_content":"我使用AI图形生成器制作课堂材料，我的学生非常喜欢！这些视觉效果以文本无法做到的方式解释复杂概念。最棒的是我可以为每节课快速创建定制图形 - 过去需要数小时的工作现在只需几分钟。","testimonial_4_role":"高中教师","testimonial_5_content":"SVG代码生成器对我们的Web开发团队来说非常出色。我们可以快速创建轻量级且响应式的自定义图形。代码干净且结构良好，易于集成到我们的项目中。这个工具显著减少了我们对库存图形的依赖。","testimonial_5_role":"前端开发者","testimonial_6_content":"我们的研究团队使用AI图形生成器为科学出版物创建可视化。图形的质量和准确性令人印象深刻，它们节省了我们无数小时，现在我们可以将这些时间用于实际研究而不是创建图表。这是任何研究小组必备的工具。","testimonial_6_role":"研究主管","graphics_comparison_title":"我们的比较优势","graphics_comparison_description":"了解FunBlocks AI图形生成器与传统设计工具和市场上其他解决方案的比较。","graphics_comparison_winner":"最佳整体解决方案","graphics_comparison_value":"最高时间节省价值","comparison_funblocks":"FunBlocks AI图形","comparison_traditional":"传统设计工具","comparison_other_ai":"其他AI工具","comparison_templates":"模板服务","comparison_feature_1":"无需设计技能","comparison_feature_1_tooltip":"无需任何设计经验即可创建专业图形","comparison_feature_2":"一键生成","comparison_feature_2_tooltip":"通过单个文本提示创建完整图形","comparison_feature_3":"内容定制","comparison_feature_3_tooltip":"自动调整设计以匹配特定内容","comparison_feature_4":"信息图生成","comparison_feature_4_tooltip":"从文本描述创建数据丰富的视觉故事","comparison_feature_5":"流程图创建","comparison_feature_5_tooltip":"从描述生成流程和图表","comparison_feature_6":"数据可视化","comparison_feature_6_tooltip":"从数据或描述创建图表和图形","comparison_feature_7":"SVG代码生成","comparison_feature_7_tooltip":"生成可编辑的SVG代码以进一步自定义","comparison_feature_8":"学习曲线","comparison_feature_8_tooltip":"易于学习和立即使用","comparison_feature_9":"创建速度","comparison_feature_9_tooltip":"在几分钟而不是几小时内创建图形","comparison_feature_10":"应用设计原则","comparison_feature_10_tooltip":"自动应用专业设计原则"}},"en":{"common":{"login":"Login","logout":"Logout","pricing":"Pricing","why_funblocks_ai":"Why FunBlocks AI {{app}}?","try_now":"Try Now","english":"English","chinese":"Chinese","load_more":"Load More","delete":"Delete","like":"Like","unlike":"Unlike","share_to_showcase":"Click to share to Shared list, get more likes, and earn free generations","username_password_required":"Username and password cannot be empty","check_credentials":"Please check your username and password","username":"Username","enter_username":"Enter username","password":"Password","enter_password":"Enter password","logging_in":"Logging in...","no_cards_yet":"You haven't created any cards yet.","generate_card":"Generate","app_slogan_graphics":"Smart Visuals, Effortless Creativity","app_one_sentence_graphics":"Turn text and data into eye-catching infographics and clever knowledge cards with AI – no design skills required.","app_slogan_mindmap":"Remap Your Mind with AI","app_one_sentence_mindmap":"Let AI be your guide to explore the world of knowledge!","app_slogan_brainstorming":"AI-Powered Brainstorming to Ignite Unlimited Creativity","app_one_sentence_brainstorming":"Let AI be your creative partner, breaking through mental barriers and discovering new possibilities","app_slogan_decision":"AI-Powered Decision Analysis Assistant","app_one_sentence_decision":"Make better decisions with AI-driven analysis that considers multiple perspectives, risks, and outcomes to help you reach rational conclusions","app_slogan_planner":"AI Task Planner - Transform Complex Tasks into Actionable Plans","app_one_sentence_planner":"Harness the power of AI to break down complex tasks, generate detailed action plans, and boost your productivity with intelligent task management.","app_slogan_slides":"Create Insightful Presentations with AI","app_one_sentence_slides":"AI-powered presentation tool that helps professionals create compelling presentations efficiently and effectively.","app_slogan_youtube":"Transform YouTube Videos into Interactive Knowledge Maps","app_one_sentence_youtube":"Unlock the knowledge within YouTube videos instantly. Our AI-powered tool transforms lengthy videos into concise, interactive summaries and knowledge maps.","app_slogan_dreamlens":"AI DreamLens","app_one_sentence_dreamlens":"Unlock the Hidden Meanings of Your Dreams with AI","app_slogan_art":"Instant Artistic Understanding Through AI","app_one_sentence_art":"Your AI Guide to Art Appreciation","app_slogan_photo":"Transform Every Shot into a Learning Journey","app_one_sentence_photo":"Your AI Photography Mentor for Instant Professional Feedback and Growth","app_slogan_reading":"Navigate Books, Illuminate Minds","app_one_sentence_reading":"Your AI-Powered Reading Guide to Knowledge Discovery","app_slogan_startupmentor":"AI Mentor for Startups","app_one_sentence_startupmentor":"Elevate Your Startup. Accelerate Your Success.","app_slogan_businessmodel":"Advanced AI-powered Strategic Analysis Tool","app_one_sentence_businessmodel":"Provides comprehensive business model analysis and innovation recommendations in seconds.","app_slogan_okr":"Precision Goals, Limitless Potential","app_one_sentence_okr":"Transforming Aspirations into Achievements","app_slogan_poetic":"Transform Images into Cultural Poetry","app_one_sentence_poetic":"AI-powered tool that turns everyday images into witty, culturally-rich poetic commentaries","app_slogan_onepageslide":"AI SlideGenius - Create Professional Slides in Seconds","app_one_sentence_onepageslide":"Transform your text content into visually stunning presentations with the power of AI.","app_slogan_infographic":"AI-Powered Infographic Generation","app_one_sentence_infographic":"Transform your text content into beautiful infographics in Seconds","app_slogan_mindkit":"Where Mental Models Meet AI","app_one_sentence_mindkit":"Resolve complex problems with modular thinking frameworks","app_slogan_mindsnap":"Where AI meets mental models to visualize complex ideas","app_one_sentence_mindsnap":"Explore your topic or question with crystal-clear, structured, and insightful infographics in seconds","ai_mindsnap_desc":"Explore any topic using AI-powered mental model analysis and create beautiful infographics instantly","app_slogan_insightcards":"Transform Your Thinking with AI-Powered Insight Cards","app_one_sentence_insightcards":"Create visually appealing insight cards that reveal hidden connections, contradictions, and perspectives on any topic","ai_insightcards_desc":"Generate visual insight cards that provide unique perspectives through frameworks like Aha Insights, Paradoxical Interpretations, and more","app_slogan_horoscope":"Your Destiny, Decoded by AI","app_one_sentence_horoscope":"Where Ancient Wisdom Meets Modern Intelligence - Personalized Astrological Insights Powered by Advanced AI","app_slogan_counselor":"Discover Your Best Self Through AI Counseling","app_one_sentence_counselor":"Expert psychological insights and empathetic support to help you navigate life's challenges","app_slogan_criticalthinking":"Beyond Intuition, Into Deep Analysis","app_one_sentence_criticalthinking":"AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths","app_slogan_reflection":"Beyond First Thoughts","app_one_sentence_reflection":"Develop stronger reasoning and deeper understanding with an AI coach that challenges your thinking","app_slogan_feynman":"Master Complex Topics with Your AI Feynman Tutor","app_one_sentence_feynman":"Transform confusion into clarity through AI-powered Feynman learning techniques that break down any subject into intuitive mind maps","app_slogan_movie":"Discover the Art of Cinema Through AI-Powered Mind Maps","app_one_sentence_movie":"Transform your movie-watching experience with intelligent analysis that reveals hidden layers, themes, and connections in your favorite films","app_slogan_refinequestion":"Ask Smarter, Think Deeper","app_one_sentence_refinequestion":"Harness AI-powered critical thinking to craft precise, powerful questions that unlock deeper insights and better solutions","app_slogan_bias":"Uncover the Truth Behind Arguments","app_one_sentence_bias":"Leverage AI to Analyze Cognitive Biases and Logical Fallacies, Develop Clearer Thinking","to_original_page":"Open the original page","gender":"Gender","male":"Male","female":"Female","horoscope_range":"Horoscope Range","horoscope_today":"Today's Horoscope","horoscope_all":"All Horoscopes","mindmap":"Mindmap","graphics":"Infographics","mine":"Mine","my_generations":"My generations","please_login_to_view_mine":"Please login to view your {{app}}","generate_new_card":"Generate New Card","please_select_card_type":"Please select a AI Master","please_enter_valid_message":"Please enter a valid message","please_select_language":"Please select a language","please_enter_valid_url":"Please enter a valid webpage link","please_enter_valid_video_url":"Please enter a valid YouTube video link","enter_webpage_url":"Please enter or paste your link","output_language":"Output","card_generation_success":"Content generated successfully","card_generation_error":"An error occurred while generating the card. Please try again later.","free_trial_exceeded":"You have used up your free trial quota. Please upgrade.","daily_quota_exceeded":"You have used up your trial or daily generation quota. Please upgrade.","upgrade":"Upgrade","generating":"Generating...","share_to_social_platforms":"Share to Social Platforms","title_and_link_copied":"Title and link copied","copy":"Copy","copied":"Copied to clipboard","copy_failed":"Copy failed","save_image_and_paste_to_instagram":"Please save the image and paste the title and link to Instagram","share_to_twitter":"Share to X/Twitter","share_to_facebook":"Share to Facebook","share_to_linkedin":"Share to LinkedIn","share_to_whatsapp":"Share to WhatsApp","share_to_instagram":"Share to Instagram","share_via_email":"Share via Email","copy_title_and_link":"Copy Link","download_image":"Download Image","email_verification_login":"Email Verification Login","please_complete_captcha":"Please complete the CAPTCHA","please_enter_valid_email":"Please enter a valid email address","failed_to_send_verification_code":"Failed to send verification code","verification_failed":"Verification failed","email_address":"Email Address","enter_your_email":"Enter your email address","sending":"Sending...","send_verification_code":"Send Verification Code","verifying":"Verifying...","verify_and_login":"Verify and Login","select_card":"Select AI Master","select_graph_type":"Graph Type","error_loading_svg":"Unable to load SVG content","loading":"Loading...","fullscreen_display":"Fullscreen display","fullscreen_tooltip":"Fullscreen display","exit_fullscreen_tooltip":"Exit Fullscreen display","download":"Download","share":"Share","save_svg":"Save SVG to local device","ai_insights":"FunBlocks AI Graphics","find_more_cards_or_create_your_own":"Find more or create your own","close_modal":"Close modal","login_success":"Login successful","login_failed":"Login failed","login_error":"An error occurred during login","login_with_google":"Login with Google","login_modal_instruction":"Please complete your login in the new tab, then click 'Completed Login' to confirm.","open_login_page":"Open Login Page","completed_login":"Completed Login","checking":"Checking...","not_logged_in_yet":"Not logged in yet","please_complete_login_first":"Please complete the login process first","check_login_failed":"Failed to check login status","login_detected":"Login detected","login_successful_auto_close":"Login successful, modal will close automatically","auto_check_login_status":"We'll automatically detect when you complete the login","example_wise_insight":"The nature of happiness","example_fun_fact":"Interesting facts about space","example_creative_idea":"Innovative solutions for climate change","examples":"Examples","your_input":"Your input...","your_question":"Your question...","given_text":"Enter or paste text...","input_length_warning":"Input length: {{current}}/{{max}}","provide_text_or_webpage":"Provide text or webpage link","url_content_fetch_failed":"Failed to fetch URL content","enter_your_topic":"Please enter your topic or text...","enter_your_ideas":"Please enter your startup idea or plan...","please_enter":"Please enter...","ai_actions":"AI Actions","fix_codes_bug":"Fix Code Issues","improve_codes":"Improve Content","user_input":"User Input","confirm":"Confirm","cancel":"Cancel","please_enter_your_requirements":"Please enter your modification requirements...","input_hint":"Tip: Press Ctrl+Enter to confirm quickly, press Esc to cancel","ai_action_success":"AI action completed successfully","ai_action_error":"AI action failed, please try again","fun_and_informative":"Fun, Informative, Extraordinary","explore_ai_thoughts_and_humor":"Explore AI's deep thoughts and sense of humor, let FunBlocks AI Graphics become your creative assistant.","generate_insightful_and_fun_cards":"Generate insightful and fun infographic cards, easily gain new perspectives and stand out on social platforms.","why_choose_funblocks_ai_insights":"Why Choose FunBlocks AI Graphics?","funblocks_ai_insights_combines_llm_and_card_features":"FunBlocks AI Graphics combines the powerful knowledge and reasoning capabilities of LLM with the readability and sharing features of cards. Whether for social media, presentations, or personal notes, we can provide you with unique perspectives and valuable content. Let AI become your thinking partner and explore the infinite possibilities of creativity together!","faq":"Frequently Asked Questions","one_click_joy":"One-Click Joy","experience_ai_insights_now":"Experience the magic of AI Graphics now—bringing creativity and joy right to your fingertips!","start_generating":"Start Generating","graphics_feature_1_text":"Generate Informative insights based on your provided topic.","graphics_feature_2_text":"Create fun, shareable cards with one-click.","graphics_feature_3_text":"Explore new ideas and perspectives with AI.","meta":{"title":"FunBlocks AI Tools: AI-Powered Productivity \u0026 Creativity Tools | Whiteboards, Mind Map, Presentations, Infographics \u0026 More","description":"Boost productivity \u0026 unleash creativity with FunBlocks AI! All-in-one AI platform with whiteboard, mind mapping, brainstorming, document writing, presentation creation, and more. Free trial available!","keywords":"LLM, ChatGPT, FunBlocks AI, AI Insights, AI productivity tools, AI creativity tools, AI whiteboard, AI mind map, AI brainstorming, AI presentation maker, AI infographic generator, AI decision analyzer, AI task planner, AI YouTube summarizer, AI knowledge assistant, free AI tools, AI platform, visual tools, one-click AI, integrated AI tools, mind mapping software, presentation software, infographic maker, creative content","og_title":"FunBlocks AI Tools: AI-Powered Productivity \u0026 Creativity Tools | Whiteboards, Mind Map, Presentations, Infographics \u0026 More","og_description":"Boost productivity \u0026 creativity with FunBlocks AI—your all-in-one tool for whiteboards, mind maps, brainstorming, writing, and presentations. Try it free!"},"graph_chat":"ChatGraphics","make_graph":"InfoGraphics","card_guru":"InsightCards","image_insights":"WonderLens","graph_chat_placeholder":"Chat with AI to generate an infographic...","make_graph_placeholder":"Enter text, upload a file, or provide a website URL...","generate":"Generate","create_artwork":"Create Artwork","input_length_sufficient":"Input length is sufficient","input_too_short":"Input too short","input_length_requirement":"Please enter at least 50 characters when not providing a URL.","explore_other_products":"Explore Other FunBlocks AI Products","boost_productivity":"Boost your productivity with AI-powered tools for seamless web interactions.","unleash_creativity":"Unleash your creativity with AI-driven brainstorming and mind mapping.","create_stunning_presentations":"Create stunning presentations effortlessly with AI-powered slide generation.","transform_workflow":"Transform your workflow with an all-in-one AI assistant for enhanced creativity and efficiency.","contact_us":"Contact Us","image":"Image","art":"Masterpieces","upload_image":"Upload Image","choose_image":"Choose Image","uploaded_image":"Uploaded Image","image_url":"Image URL","paste_image_url":"Paste image URL here","or":"OR","please_upload_or_provide_image_url":"Please upload an image or provide an image URL","upload_failed":"Failed to upload image. Please try again.","image_load_error":"Failed to load image","wonderlens_tip":"Generate fun commentary pieces based on the image","zoom_in":"Zoom in","zoom_out":"Zoom out","book":"Book","movie":"Movie","video":"Youtube Video","link":"Web page","topic":"Topic","doc":"Document","others":"Others","book_name":"Book title, author...","movie_name":"Movie title","video_url":"Youtube video link","webpage_url":"Web page link","to_aiflow":"Keep exploring with AIFlow","to_aiflow_tips":"Explore more in-depth with AIFlow","deep_dive_to_topic":"Generate a detailed introduction to the topic","expand_ideas":"Break down the topic or generate more ideas","mental_model":"Mental model","educational_model":"Educational model","upgrade_to_vip":"Upgrade your plan","upgrade_to_vip_msg":"You have exhausted your free AI generation quota for today. Please upgrade to a FunBlocks AI membership to enjoy unlimited access to all FunBlocks AI products and services, and continue to benefit from our premium offerings.","task_analysis":"Task analysis","task_breakdown":"Task breakdown","task_priority":"Adjust task priority","thinking_model":"Thinking model","swot_analysis":"SWOT Analysis","first_principle":"First Principles Thinking","business_model_canvas":"Business Model Canvas","fivew1h_method":"5W1H Method","scamper_method":"SCAMPER Method","six_thinking_hats":"Six Thinking Hats","pdca":"PDCA Cycle","systems_thinking":"Systems Thinking","dialectical_thinking":"Dialectical Thinking","probabilistic_thinking":"Probabilistic Thinking","steep_analysis":"STEEP Analysis","five_forces":"Five Forces Analysis","four_p":"4P Marketing Mix","triz":"Theory of Inventive Problem Solving","rephrazing":"Problem Rephrasing","learning_pyramid":"Learning Pyramid","kwl":"KWL Map","changing_perspectives":"Changing Perspectives","reverse_thinking":"Reverse Thinking","role_playing":"Role Playing","mckinsey_7S_framework":"McKinsey 7S Framework","value_proposition_canvas":"Value Proposition Canvas","pros_cons":"Pros and Cons","decision_tree":"Decision Tree","decision_matrix":"Decision Matrix","cost_benefit_analysis":"Cost-Benefit Analysis","casual_chain":"Casual Chain","second_order_thinking":"Second Order Thinking","inversion_thinking":"Inversion Thinking","scientific_method":"Scientific Method","pyramid_principle":"Pyramid Principle","occams_razor":"Occam's Razor","mece_principle":"MECE Principle","eisenhower_matrix":"Eisenhower Matrix","bloom":"Bloom","marzano":"Marzano","addie":"ADDIE","5e":"5E","gagne":"Gagne","constructivist":"Constructivist","auto":"Auto","other":"Other","enter_mental_model":"Type modal name","select_none":"None","birthdate":"Birth Date","platform_title":"AI-Powered Tools for Creativity and Productivity","platform_description":"Engage with AI through visual formats——beyond the text chatbox.","platform_description_1":"Explore new ways to interact with AI beyond text conversations.","explore_tools":"Explore AI Tools","learn_more":"Learn More","platform_meta_title":"AI-Powered Tools for Creativity and Productivity | AI Mindmap, Infographics, Slides and more | FunBlocks AIFlow","platform_meta_description":"Discover FunBlocks AI's suite of intelligent tools: AI Graphics, Mindmap Generator, Brainstorming Assistant, Presentation Creator, Decision Analyzer, and Task Planner. Boost your productivity and creativity with our visual AI tools.","ai_graphics_desc":"Create engaging infographics and cards with unique insights powered by AI","ai_mindmap_desc":"Effortlessly create detailed mind maps from any source, including books, movies, videos, and documents, or explore a single topic","ai_brainstorming_desc":"Partner with AI to spark creative ideas and solutions, perfect for both work and study","ai_slides_desc":"Effortlessly create engaging presentations with AI in just one click.","ai_decision_desc":"Get rational analysis and insights for better decision making","ai_planner_desc":"Break down complex tasks into manageable steps","ai_youtube_desc":"Summarize YouTube videos and transform into interactive knowledge maps","ai_dreamlens_desc":"Personalized dream analysis with multi-perspective psychological insights, helping you unlock your subconscious","ai_art_desc":"Instantly generate professional art appreciation mind maps, providing expert analysis and deeper understanding","ai_photo_desc":"AI Photography Mentor for Instant Professional Feedback and Growth","ai_reading_desc":"Generates structured mind maps from books, enabling efficient comprehension and knowledge internalization","ai_okr_desc":"Professional goal management and strategic planning tool that transform grand visions into specific, measurable strategic objectives","ai_startupmentor_desc":"Transform your entrepreneurial journey with AI-powered startup mentorship. Get instant, expert-level analysis and strategic guidance for your business venture","ai_businessmodel_desc":"Provides comprehensive business model analysis and innovation recommendations in seconds","ai_poetic_desc":"Add witty, culturally-rich poetic commentaries to image","ai_onepageslide_desc":"Transform your text content into visually stunning presentations with the power of AI","ai_infographic_desc":"Transform your text content into beautiful infographics with AI","ai_mindkit_desc":"Resolve complex problems with AI-powered Mental Models, e.g. First Principles Thinking, SWOT Analysis, 5W1H Method, SCAMPER Method, etc.","ai_horoscope_desc":"Personalized Astrological Insights Powered by Advanced AI","ai_counselor_desc":"Expert psychological insights and empathetic support to help you navigate life's challenges","ai_criticalthinking_desc":"AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths","ai_reflection_desc":"Develop stronger reasoning and deeper understanding with an AI coach that challenges your thinking","ai_feynman_desc":"Transform confusion into clarity through AI-powered Feynman learning techniques that break down any subject into intuitive mind maps","ai_bias_desc":"Leverage AI to Analyze Cognitive Biases and Logical Fallacies, Develop Clearer Thinking","ai_movie_desc":"Discover the Art of Cinema Through AI-Powered Mind Maps","ai_refinequestion_desc":"Harness AI-powered critical thinking to craft precise, powerful questions that unlock deeper insights and better solutions","why_funblocks":"Why Choose FunBlocks AI?","get_started":"Get Started","ai_powered_intelligence":"Advanced AI Intelligence","ai_powered_intelligence_desc":"Powered by cutting-edge large language models, our tools provide deep insights, creative solutions, and intelligent automation across all features. Experience AI that truly understands and enhances your work.","integrated_toolset":"Comprehensive Toolset","integrated_toolset_desc":"Our AI tool suite, from mind mapping to presentation creation, is designed to cater to specific contexts and needs, simplifying usage and enhancing accuracy while enabling ongoing exploration and creation on the FunBlocks AI platform.","user_friendly":"Innovative AI Interaction Experience","user_friendly_desc":"Our tools redefine the interaction experience with AI, providing a visual interface and structured expression that is easy to use and aligns with the brain's processing and exploration needs. Complex AI functions and prompts are simplified into one-click operations, making advanced features accessible to every user.","mental_models_toolify":"Toolification of Classic Mental Models","mental_models_toolify_desc":"We combine classic mental models with AI to provide exceptional thinking coaching and guidance, allowing the accumulated wisdom of humanity to benefit everyone in the form of tools.","brainstorming_intro_title":"AI-Powered Brainstorming to Ignite Unlimited Creativity","brainstorming_intro_description":"Let AI be your creative partner, breaking through mental barriers and discovering new possibilities","brainstorming_feature_1_title":"Diverse Perspectives","brainstorming_feature_1_text":"AI generates ideas from multiple angles, breaking through the limitations of individual thinking","brainstorming_feature_2_title":"Structured Exploration","brainstorming_feature_2_text":"Organized approach to idea generation with categorized suggestions and mind mapping","brainstorming_feature_3_title":"Instant Inspiration","brainstorming_feature_3_text":"Generate creative ideas instantly for any topic, challenge, or project","brainstorming_benefits_title":"Benefits of AI-Powered Brainstorming","brainstorming_benefit_1":"Overcome creative blocks with fresh perspectives","brainstorming_benefit_2":"Save time in ideation phases with instant idea generation","brainstorming_benefit_3":"Explore unexpected connections between concepts","brainstorming_benefit_4":"Create comprehensive solution sets for complex problems","brainstorming_usecases_title":"Popular Applications","brainstorming_usecase_1_title":"Content Creation","brainstorming_usecase_1_text":"Generate creative content ideas for blogs, social media, and marketing","brainstorming_usecase_2_title":"Problem Solving","brainstorming_usecase_2_text":"Explore diverse solutions to business, technical, or personal challenges","brainstorming_usecase_3_title":"Project Planning","brainstorming_usecase_3_text":"Develop comprehensive project approaches and execution strategies","brainstorming_usecase_4_title":"Learning \u0026 Research","brainstorming_usecase_4_text":"Explore topics from multiple angles to deepen understanding","why_not_chatgpt":"Why Not Just Use ChatGPT?","chatgpt_comparison_intro":"While ChatGPT is powerful, FunBlocks AI Tools offer unique advantages that make information processing and learning more effective:","comparison_visual_title":"Visual Learning vs Text Overload","chatgpt_text_heavy":"Generates walls of text that are time-consuming to read and process","funblocks_visual_friendly":"Creates visual mind maps, infographics, and slides for faster understanding and better retention","comparison_exploration_title":"Exploration Freedom vs Linear Constraints","chatgpt_linear_chat":"Linear chat interface limits exploration to a narrow path","funblocks_multi_perspective":"Whiteboard and mind map interface enables multi-dimensional exploration of ideas","comparison_guidance_title":"Proactive Guidance vs Passive Responses","chatgpt_passive_waiting":"Passively waits for user questions, which can be challenging to formulate","funblocks_proactive_guide":"Proactively generates exploration paths and guides users through complex topics","comparison_learning_title":"Skill Building vs Quick Answers","chatgpt_answer_only":"Simply provides answers without developing critical thinking skills","funblocks_thinking_process":"Collaborates with users to explore solutions while building analytical capabilities","criticalthinking_intro_title":"Beyond Intuition, Into Deep Analysis","criticalthinking_intro_description":"AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths","criticalthinking_feature_1_title":"Structured Analysis","criticalthinking_feature_1_text":"Apply systematic critical thinking frameworks to any problem or question","criticalthinking_feature_2_title":"Cognitive Bias Detection","criticalthinking_feature_2_text":"Identify potential thinking errors and logical fallacies in arguments","criticalthinking_feature_3_title":"Multi-perspective Evaluation","criticalthinking_feature_3_text":"Analyze issues from multiple angles to develop comprehensive understanding","criticalthinking_benefits_title":"Benefits of Critical Thinking","criticalthinking_benefit_1":"Make more rational, well-reasoned decisions","criticalthinking_benefit_2":"Develop stronger arguments and logical reasoning skills","criticalthinking_benefit_3":"Avoid common cognitive pitfalls and biases","criticalthinking_benefit_4":"Improve problem-solving efficiency with structured approaches","criticalthinking_frameworks_title":"Critical Thinking Frameworks","criticalthinking_framework_1_title":"Socratic Questioning","criticalthinking_framework_1_text":"Probe assumptions and evidence through systematic questioning","criticalthinking_framework_2_title":"PESTEL Analysis","criticalthinking_framework_2_text":"Examine political, economic, social, technological, environmental and legal factors","criticalthinking_framework_3_title":"Bloom's Taxonomy","criticalthinking_framework_3_text":"Progress through understanding, application, analysis, evaluation, and creation","criticalthinking_framework_4_title":"Systems Thinking","criticalthinking_framework_4_text":"Analyze complex systems by examining interconnections and feedback loops","mindmaps":"Mind Maps","slides":"Slides","infographics":"Infographics","ai_tools_heading":"FunBlocks AI Tools","ai_tools_description":"The Entrances to Clear Mind and Creative Success","thinking_benefits_title":"Empowering Your Mind","deep_thinking":"Deepen Thinking","deep_thinking_desc":"Analyze cognitive biases and logical fallacies\nRefine questions for better problem-solving\nMake data-driven decisions\nDevelop systematic analytical frameworks\nStrengthen reasoning abilities through AI coaching","enhanced_productivity":"Enhanced Productivity","enhanced_productivity_desc":"One-click generation of professional presentations\nInstant creation of engaging infographics\nAI-powered mind mapping for complex topics\nAutomated task planning and breakdown","boosted_creativity":"Boosted Creativity","boosted_creativity_desc":"AI-enhanced brainstorming sessions\nIntegration with classic mental models\nUnlimited canvas for idea exploration\nInteractive mind mapping\nCross-pollination of ideas through AI insights","ai_lessonplans_desc":"Generate comprehensive, research-based lesson plans instantly with just a topic input. Transform your teaching with AI-powered instructional design expertise","app_slogan_lessonplans":"Smart Lesson Planning, Every Class Extraordinary","app_one_sentence_lessonplans":"Generate comprehensive, research-based lesson plans instantly with just a topic input. Transform your teaching with AI-powered instructional design expertise","ai_teachingslides_desc":"Empower every educator to design expert-level teaching content and focus on the art of teaching, not tedious slide creation","app_slogan_teachingslides":"Create Professional Teaching Slides with AI","app_one_sentence_teachingslides":"Empower every educator to design expert-level teaching content and focus on the art of teaching, not tedious slide creation","ai_dokassessment_desc":"Delivers professional-grade evaluations with cognitive depth analysis, implementation guides, and quality standards - all in minutes","app_slogan_dokassessment":"Create Expert-Level Educational Assessments with AI","app_one_sentence_dokassessment":"Delivers professional-grade evaluations with cognitive depth analysis, implementation guides, and quality standards - all in minutes","ai_bloom_desc":"Transform any topic into professional educational mind maps with Bloom's Taxonomy and AI, in just one click","app_slogan_bloom":"Teaching Design, Brilliantly Reimagined","app_one_sentence_bloom":"Transform any topic into professional educational mind maps with Bloom's Taxonomy and AI, in just one click","ai_solo_desc":"Build structured learning using the SOLO Taxonomy, breaking down topics into progressive cognitive levels for effective teaching and learning.","app_slogan_solo":"Transform Any Topic into Structured Learning with SOLO Taxonomy","app_one_sentence_solo":"AI-powered educational planning tool that breaks down topics into progressive cognitive levels for effective teaching and learning","ai_dok_desc":"Generate comprehensive curriculum mind maps in seconds with our Webb's DOK-powered tool","app_slogan_dok":"AI DOKBrain: The Intelligence Behind Deeper Learning","app_one_sentence_dok":"From recall to creation, design complete learning journeys across all cognitive levels","ai_dok_assessment_desc":"Create comprehensive educational assessments based on Webb's DOK framework with AI-powered analysis and professional quality","app_slogan_dok_assessment":"AI DOK Assessment: Smart Assessment Design, Professional Results","app_one_sentence_dok_assessment":"Transform any teaching topic into professional, multi-level assessments with DOK-based cognitive progression","ai_marzano_desc":"Transform any topic into a comprehensive learning design using Marzano's Taxonomy of Educational Objectives","app_slogan_marzano":"Intelligent Learning Design Based on Marzano's Taxonomy","app_one_sentence_marzano":"Transform any topic into a comprehensive learning journey through all cognitive dimensions","ai_layeredexplanation_desc":"Scale any concept through progressive layers of knowledge with AI MindLadder, from intuitive basics to profound insights","app_slogan_layeredexplanation":"AI MindLadder: Climb to Complete Understanding","app_one_sentence_layeredexplanation":"Scale any concept through progressive layers of knowledge with our 8-tier learning system","erase_watermark":"Erase Watermarks","edit_image":"Edit Image","edit_instructions":"Editing Instructions","image_edit_placeholder":"Describe what you want to change in the image (e.g., 'remove the background', 'make it brighter', 'change the sky to sunset colors')","ai_erase_desc":"One-click watermark removal to restore the true beauty of your images","app_slogan_erase":"AI Erase - Remove Watermarks Instantly, Preserve Image Beauty","app_one_sentence_erase":"Advanced AI technology that intelligently identifies and removes watermarks while maintaining image integrity","ai_avatar_desc":"Instantly convert your photos into stylized avatars in a wide range of artistic styles","app_slogan_avatar":"AI Avatar Generator | Transform Your Photos into Stunning Digital Art!","app_one_sentence_avatar":"Instantly convert your photos into high-quality, stylized avatars in a wide range of artistic styles!","ai_imageeditor_desc":"Edit images using natural language instructions - no Photoshop skills required","app_slogan_imageeditor":"AI Image Editor - Transform Images with Natural Language","app_one_sentence_imageeditor":"Edit photos and images using simple text instructions - just describe what you want to change and our AI will do the rest!","ai_sketch_desc":"Transform your simple drawings into stunning artwork with AI-powered style enhancement","app_slogan_sketch":"AI Sketch - Turn Your Drawings into Art","app_one_sentence_sketch":"Draw simple sketches and watch AI transform them into professional artwork in multiple artistic styles","llm_support_title":"Support for All Major Large Language Models","llm_support_desc":"Access all industry-leading models in one place to enhance your learning and work efficiency. No need to switch between platforms or purchase memberships for each one.","target_audience_title":"Who is FunBlocks AI Tools For?","target_audience_desc":"FunBlocks AI Tools are designed for anyone who wants to enhance their learning, thinking, and productivity through AI-powered tools.","target_audience_students":"Students","target_audience_students_desc":"Perfect for students looking to improve their study methods, create better notes, and understand complex topics.","target_audience_professionals":"Professionals","target_audience_professionals_desc":"Ideal for professionals who need to organize ideas, create presentations, and enhance their decision-making process.","target_audience_creatives":"Creatives","target_audience_creatives_desc":"Great for creative professionals who want to explore ideas, generate content, and visualize concepts.","use_cases_title":"Real-World Applications","use_cases_desc":"Discover how FunBlocks AI can transform your work and learning experience with these practical examples.","use_case_1_title":"Academic Excellence","use_case_1_desc":"Create comprehensive mind maps for complex subjects, generate study guides, and improve your understanding of difficult concepts.","use_case_2_title":"Business Innovation","use_case_2_desc":"Develop business strategies, create professional presentations, and analyze market trends with AI-powered insights.","use_case_3_title":"Creative Projects","use_case_3_desc":"Transform your ideas into visual stories, generate creative content, and explore new perspectives with AI assistance.","use_case_4_title":"In-Depth Research \u0026 Analysis","use_case_4_desc":"Conduct thorough research from multiple perspectives, delving deeply into topics and questions for comprehensive understanding.","use_case_5_title":"Personal Growth \u0026 Development","use_case_5_desc":"Chart your learning journey using visual frameworks. Foster critical thinking through AI-enhanced insights and a multi-perspective interface.","use_case_6_title":"Team Collaboration","use_case_6_desc":"Create shared knowledge maps, facilitate brainstorming sessions, and improve team communication with visual thinking tools.","platform_faq_1_q":"What makes FunBlocks AI different from other AI platforms?","platform_faq_1_a":"FunBlocks AI stands out by focusing on visual thinking rather than just text. While most AI platforms produce text-heavy outputs, we transform ideas into visual maps, infographics, and presentations. Our platform combines multiple AI models, specialized tools for different thinking styles, and a user-friendly interface designed to enhance your creative and analytical processes.","platform_faq_2_q":"Do I need to be tech-savvy to use FunBlocks AI?","platform_faq_2_a":"Not at all! FunBlocks AI is designed with simplicity in mind. Our user-friendly interface guides you through each tool with clear instructions. Just input your ideas or questions, and our AI will handle the complex work of organizing and visualizing information. Whether you're a technology enthusiast or new to AI tools, you'll find FunBlocks approachable and intuitive.","platform_faq_3_q":"Which AI models does FunBlocks support?","platform_faq_3_a":"FunBlocks AI integrates all leading language models including GPT-4, Claude, Gemini, and other cutting-edge AI systems. We're continuously adding support for new models as they become available. You can easily switch between different models depending on your specific needs and preferences, all through a single unified interface.","platform_faq_4_q":"Can I use FunBlocks AI for educational purposes?","platform_faq_4_a":"Absolutely! Education is one of our core focus areas. FunBlocks AI offers specialized educational frameworks like BloomBrain, MarzanoBrain, SOLOBrain, and DOKBrain that align with established learning taxonomies. These tools help students organize complex topics, create study guides, and deepen understanding through visual learning. Teachers can use our platform to create engaging instructional materials and assess student comprehension.","platform_faq_5_q":"How does FunBlocks AI help with business and professional work?","platform_faq_5_a":"FunBlocks AI transforms business thinking through tools like OKR Assistant for goal setting, Business Model Analyzer for strategy development, and AI Task Planner for project management. The platform also excels at creating professional presentations, organizing competitive research, facilitating team brainstorming, and visualizing complex business concepts. These tools help professionals make better decisions, communicate more effectively, and drive innovation.","platform_faq_6_q":"Is my data secure when using FunBlocks AI?","platform_faq_6_a":"We take data security very seriously. All data transmitted to and from our platform is encrypted using industry-standard protocols. We don't store your content any longer than necessary to provide our services, and we never use your data to train our models without explicit consent. Our privacy policy clearly outlines how we protect your information and the measures we take to ensure confidentiality.","platform_faq_7_q":"What is the pricing model for FunBlocks AI?","platform_faq_7_a":"FunBlocks AI offers a flexible tiered pricing structure to accommodate different needs. We provide a free tier with access to essential tools and limited usage, perfect for individuals just getting started. Our premium plans include unlimited access to all tools, priority processing, and advanced features. Visit our pricing page for detailed information on current plans and pricing.","platform_faq_8_q":"Can I share and collaborate on projects created with FunBlocks AI?","platform_faq_8_a":"Yes! Collaboration is a key feature of FunBlocks AI. You can easily share your mind maps, infographics, and presentations with team members or colleagues. You can export your projects in various formats for presentations, reports, or further editing in other software.","platform_faq_9_q":"How often does FunBlocks AI add new features and tools?","platform_faq_9_a":"We're constantly evolving! Our development team releases new tools and improvements on a regular basis. We listen closely to user feedback and industry trends to prioritize the most valuable additions. Major feature updates typically occur monthly, with smaller improvements and fixes deployed weekly. Subscribe to our newsletter to stay informed about the latest enhancements and additions to the platform.","platform_faq_10_q":"Can FunBlocks AI help with creative projects and content creation?","platform_faq_10_a":"Absolutely! FunBlocks AI excels at boosting creativity through tools like AI Brainstorming, AI MindKit, and AI Poetic Lens. These tools help generate unique ideas, explore different perspectives, and visualize creative concepts. Content creators use our platform to plan content strategies, develop storylines, create visual storyboards, and organize creative projects—all with the assistance of advanced AI models that enhance rather than replace human creativity.","platform_faq_11_q":"How do AI Tools and AIFlow work together?","platform_faq_11_a":"AIFlow is the main platform that powers everything, while AI Tools are easy-to-use applications that help you quickly access features within AIFlow. These tools are designed for specific tasks, making it simple for you to explore and create with AIFlow.","platform_faq_12_q":"What if I can't find the tool I need in AI Tools?","platform_faq_12_a":"If you can't find a specific tool in AI Tools, don't worry! You can still use AIFlow directly to get the job done. While AI Tools are convenient, they may not cover every need, but AIFlow has all the capabilities you require.","platform_faq_13_q":"How does FunBlocks AI compare to other visual thinking tools?","platform_faq_13_a":"Unlike traditional visual thinking tools that require manual creation, FunBlocks AI automatically generates visual content based on your input. Our AI-powered approach saves time, provides deeper insights, and offers multiple perspectives that might not be immediately obvious. We combine the best of AI language models with visual thinking principles to create a unique tool that enhances both creativity and analytical thinking.","platform_faq_14_q":"Can FunBlocks AI help with learning complex subjects?","platform_faq_14_a":"Absolutely! FunBlocks AI excels at breaking down complex subjects into understandable visual formats. Our educational tools like MindLadder, BloomBrain, and SOLOBrain are specifically designed to facilitate learning by organizing information according to established educational frameworks. These tools help you visualize connections between concepts, identify knowledge gaps, and build a comprehensive understanding of any subject.","platform_faq_15_q":"How does FunBlocks AI ensure the quality of generated content?","platform_faq_15_a":"We employ several strategies to ensure high-quality outputs. First, we use only the most advanced AI models available. Second, we've developed specialized prompting techniques that guide these models to produce well-structured, accurate, and relevant content. Third, our tools are designed with specific frameworks that organize information logically. Finally, we continuously refine our systems based on user feedback to improve quality over time.","aiflow_and_aitools_title":"What is the Relationship Between AI Tools and AIFlow?","aitools_not_covered":"Can't find what you need in AI Tools? No problem! Use AIFlow directly.","try_aiflow_now":"Try FunBlocks AIFlow Now","about_aiflow_title":"AIFlow: Your Foundation for AI","about_aiflow_point1":"The core platform for all AI Tools.","about_aiflow_point2":"A flexible UI that offers a limitless canvas for your ideas.","about_aiflow_point3":"Access everything you need directly.","about_aitools_title":"AI Tools: Easy-to-Use Solutions","about_aitools_point1":"Ready-made applications and demos for quick use.","about_aitools_point2":"Designed for ease of use.","about_aitools_point3":"Solutions tailored to specific scenarios.","continue_exploring_ai_content":"The journey of exploration after AI content generation","what_after_generation":"What comes next after AI generates content?","ai_tools_generation_explanation":"Your journey of exploration doesn't have to end after generating content with AI Tools.","continue_with_aiflow":"Continue your exploration with AIFlow","click_continue_to_explore":"Click the 'Keep exploring with AIFlow' button","access_funblocks_aiflow":"Access the FunBlocks AIFlow interface","hero_badge_1":"AI-POWERED VISUAL TOOLS","hero_badge_2":"CREATIVITY AND PRODUCTIVITY","hero_heading_1":"Transform","hero_heading_2":"Your Thinking with","hero_heading_3":"Visual AI Tools","hero_description":"Visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.","discover_categories":"Discover Our AI Tool Categories","stat_tools":"AI Tools","stat_thinking":"Faster Thinking","stat_assistance":"AI Assistance","deep_dive_capabilities":"Dive deeper into more advanced features and possibilities","model_openai":"OpenAI GPT","model_openai_desc":"Leverage the power of GPT series for creative content generation and complex problem-solving.","model_anthropic":"Anthropic Claude","model_anthropic_desc":"Use Claude for technical and scientific content, nuanced understanding, reasoning, and exceptional coding capabilities","model_google":"Google Gemini","model_google_desc":"Access Google's multimodal AI capabilities for integrated text, image, and code understanding.","model_deepseek":"Deepseek AI","model_deepseek_desc":"Specialized models for nuanced understanding, reasoning, detailed explanations with minimal hallucinations.","model_mistral":"Mistral AI","model_mistral_desc":"Efficient models balancing performance and speed for everyday tasks and specialized domains.","model_cohere":"Cohere","model_cohere_desc":"Advanced models for text generation, summarization and semantic search applications.","infographic_howto_section_title":"How to Use the AI Infographic Creator?","infographic_howto_section_description":"Transform information into visually stunning infographics with these easy steps","infographic_howto_step1_title":"Input Content","infographic_howto_step1_description":"Enter a topic or paste text into the generation interface that you want to transform into an infographic","infographic_howto_step2_title":"Customize Settings","infographic_howto_step2_description":"Adjust additional settings as needed, such as generation language (default is English) and style options for your infographic","infographic_howto_step3_title":"Instant Infographic Generation","infographic_howto_step3_description":"Click generate and watch as AI creates a insightful infographic","infographic_howto_step4_title":"Download or Share","infographic_howto_step4_description":"Click the Download button to save your infographic or use the Share option to distribute it to others","infographic_howto_tips_title":"Tips for Great Infographics","infographic_howto_tip_1":"Focus on clear, concise information to create more effective visuals","infographic_howto_tip_2":"Try different visual styles to find the one that best communicates your data","infographic_howto_tip_3":"For complex topics, consider breaking content into multiple infographics focusing on different aspects","mindmap_howto_section_title":"How to Use the AI Mindmap Tool?","mindmap_howto_section_description":"Create informative and visually engaging mindmaps with these simple steps","mindmap_howto_step1_title":"Enter Your Topic","mindmap_howto_step1_description":"Depending on what's supported in the interface, enter a topic, paste some text, or provide a link—such as to a webpage or video—to start mapping your content","mindmap_howto_step2_title":"Customize Settings","mindmap_howto_step2_description":"Adjust options like generation language (default is English), mental models to guide your AI","mindmap_howto_step3_title":"Instant Mindmap Generation","mindmap_howto_step3_description":"Click generate and watch as AI creates a comprehensive mind map with multiple branches of ideas","mindmap_howto_step4_title":"Explore in AIFlow","mindmap_howto_step4_description":"Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support","mindmap_howto_tips_title":"Tips for Better Mindmaps","mindmap_howto_tip_1":"Use specific, focused topics for more detailed and useful mindmaps","mindmap_howto_tip_2":"Experiment with different settings, such as mental models to see varied perspectives on your topic","mindmap_howto_tip_3":"For complex subjects, try breaking them down into multiple mindmaps focusing on different aspects","slides_howto_section_title":"How to Use the FunBlocks AI Slides Creator?","slides_howto_section_description":"Elevate your content into engaging, visually stunning slides with these simple steps","slides_howto_step1_title":"Input Content","slides_howto_step1_description":"Enter a topic or paste text into the generation interface that you want to transform into an slides","slides_howto_step2_title":"Customize Settings","slides_howto_step2_description":"Adjust options like generation language (default is English) to suit your needs","slides_howto_step3_title":"Instant Slides Generation","slides_howto_step3_description":"Click generate and watch as AI creates a comprehensive slides","slides_howto_step4_title":"Present or Share Slides Online, or Continue Editing with FunBlocks AI Slides","slides_howto_step4_description":"After generating your slides, you can present them directly, share them online with others, or continue refining them using FunBlocks AI Slides for further customization.","slides_howto_tips_title":"Tips for Creating Effective Slides","slides_howto_tip_1":"Choose specific, focused topics or provide detailed text to create more informative and valuable slides.","slides_howto_tip_2":"Experiment with different settings to discover the best way to convey your ideas.","slides_howto_tip_3":"For complex subjects, consider using AIFlow for brainstorming first, then generate slides using the mindmap of explored ideas.","photo_howto_section_title":"How to Use the FunBlocks AI Photo Tools?","photo_howto_section_description":"Enhance your photography skills with personalized AI feedback in just a few steps","photo_howto_step1_title":"Upload Your Photo","photo_howto_step1_description":"Use the upload interface to select and upload a photograph you'd like feedback on","photo_howto_step2_title":"Customize Settings","photo_howto_step2_description":"Adjust options like generation language (default is English) to suit your needs","photo_howto_step3_title":"Instant Feedback Generation","photo_howto_step3_description":"Click generate and watch as AI creates a comprehensive mindmap or desired photo","photo_howto_step4_title":"Explore in AIFlow","photo_howto_step4_description":"Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support","photo_howto_tips_title":"Tips for Better Feedback","photo_howto_tip_1":"Upload high-quality images for more accurate and detailed feedback","photo_howto_tip_2":"Try different types of photographs to get varied insights about your photography style","photo_howto_tip_3":"Save feedback to track your progress and improvement over time","video_howto_section_title":"How to Use FunBlocks AI Vidoe Suammarization Tool?","video_howto_section_description":"Create informative and visually engaging video summarization mindmaps with these simple steps","video_howto_step1_title":"Enter Your Youtube Video Link","video_howto_step1_description":"Use the generation interface to provide a link to the Youtube video you want to map","video_howto_step2_title":"Customize Settings","video_howto_step2_description":"Adjust options like generation language (default is English) to suit your needs","video_howto_step3_title":"Instant Mindmap Generation","video_howto_step3_description":"Click generate and watch as AI creates a comprehensive mind map with multiple branches of ideas","video_howto_step4_title":"Explore in AIFlow","video_howto_step4_description":"Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support","mindsnap_howto_section_title":"How to Use MindSnap?","mindsnap_howto_section_description":"Transform any topic into visual insights using mental models with these simple steps","mindsnap_howto_step1_title":"Enter Your Topic","mindsnap_howto_step1_description":"Input any topic, question, or challenge you want to analyze through the lens of mental models","mindsnap_howto_step2_title":"Select a Mental Model (Optional)","mindsnap_howto_step2_description":"Choose a specific mental model or let AI select the most appropriate one for your topic","mindsnap_howto_step3_title":"Generate Visual Insights","mindsnap_howto_step3_description":"Click generate and watch as AI analyzes your topic through the selected mental model and creates a beautiful infographic","mindsnap_howto_step4_title":"Download or Share","mindsnap_howto_step4_description":"Save your infographic as an SVG file or share it directly with others","mindsnap_howto_tips_title":"Tips for Better Results","mindsnap_howto_tip_1":"Be specific with your topic to get more targeted and useful insights","mindsnap_howto_tip_2":"Try analyzing the same topic through different mental models to gain multiple perspectives","mindsnap_howto_tip_3":"For complex topics, break them down into smaller, more focused questions for deeper analysis","insightcards_howto_section_title":"How to Use InsightCards","insightcards_howto_section_description":"Creating powerful insight cards is simple with our intuitive process:","insightcards_howto_step1_title":"Enter Your Topic","insightcards_howto_step1_description":"Type in any concept, idea, or question you want to explore more deeply.","insightcards_howto_step2_title":"Select a Card Type","insightcards_howto_step2_description":"Choose from multiple insight frameworks based on your learning goals.","insightcards_howto_step3_title":"Generate Your InsightCard","insightcards_howto_step3_description":"Our AI analyzes your topic and creates a visually appealing insight card.","insightcards_howto_step4_title":"Share and Learn","insightcards_howto_step4_description":"Download your card, share it with others, or save it to your collection.","insightcards_howto_tips_title":"Tips for Better InsightCards","insightcards_howto_tip_1":"Be specific with your topic to get more focused and meaningful insights","insightcards_howto_tip_2":"Try different card types on the same topic to gain multiple perspectives","insightcards_howto_tip_3":"Use the generated insights as starting points for deeper exploration and discussion","app_slogan_promptoptimizer":"Unlock AI Potential: Optimize Your Prompts","app_one_sentence_promptoptimizer":"Unlock the full potential of AI with clearer, more effective prompts. Get better results, faster","ai_promptoptimizer_desc":"Unlock the full potential of AI with clearer, more effective prompts. Get better results, faster","prompt-optimizer_howto_section_title":"Optimizing Your AI Prompts Made Easy","prompt-optimizer_howto_section_description":"Discover how to refine your AI interactions with these straightforward steps:","prompt-optimizer_howto_step1_title":"Enter Your Initial Prompt","prompt-optimizer_howto_step1_description":"Start by inputting the original prompt or question you'd like to optimize. This can range from simple inquiries to complex instructions.","prompt-optimizer_howto_step2_title":"Select Your Language","prompt-optimizer_howto_step2_description":"Choose the language for your optimized prompt from our list of available options.","prompt-optimizer_howto_step3_title":"Get Your Optimized Prompt","prompt-optimizer_howto_step3_description":"Our AI Prompt Optimizer will analyze your input and generate a refined prompt for you.","prompt-optimizer_howto_step4_title":"Test and Refine Further","prompt-optimizer_howto_step4_description":"Put your optimized prompt to the test with your AI system, making any necessary adjustments based on the results.","prompt-optimizer_howto_tips_title":"Tips for Crafting Effective Prompts","prompt-optimizer_howto_tip_1":"Clearly define your desired outcome or goal in your initial prompt.","prompt-optimizer_howto_tip_2":"Avoid including detailed steps in your initial prompt to allow for the full potential of LLM.","prompt-optimizer_howto_tip_3":"Use the generated prompt as a starting point and iterate for further optimization.","case_studies_badge":"Real-World Applications","case_studies_title":"Success Stories: {{appname}} in Action","case_studies_cta_text":"Ready to create your own success story?","case_studies_cta_button":"Start Creating Now","testimonials_title":"What Our Users Say","testimonials_description":"Join thousands of users who have transformed their experience with {{appname}}.","testimonials_rating":"{{rating}} average rating","testimonials_users":"{{users}}+ active users","comparison_winner":"Best Overall Solution","comparison_value":"Best Value","case_study_1_title":"Educational Institution Transformation","case_study_1_industry":"Higher Education","case_study_1_challenge":"A leading university struggled with helping students visualize complex concepts across multiple disciplines, resulting in lower comprehension and retention rates.","case_study_1_solution":"Implemented a comprehensive suite of FunBlocks AI tools across departments: MarzanoBrain and BloomBrain for creating structured learning materials, Slides for generating interactive presentations, and Brainstorming, Critical Thinking, and Creative Thinking tools to develop students' cognitive abilities.","case_study_1_results":"Significant improvement in student comprehension and retention rates. Students demonstrated enhanced critical thinking skills and ability to connect concepts across disciplines. Faculty reported more engaging classroom discussions and higher quality student work.","case_study_2_title":"Innovation Thinking Enhancement","case_study_2_industry":"Product Design \u0026 Marketing","case_study_2_challenge":"A multinational company struggled with fostering innovative thinking among their product design and marketing teams, resulting in predictable solutions and declining market differentiation.","case_study_2_solution":"Integrated FunBlocks AI Brainstorming, MindKit, MindSnap, OKR Assistant, Task Planner and other FunBlocks AI tools into their ideation process. Teams used these tools to explore multiple perspectives, challenge assumptions, and visualize connections between seemingly unrelated concepts.","case_study_2_results":"Teams developed more innovative product designs and marketing campaigns that resonated with customers. The company reported increased creative output, more diverse solution sets, and improved cross-team collaboration on complex projects.","case_studies_description":"See how organizations across different sectors have leveraged FunBlocks AI tools to solve real challenges and achieve measurable results.","research_title":"Research-Backed Approach","research_description":"Our tools are built on solid scientific foundations and proven cognitive principles to maximize learning and productivity.","research_area_1_title":"Cognitive Load Theory","research_area_1_description":"Our visual tools reduce cognitive load by organizing information spatially, allowing users to process complex concepts more efficiently while minimizing mental effort.","research_area_2_title":"Visual Learning Efficacy","research_area_2_description":"Research shows visual learning can improve understanding by up to 400% and retention by 38% compared to text-only learning, making complex information more accessible and memorable.","research_area_3_title":"Mental Models \u0026 Frameworks","research_area_3_description":"Our tools leverage established mental models and educational frameworks that help structure thinking, improve problem-solving capabilities, and enhance conceptual understanding across disciplines.","research_area_4_title":"AI-Enhanced Learning","research_area_4_description":"Studies show that AI-assisted learning tools can personalize the educational experience, provide adaptive feedback, and improve outcomes across diverse learning styles and contexts.","testimonial_1_content":"MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!","testimonial_1_role":"Medical Student","testimonial_2_content":"As a product manager, I need to communicate complex ideas to different teams. The AI Infographic Generator has become my secret weapon for creating visually stunning concept maps and comparison charts in minutes instead of hours.","testimonial_2_role":"Product Manager","testimonial_3_content":"I've tried many brainstorming tools, but FunBlocks AI Brainstorming Assistant is in a league of its own. It doesn't just generate ideas - it helps structure and refine them in ways I wouldn't have considered. It's like having a creative thinking partner available 24/7.","testimonial_3_role":"Creative Director","common_questions":"Common Questions","faq_description":"Find answers to commonly asked questions about FunBlocks AI tools and how they can help you.","faq_category_general":"About FunBlocks AI","faq_category_features":"Tools \u0026 Features","faq_category_usage":"Use Cases \u0026 Applications","faq_category_technical":"Technical \u0026 Support","faq_need_more_help":"Need More Help?","faq_support_text":"If you have questions not covered here, our support team is ready to help. You can also check our detailed documentation for more information.","contact_support":"Contact Support","view_documentation":"View Documentation","llm_support_notes":"Access all these models and more through a single, unified interface. Switch between models seamlessly based on your task requirements.","category_mindmap_title":"Mindmaps","category_mindmap_desc":"AI-powered mind maps for learning, planning, and creative thinking","category_infographics_title":"Infographics","category_infographics_desc":"Transform ideas into beautiful visual content with AI","category_slides_title":"Slides","category_slides_desc":"Create professional presentations in seconds with AI","category_images_title":"Images","category_images_desc":"AI-powered image generation and enhancement tools","category_text_title":"Text","category_text_desc":"AI-powered text content generation","examples_showcase_title":"See Our Tools in Action","examples_showcase_desc":"Explore real examples of what you can create with our AI-powered tools","example_mindmap_title":"AI Mindmap Example","example_mindmap_desc":"Visualize complex topics with our AI Mindmap tool. This example shows a mindmap about the book \"The Great Gatsby\".","example_infographic_title":"AI Infographic Example","example_infographic_desc":"Create beautiful infographics instantly. This example shows a witty and attractive infographic generated with InsightCards.","example_slides_title":"AI Slides Example","example_slides_desc":"Generate professional presentations in seconds. This example shows a competitor analysis presentation.","example_mindsnap_title":"AI MindSnap Example","example_mindsnap_desc":"Transform topics into visual mental models. This example shows a SWOT analysis.","lesson-plans_howto_section_title":"How to Use the AI Lesson Plans Tool?","lesson-plans_howto_section_description":"Create professional, framework-based lesson plans with these simple steps","lesson-plans_howto_step1_title":"Enter Your Teaching Topic","lesson-plans_howto_step1_description":"Input your lesson topic, subject area, or learning objectives to start creating your lesson plan","lesson-plans_howto_step2_title":"Select Teaching Framework","lesson-plans_howto_step2_description":"Choose from proven pedagogical frameworks like Bloom's Taxonomy, Marzano's Strategies, or ADDIE Model","lesson-plans_howto_step3_title":"Customize Settings","lesson-plans_howto_step3_description":"Adjust educational level, duration, and specific requirements for your classroom context","lesson-plans_howto_step4_title":"Generate \u0026 Refine","lesson-plans_howto_step4_description":"Get a comprehensive lesson plan with objectives, activities, assessments, and resources that you can further customize","lesson-plans_howto_tips_title":"Tips for Better Lesson Plans","lesson-plans_howto_tip_1":"Be specific about learning objectives and target audience for more tailored lesson plans","lesson-plans_howto_tip_2":"Experiment with different teaching frameworks to find the best approach for your content","lesson-plans_howto_tip_3":"Consider your classroom environment and available resources when customizing the generated plan","dok-assessment_howto_section_title":"How to Use FunBlocks AI Assessment","dok-assessment_howto_section_description":"Create professional DOK-based assessments in just a few simple steps. Our AI handles the complexity while you focus on teaching.","dok-assessment_howto_step1_title":"Enter Your Teaching Topic","dok-assessment_howto_step1_description":"Input any subject, lesson, or learning objective you want to assess. Be as specific or general as you like.","dok-assessment_howto_step2_title":"AI Analyzes Content","dok-assessment_howto_step2_description":"Our AI identifies key concepts, determines optimal DOK distribution, and plans comprehensive assessment coverage.","dok-assessment_howto_step3_title":"Review Generated Assessment","dok-assessment_howto_step3_description":"Examine the complete assessment with questions across all DOK levels, rubrics, and implementation guidelines.","dok-assessment_howto_step4_title":"Customize and Implement","dok-assessment_howto_step4_description":"Modify questions, adjust difficulty levels, and adapt the assessment to your specific teaching context and standards.","dok-assessment_howto_tips_title":"Pro Tips for Better Assessments","dok-assessment_howto_tip_1":"Be specific about grade level and subject area for more targeted questions","dok-assessment_howto_tip_2":"Specify assessment type (formative/summative) for appropriate question styles","dok-assessment_howto_tip_3":"Include learning objectives to ensure perfect alignment with your curriculum","example_try_tool":"Try this tool"},"graphics":{"hero_badge":"AI GRAPHICS GENERATOR","hero_heading":"Transform Your Ideas into Beautiful Graphics with AI","hero_description":"Create stunning infographics, flowcharts, data charts, and visual content in seconds - no design skills required.","hero_start_button":"Get Started","hero_examples_button":"See Examples","hero_metric_1_number":"10x","hero_metric_1_text":"Faster than traditional design","hero_metric_2_number":"50+","hero_metric_2_text":"Visual styles and formats","hero_metric_3_number":"75%","hero_metric_3_text":"Improved Information Retention","graphics_visual_types_title":"Insightful Infographics","graphics_visual_1_title":"ChatGraphics","graphics_visual_2_title":"Infographics","graphics_visual_3_title":"Witty Card","graphics_visual_1_text":"Experience a new level of efficiency in information acquisition, surpassing traditional text dialogues like ChatGPT with clarity and simplicity.","graphics_visual_2_text":"Unlock deeper insights and enjoyment with expertly crafted prompts that guide the LLM for enhanced understanding.","graphics_visual_3_text":"Analyze given theme in depth to discover new insights, create simple, insightful diagrams, and produce attractive social media cards ready to share.","graphics_examples_title":"What You Can Create","graphics_example_1_title":"Data Visualization","graphics_example_1_description":"Turn complex data into intuitive, beautiful charts that tell a story at a glance.","graphics_example_2_title":"Process Diagrams","graphics_example_2_description":"Illustrate multi-step processes with clear, engaging visuals that improve understanding.","graphics_example_3_title":"Conceptual Flowcharts","graphics_example_3_description":"Map out relationships, hierarchies, and decision flows with professional-looking diagrams.","graphics_example_4_title":"Insightful Cards","graphics_example_4_description":"Present topics with elegant visuals that spotlight core insights.","examples_subtitle":"Ready to create your own stunning graphics?","examples_button":"Start Creating Now","graphics_intro_description":"FunBlocks AI Graphics combines artificial intelligence with design principles to help you create professional-quality visual content in minutes instead of hours.","comparison_title":"AI Graphics vs. Traditional Design","comparison_header_feature":"Feature","comparison_header_traditional":"Traditional Design","comparison_header_ai":"AI Graphics","comparison_time":"Creation Time","comparison_time_traditional":"Hours to days","comparison_time_ai":"Minutes","comparison_skills":"Skills Required","comparison_skills_traditional":"Advanced design expertise","comparison_skills_ai":"No special skills needed","comparison_design":"Design Quality","comparison_design_traditional":"Varies based on skill","comparison_design_ai":"Consistently professional","comparison_variety":"Visual Variety","comparison_variety_traditional":"Limited by time \u0026 expertise","comparison_variety_ai":"Unlimited variations","comparison_iteration":"Iteration Speed","comparison_iteration_traditional":"Slow, manual process","comparison_iteration_ai":"Instant updates","graphics_benefits_title":"Benefits for Everyone","graphics_benefits_pro_title":"For Professionals","graphics_benefit_pro_1":"Save hours on routine design tasks","graphics_benefit_pro_2":"Create consistent brand visuals","graphics_benefit_pro_3":"Generate multiple design variations","graphics_benefit_pro_4":"Focus on strategy, not execution","graphics_benefits_business_title":"For Businesses","graphics_benefit_business_1":"Improve presentation quality","graphics_benefit_business_2":"Enhance audience engagement","graphics_benefit_business_3":"Communicate complex ideas effectively","graphics_benefit_business_4":"Maintain brand consistency","graphics_use_cases_title":"Popular Use Cases","graphics_use_cases_description":"Our AI Graphics tool is versatile enough to handle a wide range of visualization needs across industries and applications.","graphics_scenarios_title":"Common Scenarios","graphics_scenario_1_title":"Business Presentations","graphics_scenario_1_text":"Create impactful slides with data visualizations that drive your point home.","graphics_scenario_2_title":"Social Media Content","graphics_scenario_2_text":"Design shareable infographics that boost engagement and explain complex topics.","graphics_scenario_3_title":"Project Documentation","graphics_scenario_3_text":"Develop clear process flowcharts and documentation visuals for teams.","graphics_scenario_4_title":"Educational Materials","graphics_scenario_4_text":"Build learning aids that make complex concepts easier to understand and remember.","graphics_users_title":"Who Uses Our Tool","graphics_user_1_title":"Marketers \u0026 Content Creators","graphics_user_1_text":"Create engaging visuals for campaigns, social media, and blog content.","graphics_user_2_title":"Business Professionals","graphics_user_2_text":"Enhance presentations and reports with professional-quality graphics.","graphics_user_3_title":"Educators \u0026 Trainers","graphics_user_3_text":"Develop impactful visual learning materials and course content.","graphics_user_4_title":"Entrepreneurs \u0026 Startups","graphics_user_4_text":"Build professional visuals for pitches and marketing without a design team.","cta_title":"Start Creating Beautiful Graphics Today","cta_description":"Join thousands of professionals who are saving time and creating stunning visuals with our AI Graphics tool.","cta_button":"Create Your First Graphic","graphics_faq_title":"Frequently Asked Questions","graphics_faq_1_q":"What is FunBlocks AI Graphics?","graphics_faq_1_a":"FunBlocks AI Graphics is a tool that uses large language models (LLM) technology to generate interesting and insightful SVG infographic cards. It can help users create shareable content suitable for social media, presentations, or personal notes.","graphics_faq_2_q":"What can FunBlocks AI Graphics do?","graphics_faq_2_a":"FunBlocks AI Graphics can generate interesting and insightful SVG infographic cards suitable for social media, presentations, personal notes, or just for fun.","graphics_faq_3_q":"Do I need design skills to use this tool?","graphics_faq_3_a":"Not at all! Our AI handles the design aspects. You just need to describe what you want, and the AI will create professional graphics for you.","graphics_faq_4_q":"What types of graphics can I create?","graphics_faq_4_a":"You can create a wide variety of visuals including infographics, flowcharts, process diagrams, data charts, timelines, comparison tables, and more.","graphics_faq_5_q":"How do I input data for charts and graphs?","graphics_faq_5_a":"You can type or paste your data directly in the prompt.","graphics_faq_6_q":"Can I save my graphics?","graphics_faq_6_a":"Yes, all your creations are saved to your account where you can access, download, share at any time.","graphics_faq_7_q":"Is there a limit to how many graphics I can create?","graphics_faq_7_a":"Free accounts have a daily limit, while paid subscribers enjoy more quotas or unlimited graphics creation.","graphics_feature_1_title":"AI-Driven Insights","graphics_feature_1_text":"Utilize advanced LLM technology to deeply analyze topics and provide unique perspectives.","graphics_feature_2_title":"Creativity Booster","graphics_feature_2_text":"Break through thinking limitations, gain novel viewpoints, and inspire unlimited creative inspiration.","graphics_feature_3_title":"Humor and Wisdom Coexist","graphics_feature_3_text":"Perfect combination of profound insights and humorous expression, making thinking full of fun.","graphics_feature_4_title":"Fun Cards Easy to Share","graphics_feature_4_text":"Generate eye-catching content cards, easily stand out on social platforms.","deep_informative_content":"Deep, Informative, Extraordinary Content","funblocks_ai_insights_unique":"FunBlocks AI Graphics is not an ordinary content generation tool. It can create truly deep, valuable, and unique content:","deep_insights":"Deep Insights: AI deeply analyzes topics, providing unique perspectives","quality_content":"Quality Content: Combine vast knowledge to generate high-quality content","innovative_thinking":"Innovative Thinking: Break through conventions, inspire novel viewpoints","humor_integration":"Humor Integration: Cleverly blend wisdom and humor, making content more interesting","fun_social_tool":"Fun, Novel, Thought-Provoking Social Tool","funblocks_ai_insights_thought_stimulator":"FunBlocks AI Graphics is not just a content generation tool, it's a catalyst for your thinking, a booster for your social interactions:","endless_fun":"Endless Fun","endless_fun_desc":"Every use is an exploration full of surprises, making thinking interesting and enjoyable.","novel_format":"Novel and Concise Format","novel_format_desc":"Refined card format, concise and clear, yet containing rich content, at a glance.","stimulate_thinking":"Stimulate Thinking","stimulate_thinking_desc":"Break through conventional thinking limitations, open up innovative perspectives, making your ideas more colorful.","enhance_social_interaction":"Enhance Social Interaction","enhance_social_interaction_desc":"Share unique insights, become a thought leader in your friend circle, adding depth to social interactions.","why_infographic_cards":"Why choose SVG infographic cards?","why_infographic_cards_answer":"Infographic cards are easy to understand, easy to share, and highly inspiring. The SVG format is very flexible, and large language models are good at divergent thinking, so the final SVG infographic cards generated by AI Graphics are highly inspiring, beneficial for learning, sharing, and inspiring creativity.","how_to_use_ai_insights":"How to use AI Graphics?","how_to_use_ai_insights_answer":"It's simple to use: enter a topic or question, select the card type, and then let AI generate content for you.","difference_of_three_modes":"What are the differences between the three modes?","difference_of_three_modes_answer":"The main difference between the three modes lies in their usage scenarios.\n\nThe **Graph Chat** mode is primarily used for conversational chart generation, similar to ChatGPT and Claude. You can interact with AI (large language model), but the difference is that FunBlocks AI Graphics ultimately presents the generated content in the form of an infographic.\n\nThe **Make Graph** mode is mainly used to generate infographics from existing text, files, or web content.\n\nThe **Witty Card** mode further incorporates powerful prompts, applying the powerful insights of LLM to various scenarios, allowing you to generate highly insightful and interesting cards.","can_content_be_edited":"Can the generated content be edited?","can_content_be_edited_answer":"No, the generated content cannot be edited, but you can regenerate it. This ensures that all content is generated by AI.","can_i_share_cards":"Can I share the cards I create?","can_i_share_cards_answer":"Of course! You can easily share the cards you create. We provide sharing functionality that allows you to share amazing cards on various social media platforms or directly via links.","is_content_true":"Is the content generated by AI Graphics true?","is_content_true_answer":"The content generated by AI Graphics is based on large language models (LLM). While it cannot guarantee complete truth, it can provide certain reference value.","is_content_original":"Is the content generated by AI Graphics original?","is_content_original_answer":"The content generated by AI Graphics is based on large language models (LLM). While it cannot guarantee complete originality, based on the generation characteristics of LLM, most of the content can be considered original.","can_other_ai_generate_cards":"Can ChatGPT, Claude, Gemini, and other large model applications generate similar cards?","can_other_ai_generate_cards_answer":"Yes, ChatGPT, Claude, Gemini, and other large model applications can also generate similar cards, but you need to write prompts yourself, and even write code to process and render the generated code, while FunBlocks AI Graphics automatically generates cards.","difference_from_other_ai":"What's the difference between FunBlocks AI Graphics and ChatGPT, Claude, Gemini?","difference_from_other_ai_answer":"FunBlocks AI Graphics automatically generates cards. You don't need to write prompts, nor do you need to process and render generated code. You just need to enjoy the process of generating cards.","in_common_as_chatgpt":"What do FunBlocks AI Graphics and Claude, ChatGPT have in common?","in_common_as_chatgpt_answer":"FunBlocks AI Graphics and Claude, ChatGPT are both generative applications based on large language models (LLM) that can answer user questions or generate content according to user requests. However, FunBlocks AI Graphics ultimately generates SVG cards, which are easier to understand, highly inspiring, and beneficial for learning, sharing, and inspiring creativity, while ChatGPT and similar applications generate text, which is suitable for more scenarios.","is_ai_insights_free":"Do I need to pay to use AI Graphics?","is_ai_insights_free_answer":"We offer free and paid plans. You can try it for free, but you can also subscribe to our paid plans to enjoy advanced features and more usage quota. Please check our pricing page for details.","case_studies_badge":"Real-World Applications","case_studies_title":"Success Stories: AI Graphics in Action","case_studies_description":"See how professionals across different industries have used our AI Graphics Generator to solve real communication challenges and achieve measurable results.","case_studies_cta_text":"Ready to create your own success story?","case_studies_cta_button":"Start Creating Now","case_study_1_title":"Data Visualization for Executive Reports","case_study_1_industry":"Business Intelligence","case_study_1_challenge":"A Fortune 500 company needed to transform complex quarterly performance data into easily digestible visuals for executive meetings, but their design team was overwhelmed with requests.","case_study_1_solution":"Using our AI Graphics Generator, they created professional data charts and infographics in minutes, highlighting key metrics and trends with minimal effort.","case_study_1_results":"Meeting preparation time reduced by 60%, executive comprehension improved by 45%, and design team freed up for strategic projects.","case_study_2_title":"Educational Concept Visualization","case_study_2_industry":"Education","case_study_2_challenge":"A university professor struggled to explain complex scientific processes to undergraduate students, finding traditional textbook explanations insufficient for visual learners.","case_study_2_solution":"Created a series of process-based infographics using our AI tool that visually broke down complex concepts into clear, sequential steps with visual cues.","case_study_2_results":"Student comprehension increased by 32%, test scores improved by 27%, and course satisfaction ratings rose from 3.2/5 to 4.7/5.","case_study_3_title":"Software Architecture Documentation","case_study_3_industry":"Software Development","case_study_3_challenge":"A tech startup needed to document their complex microservices architecture for new developers but lacked dedicated technical writers or designers.","case_study_3_solution":"Used the AI Flowchart Generator to create comprehensive system diagrams showing service relationships, data flows, and integration points with minimal technical input.","case_study_3_results":"Onboarding time for new developers reduced by 40%, documentation maintenance simplified, and system understanding improved across non-technical teams.","case_study_4_title":"Social Media Content Strategy","case_study_4_industry":"Digital Marketing","case_study_4_challenge":"A digital marketing agency needed to create engaging, data-rich visuals for social media campaigns but was constrained by limited design resources and tight deadlines.","case_study_4_solution":"Implemented our AI Graphics Generator to rapidly produce customized infographics and data visualizations tailored to each client's brand and campaign goals.","case_study_4_results":"Content production time decreased by 75%, engagement rates increased by 38% compared to text-only posts, and client satisfaction scores improved significantly.","research_title":"Research-Backed Visualization","research_description":"Our AI Graphics Generator is built on solid scientific foundations and evidence-based principles from the fields of cognitive psychology, data visualization, and information design.","research_button_science":"Evidence-Based Design","research_button_data":"Data Visualization Research","research_area_1_title":"Visual Information Processing","research_area_1_description":"Our AI Graphics Generator leverages research on how humans process visual information more efficiently than text. Studies show that visuals are processed 60,000 times faster than text and improve comprehension by up to 400%.","research_area_2_title":"Data Visualization Principles","research_area_2_description":"Our AI implements established data visualization principles from leading researchers like Edward Tufte and Stephen Few, ensuring that complex data is presented with clarity, precision, and minimal cognitive load.","research_area_3_title":"Cognitive Load Theory","research_area_3_description":"Our AI Graphics Generator applies cognitive load theory to create visuals that reduce extraneous cognitive load, allowing users to focus on understanding the content rather than deciphering complex presentations.","research_area_4_title":"Information Design","research_area_4_description":"Our system incorporates principles of information design and visual hierarchy to ensure that the most important information stands out and complex relationships are clearly communicated.","testimonials_title":"What Our Users Say","testimonials_description":"Join thousands of professionals who have transformed their visual communication with our AI Graphics Generator.","testimonials_rating":"4.8 average rating","testimonials_users":"15,000+ active users","testimonial_1_content":"As a marketing manager with no design background, I was struggling to create professional infographics for our campaigns. The AI Graphics Generator changed everything! Now I can create stunning visuals in minutes that would have taken our design team days. Our engagement rates have increased by 45% since we started using these graphics.","testimonial_1_role":"Marketing Manager","testimonial_2_content":"The flowchart generator is a game-changer for our development team. We used to spend hours creating system architecture diagrams, but now we can generate them in minutes. The AI somehow understands exactly what we need from just a text description. It's like having a professional designer on call 24/7.","testimonial_2_role":"Software Architect","testimonial_3_content":"As a data analyst, I need to present complex findings to non-technical stakeholders regularly. The AI Graphics Generator helps me transform boring spreadsheets into compelling data visualizations that tell a story. My presentations now generate much more engagement and understanding from executives.","testimonial_3_role":"Data Analyst","testimonial_4_content":"I use the AI Graphics Generator for my classroom materials, and my students love it! The visuals help explain complex concepts in a way that text alone never could. The best part is how quickly I can create custom graphics for each lesson - what used to take hours now takes minutes.","testimonial_4_role":"High School Teacher","testimonial_5_content":"The SVG code generator is brilliant for our web development team. We can quickly create custom graphics that are lightweight and responsive. The code is clean and well-structured, making it easy to integrate into our projects. This tool has significantly reduced our dependency on stock graphics.","testimonial_5_role":"Front-end Developer","testimonial_6_content":"Our research team uses the AI Graphics Generator to create visualizations for scientific publications. The quality and accuracy of the graphics are impressive, and they save us countless hours that we can now dedicate to actual research instead of creating figures. A must-have tool for any research group.","testimonial_6_role":"Research Director","graphics_comparison_title":"How We Compare","graphics_comparison_description":"See how FunBlocks AI Graphics Generator compares to traditional design tools and other solutions in the market.","graphics_comparison_winner":"Best Overall Solution","graphics_comparison_value":"Highest Time-Saving Value","comparison_funblocks":"FunBlocks AI Graphics","comparison_traditional":"Traditional Design Tools","comparison_other_ai":"Other AI Tools","comparison_templates":"Template Services","comparison_feature_1":"No Design Skills Required","comparison_feature_1_tooltip":"Can create professional graphics without any design experience","comparison_feature_2":"One-Click Generation","comparison_feature_2_tooltip":"Creates complete graphics with a single text prompt","comparison_feature_3":"Customized to Content","comparison_feature_3_tooltip":"Automatically adapts design to match the specific content","comparison_feature_4":"Infographic Generation","comparison_feature_4_tooltip":"Creates data-rich visual stories from text descriptions","comparison_feature_5":"Flowchart Creation","comparison_feature_5_tooltip":"Generates process flows and diagrams from descriptions","comparison_feature_6":"Data Visualization","comparison_feature_6_tooltip":"Creates charts and graphs from data or descriptions","comparison_feature_7":"SVG Code Generation","comparison_feature_7_tooltip":"Produces editable SVG code for further customization","comparison_feature_8":"Learning Curve","comparison_feature_8_tooltip":"Easy to learn and use immediately","comparison_feature_9":"Speed of Creation","comparison_feature_9_tooltip":"Creates graphics in minutes rather than hours","comparison_feature_10":"Design Principles Applied","comparison_feature_10_tooltip":"Automatically applies professional design principles"}}},"initialLocale":"zh","ns":["common","graphics"],"userConfig":{"i18n":{"defaultLocale":"en","locales":["en","zh"]},"localePath":"/Users/<USER>/workspace/wise-card/public/locales","trailingSlash":true,"default":{"i18n":{"defaultLocale":"en","locales":["en","zh"]},"localePath":"/Users/<USER>/workspace/wise-card/public/locales","trailingSlash":true}}}},"__N_SSG":true},"page":"/graphics","query":{},"buildId":"nm9AgT6Nhv6t9TwNeg-eH","assetPrefix":"/aitools","runtimeConfig":{"basePath":"/aitools","basePathGraphics":"/aitools/graphics","basePathMindmap":"/aitools/mindmap"},"isFallback":false,"gsp":true,"locale":"zh","locales":["en","zh"],"defaultLocale":"en","scriptLoader":[]}</script></body></html>