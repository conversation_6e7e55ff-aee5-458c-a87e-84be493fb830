"use strict";
(() => {
var exports = {};
exports.id = 5525;
exports.ids = [5525];
exports.modules = {

/***/ 4795:
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   "getServerSideProps": () => (/* binding */ getServerSideProps)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(997);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6689);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(968);
/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1853);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2210);
/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6134);
/* harmony import */ var _utils_apiUtils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(7597);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1377);
/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(5460);
/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(4558);
/* harmony import */ var next_config__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_config__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _utils_svgUtils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(9399);
/* harmony import */ var _components_CardGenerator_ArtifactDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(5655);
/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(6611);
/* harmony import */ var _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(1082);
/* harmony import */ var _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1692);
/* harmony import */ var _components_Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(283);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(7426);
/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(5744);
/* harmony import */ var _components_Home_Main__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(906);
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__, _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__, _components_CardGenerator_ArtifactDisplay__WEBPACK_IMPORTED_MODULE_9__, qrcode_react__WEBPACK_IMPORTED_MODULE_10__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_11__, _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_12__, _components_Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_13__, _components_Home_Main__WEBPACK_IMPORTED_MODULE_16__]);
([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__, _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__, _components_CardGenerator_ArtifactDisplay__WEBPACK_IMPORTED_MODULE_9__, qrcode_react__WEBPACK_IMPORTED_MODULE_10__, _contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_11__, _components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_12__, _components_Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_13__, _components_Home_Main__WEBPACK_IMPORTED_MODULE_16__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);



















const Tools = ({ app , artifact , svgRef  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)("common");
    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useToast)();
    const { openShareModal  } = (0,_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_11__/* .useShareModal */ .K)();
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {
        spacing: 4,
        children: [
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {
                size: "sm",
                colorScheme: "whiteAlpha",
                variant: "link",
                // onClick={() => handleDownload(svgRef, toast)}
                onClick: async ()=>{
                    setIsLoading("download"); // 开始加载
                    try {
                        await (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__/* .handleDownload */ .gp)(svgRef, toast);
                    } finally{
                        setIsLoading(null); // 结束加载
                    }
                },
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    style: {
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        columnGap: 6
                    },
                    children: [
                        t("download"),
                        " ",
                        isLoading === "download" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Spinner, {
                            size: "sm"
                        })
                    ]
                })
            }),
            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {
                size: "sm",
                colorScheme: "blue",
                onClick: async ()=>{
                    setIsLoading("share"); // 开始加载
                    try {
                        await (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__/* .handleShare */ .Bf)(app, artifact?._id, artifact?.title, svgRef, openShareModal, toast);
                    } finally{
                        setIsLoading(null); // 结束加载
                    }
                },
                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
                    style: {
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        columnGap: 6
                    },
                    children: [
                        t("share"),
                        " ",
                        isLoading === "share" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Spinner, {
                            size: "sm"
                        })
                    ]
                })
            })
        ]
    });
};
const Features = ({ app , appTranslationFile  })=>{
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)(appTranslationFile);
    return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.List, {
        spacing: 3,
        textAlign: "left",
        children: ([
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.mindmap */ .IF.mindmap,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.brainstorming */ .IF.brainstorming,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.decision */ .IF.decision,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.graphics */ .IF.graphics,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.infographic */ .IF.infographic,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.dreamlens */ .IF.dreamlens,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.art */ .IF.art,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.photo */ .IF.photo,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.reading */ .IF.reading,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.startupmentor */ .IF.startupmentor,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.businessmodel */ .IF.businessmodel,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.okr */ .IF.okr,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.poetic */ .IF.poetic,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.horoscope */ .IF.horoscope,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.movie */ .IF.movie,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.feynman */ .IF.feynman,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.bloom */ .IF.bloom,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.solo */ .IF.solo,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.dok */ .IF.dok,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.marzano */ .IF.marzano,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.erase */ .IF.erase,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.avatar */ .IF.avatar,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.imageEditor */ .IF.imageEditor,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.layeredExplanation */ .IF.layeredExplanation,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.promptOptimizer */ .IF.promptOptimizer,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.lessonPlanner */ .IF.lessonPlanner,
            _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.dokAssessment */ .IF.dokAssessment
        ].includes(app) ? [
            1,
            2,
            3
        ] : [
            1,
            2,
            3,
            4
        ]).map((i)=>{
            return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ListItem, {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ListIcon, {
                        as: _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__.CheckIcon,
                        color: "green.300"
                    }),
                    t([
                        _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.graphics */ .IF.graphics,
                        _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.infographic */ .IF.infographic
                    ].includes(app) && "graphics_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.onePageSlides */ .IF.onePageSlides && "one_slides_pro_benefit_" + i || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.promptOptimizer */ .IF.promptOptimizer && "prompt_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.lessonPlanner */ .IF.lessonPlanner && "lesson_plans_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.teachingSlides */ .IF.teachingSlides && "teaching_slides_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.dokAssessment */ .IF.dokAssessment && "dok_assessment_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.imageEditor */ .IF.imageEditor && "editor_feature_" + i + "_text" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.sketch */ .IF.sketch && "feature_" + i + "_text" || appTranslationFile + "_feature_" + i + "_text")
                ]
            }, i + "");
        })
    });
};
const SharePage = ({ cardData , artifact , app , appTranslationFile  })=>{
    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();
    const { t  } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)("common");
    const { basePath  } = next_config__WEBPACK_IMPORTED_MODULE_8___default()().publicRuntimeConfig;
    const { isOpen , onOpen , onClose  } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)();
    const [imageUrl, setImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
    const svgRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
    const [isMobile] = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useMediaQuery)("(max-width: 768px)");
    const shareUrl =  false ? 0 : "";
    const tools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[].concat(...Object.values(_components_Home_Main__WEBPACK_IMPORTED_MODULE_16__/* .aiTools */ .C)), []);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        if (artifact?.type != "svg") return;
        const blob = new Blob([
            (0,_utils_svgUtils__WEBPACK_IMPORTED_MODULE_17__/* .preprocessSvg */ .w)(artifact.content)
        ], {
            type: "image/svg+xml"
        });
        const url = URL.createObjectURL(blob);
        setImageUrl(url);
    }, [
        artifact
    ]);
    console.log("artifact..............", artifact);
    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_contexts_ShareModalContext__WEBPACK_IMPORTED_MODULE_11__/* .ShareModalProvider */ .$, {
        children: [
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("title", {
                        children: cardData.title
                    }, "title"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "keywords",
                        content: cardData.keywords || t("meta.keywords")
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "description",
                        content: cardData.description
                    }, "description"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:title",
                        content: cardData.title
                    }, "og:title"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:description",
                        content: cardData.description
                    }, "og:description"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:image",
                        content: cardData.imageUrl
                    }, "og:image"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        property: "og:url",
                        content: cardData.shareUrl
                    }, "og:url"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:card",
                        content: "summary_large_image"
                    }, "twitter:card"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:title",
                        content: cardData.title
                    }, "twitter:title"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:description",
                        content: cardData.description
                    }, "twitter:description"),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("meta", {
                        name: "twitter:image",
                        content: cardData.imageUrl
                    }, "twitter:image")
                ]
            }),
            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {
                minHeight: "100vh",
                bgGradient: "linear(to-b, purple.500, blue.500)",
                color: "white",
                py: 6,
                children: [
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Container, {
                        maxW: "container.xl",
                        pt: {
                            base: 4,
                            md: 10
                        },
                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {
                            spacing: 4,
                            align: "center",
                            children: [
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Heading, {
                                    as: "h1",
                                    size: {
                                        base: "lg",
                                        md: "2xl"
                                    },
                                    textAlign: "center",
                                    mb: 2,
                                    children: t("app_slogan_" + app.replaceAll("-", "").toLowerCase())
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {
                                    fontSize: {
                                        base: "lg",
                                        md: "xl"
                                    },
                                    fontWeight: "bold",
                                    textAlign: "center",
                                    mb: 2,
                                    children: t("app_one_sentence_" + app.replaceAll("-", "").toLowerCase())
                                }),
                                artifact?.type === "flow" && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {
                                    style: {
                                        width: "100%",
                                        maxWidth: 1040,
                                        height: isMobile ? 300 : 680,
                                        color: "black"
                                    },
                                    ref: svgRef,
                                    align: "left",
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_Flow_FlowEditor__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
                                        app: app,
                                        mode: artifact.mode,
                                        doc: artifact
                                    })
                                }),
                                artifact?.type === "slides" && artifact.hid && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                                    ref: svgRef,
                                    style: {
                                        width: isMobile ? "100%" : 960,
                                        height: isMobile ? 300 : 540
                                    },
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("iframe", {
                                        className: "nodrag",
                                        id: "slides-frame",
                                        style: {
                                            width: "100%",
                                            height: "100%"
                                        },
                                        // ref={iframeRef}
                                        src: `https://service.funblocks.net/view.html?theme=sky&hid=${artifact.hid}`,
                                        title: "FunBlocks AI Slides"
                                    })
                                }),
                                artifact && ([
                                    "markdown",
                                    "svg",
                                    "mermaid"
                                ].includes(artifact.type) || [
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.poetic */ .IF.poetic,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.graphics */ .IF.graphics,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.onePageSlides */ .IF.onePageSlides,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.infographic */ .IF.infographic,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.mindsnap */ .IF.mindsnap,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.insightcards */ .IF.insightcards,
                                    _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.promptOptimizer */ .IF.promptOptimizer
                                ].includes(app)) && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_ArtifactDisplay__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
                                    app: app,
                                    artifact: artifact,
                                    svgRef: svgRef,
                                    showDownload: true,
                                    showShare: true
                                }),
                                artifact && /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Tools, {
                                    app: app,
                                    svgRef: svgRef,
                                    artifact: artifact
                                }),
                                /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {
                                    spacing: 2,
                                    mt: 2,
                                    children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {
                                        borderRadius: "full",
                                        colorScheme: "teal",
                                        size: "lg",
                                        onClick: ()=>window.open(basePath + "/" + (app?.toLowerCase() || ""), "_blank"),
                                        px: 8,
                                        children: t("find_more_cards_or_create_your_own")
                                    })
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {
                                    maxW: "680px",
                                    textAlign: "center",
                                    mt: 4,
                                    mb: 8,
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Heading, {
                                            as: "h2",
                                            size: "lg",
                                            mb: 4,
                                            children: t("why_funblocks_ai", {
                                                app: (0,_utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .getAppname */ .WD)(app)
                                            })
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(Features, {
                                            app: app,
                                            appTranslationFile: appTranslationFile
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {
                                    maxW: "800px",
                                    textAlign: "center",
                                    mt: 4,
                                    mb: 8,
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Heading, {
                                            as: "h2",
                                            size: "md",
                                            mb: 4,
                                            children: "FunBlocks AI Tools"
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {
                                            columns: {
                                                base: 1,
                                                md: 2,
                                                lg: 3
                                            },
                                            spacing: 2,
                                            w: "full",
                                            children: tools.map((tool)=>{
                                                return /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Link, {
                                                    href: basePath + tool.link,
                                                    isExternal: true,
                                                    style: {
                                                        color: "white"
                                                    },
                                                    children: tool.title
                                                });
                                            })
                                        })
                                    ]
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Modal, {
                        isOpen: isOpen,
                        onClose: onClose,
                        size: "full",
                        children: [
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalOverlay, {}),
                            /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalContent, {
                                bg: "rgba(0,0,0,0.8)",
                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalBody, {
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: "100vh",
                                    position: "relative",
                                    children: [
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {
                                            icon: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__.CloseIcon, {}),
                                            position: "absolute",
                                            right: "20px",
                                            top: "20px",
                                            onClick: onClose,
                                            "aria-label": t("close_modal"),
                                            bg: "transparent",
                                            color: "white",
                                            _hover: {
                                                bg: "rgba(255,255,255,0.2)"
                                            }
                                        }),
                                        /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Image, {
                                            src: imageUrl,
                                            alt: "Shared SVG Full Screen",
                                            objectFit: "contain",
                                            maxHeight: "90vh",
                                            maxWidth: "90vw",
                                            onClick: (e)=>e.stopPropagation(),
                                            cursor: "default"
                                        })
                                    ]
                                })
                            })
                        ]
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(_components_CardGenerator_ShareModal__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {
                        app: app
                    }),
                    /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx("div", {
                        style: {
                            display: "none"
                        },
                        children: /*#__PURE__*/ react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx(qrcode_react__WEBPACK_IMPORTED_MODULE_10__.QRCodeCanvas, {
                            value: `https://www.funblocks.net${basePath + "/" + (app?.toLowerCase() || "")}`,
                            size: 100,
                            id: "qrcode-canvas"
                        })
                    })
                ]
            })
        ]
    });
};
async function getServerSideProps({ params , locale , resolvedUrl  }) {
    const { slug  } = params;
    const app = (0,_utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .getAppValue */ .X0)(slug[0]);
    const id = slug[1];
    const shareUrl = resolvedUrl;
    const metaInfo = app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.graphics */ .IF.graphics ? {
        title: "FunBlocks AI Graphics - Fun, Informative, Extraordinary graphics with AI",
        description: "Generate insightful and fun infographic cards, easily gain new perspectives and stand out on social platforms.",
        imageUrl: "https://www.funblocks.net/assets/img/portfolio/fullsize/ai_insights.png"
    } : {
        title: "FunBlocks AI Mindmap - AI generating mind map for books, movies, videos, web pages and more",
        description: "AI-powered whiteboard & mind mapping tool. Boost creativity by 10x with GPT-4 & Claude LLM. Explore ideas, solve problems & learn faster. Free trial available!",
        imageUrl: "https://www.funblocks.net/assets/img/portfolio/fullsize/aiflow_benefits.png",
        keywords: "LLM, ChatGPT, AI, Mind map, Brainstorming, AI Tools, FunBlocks AI, AI Graphics, AI Infographics, AI Mindmap, Mind Mapping, Visual Learning, Knowledge Management, AI-Guided Exploration, One-Click Generation, Study Research, Brainstorming, ChatGPT Alternative, Claude Alternative, Content Summarization, Video Summarization, Web Summarization, Document Summarization, AI Learning Tools"
    };
    metaInfo.shareUrl = `https://www.funblocks.net/aitools/${app?.toLowerCase() || "mindmap"}`;
    const appTranslationFile = (!app || app === "undefined") && "mindmap" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.art */ .IF.art && "artinsight" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.brainstorming */ .IF.brainstorming && "brainstorm" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.businessmodel */ .IF.businessmodel && "bma" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.movie */ .IF.movie && "cinemap" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.bias */ .IF.bias && "logic" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.onePageSlides */ .IF.onePageSlides && "oneslide" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.refineQuestion */ .IF.refineQuestion && "question" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.startupmentor */ .IF.startupmentor && "startup" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.youtube */ .IF.youtube && "summarizer" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.layeredExplanation */ .IF.layeredExplanation && "layered" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.promptOptimizer */ .IF.promptOptimizer && "prompt-optimizer" || app === _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.imageEditor */ .IF.imageEditor && "image-editor" || [
        _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.graphics */ .IF.graphics,
        _utils_constants__WEBPACK_IMPORTED_MODULE_14__/* .APP_TYPE.infographic */ .IF.infographic
    ].includes(app) && "common" || app.toLowerCase();
    try {
        const response = await fetch(`${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_18__/* .apiUrl */ .J}/ai/artifact?_id=${id?.trim()}&app=${app}`);
        const data = await response.json();
        const cardData = data?.data ? {
            ...metaInfo,
            title: data.data.title || `${data.data.promptLabel} - ${data.data.userInput}`,
            description: data.data?.type === "markdown" && data.data.content || data.data.description || data.data.type === "flow" && `AI generated Mind map with FunBlocks AIFlow, topic: ${data.data.title}` || "",
            imageUrl: data.data?.type === "svg" && `${_utils_apiUtils__WEBPACK_IMPORTED_MODULE_18__/* .apiUrl */ .J}/ai/svg2png/${id}` || data.data.imageUrl || "",
            shareUrl: shareUrl
        } : metaInfo;
        return {
            props: {
                ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_7__.serverSideTranslations)(locale, [
                    "common",
                    appTranslationFile
                ]),
                cardData,
                artifact: data.data,
                // 添加这些 props 以在 _app.js 中使用
                metaTitle: cardData.title,
                metaDescription: cardData.description,
                metaImage: cardData.imageUrl,
                metaUrl: cardData.shareUrl,
                app,
                appTranslationFile
            }
        };
    } catch (error) {
        console.error("Error fetching card data:", error);
        return {
            props: {
                ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_7__.serverSideTranslations)(locale, [
                    "common"
                ]),
                cardData: metaInfo,
                // 添加默认的 meta 数据
                metaTitle: metaInfo.title,
                metaDescription: metaInfo.description,
                metaImage: metaInfo.imageUrl,
                metaUrl: metaInfo.shareUrl,
                app,
                appTranslationFile
            }
        };
    }
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SharePage);

__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ 5692:
/***/ ((module) => {

module.exports = require("@mui/material");

/***/ }),

/***/ 9048:
/***/ ((module) => {

module.exports = require("@mui/material/CircularProgress");

/***/ }),

/***/ 6999:
/***/ ((module) => {

module.exports = require("@react-oauth/google");

/***/ }),

/***/ 3665:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap");

/***/ }),

/***/ 9327:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/ChevronDown");

/***/ }),

/***/ 39:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/FileText");

/***/ }),

/***/ 450:
/***/ ((module) => {

module.exports = require("@styled-icons/bootstrap/TextLeft");

/***/ }),

/***/ 9077:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/FlowCascade");

/***/ }),

/***/ 8847:
/***/ ((module) => {

module.exports = require("@styled-icons/entypo/Link");

/***/ }),

/***/ 1977:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxChecked");

/***/ }),

/***/ 7520:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/CheckboxUnchecked");

/***/ }),

/***/ 3770:
/***/ ((module) => {

module.exports = require("@styled-icons/fluentui-system-regular/Edit");

/***/ }),

/***/ 3620:
/***/ ((module) => {

module.exports = require("@styled-icons/material");

/***/ }),

/***/ 384:
/***/ ((module) => {

module.exports = require("@styled-icons/material/Close");

/***/ }),

/***/ 6131:
/***/ ((module) => {

module.exports = require("html-to-image");

/***/ }),

/***/ 6333:
/***/ ((module) => {

module.exports = require("immutability-helper");

/***/ }),

/***/ 1377:
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ 5460:
/***/ ((module) => {

module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ 4558:
/***/ ((module) => {

module.exports = require("next/config");

/***/ }),

/***/ 968:
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ 1853:
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ 6689:
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ 997:
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ 6134:
/***/ ((module) => {

module.exports = import("@chakra-ui/icons");;

/***/ }),

/***/ 2210:
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ 2728:
/***/ ((module) => {

module.exports = import("@xyflow/react");;

/***/ }),

/***/ 6197:
/***/ ((module) => {

module.exports = import("framer-motion");;

/***/ }),

/***/ 1024:
/***/ ((module) => {

module.exports = import("mermaid");;

/***/ }),

/***/ 6611:
/***/ ((module) => {

module.exports = import("qrcode.react");;

/***/ }),

/***/ 3196:
/***/ ((module) => {

module.exports = import("react-dnd");;

/***/ }),

/***/ 1152:
/***/ ((module) => {

module.exports = import("react-dnd-html5-backend");;

/***/ }),

/***/ 7987:
/***/ ((module) => {

module.exports = import("react-i18next");;

/***/ }),

/***/ 1301:
/***/ ((module) => {

module.exports = import("react-icons/fa");;

/***/ }),

/***/ 6905:
/***/ ((module) => {

module.exports = import("react-icons/md");;

/***/ }),

/***/ 9034:
/***/ ((module) => {

module.exports = import("react-icons/si");;

/***/ }),

/***/ 3135:
/***/ ((module) => {

module.exports = import("react-markdown");;

/***/ }),

/***/ 2017:
/***/ ((module) => {

module.exports = import("react-share");;

/***/ }),

/***/ 9521:
/***/ ((module) => {

module.exports = import("rehype-katex");;

/***/ }),

/***/ 1871:
/***/ ((module) => {

module.exports = import("rehype-raw");;

/***/ }),

/***/ 6809:
/***/ ((module) => {

module.exports = import("remark-gfm");;

/***/ }),

/***/ 9832:
/***/ ((module) => {

module.exports = import("remark-math");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, [1951,4743,101,5408,8534,906], () => (__webpack_exec__(4795)));
module.exports = __webpack_exports__;

})();