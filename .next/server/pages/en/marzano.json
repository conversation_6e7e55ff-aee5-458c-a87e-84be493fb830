{"pageProps": {"_nextI18Next": {"initialI18nStore": {"en": {"marzano": {"marzano_intro_description": "MarzanoBrain is a web application that transforms how educators and self-learners approach curriculum design. Using advanced AI powered by Marzano's Taxonomy of Educational Objectives, our platform generates comprehensive learning roadmaps for any subject in an intuitive mind map format.", "marzano_feature_1_title": "Topic-to-Curriculum Conversion", "marzano_feature_1_text": "Simply enter any learning topic of interest, and receive a fully structured curriculum design within seconds", "marzano_feature_2_title": "Marzano Taxonomy Framework", "marzano_feature_2_text": "All learning plans are built on <PERSON>'s proven educational framework, ensuring comprehensive cognitive development", "marzano_feature_3_title": "Interactive Mind Maps", "marzano_feature_3_text": "Visualize your curriculum in an engaging, hierarchical mind map that shows the relationships between concepts", "marzano_benefits_title": "Benefits of MarzanoBrain", "marzano_benefits_edu_title": "For Educators", "marzano_benefit_edu_1": "Create research-backed lesson plans in minutes instead of hours", "marzano_benefit_edu_2": "Align teaching activities with all cognitive systems", "marzano_benefit_edu_3": "Design assessments that match appropriate developmental stages", "marzano_benefit_edu_4": "Build learning experiences that optimize student outcomes", "marzano_benefits_learner_title": "For Learners", "marzano_benefit_learner_1": "Understand your learning journey through a clear framework", "marzano_benefit_learner_2": "Identify where you are in the mastery process", "marzano_benefit_learner_3": "Set realistic goals for your educational development", "marzano_benefit_learner_4": "Track your cognitive progress from basic recall to creative application", "marzano_audience_title": "Who Can Benefit from MarzanoBrain", "marzano_audience_1_title": "Teachers and Educators", "marzano_audience_1_text": "Create structured lesson plans and course outlines that foster comprehensive cognitive development", "marzano_audience_2_title": "Curriculum Designers", "marzano_audience_2_text": "Generate foundational frameworks that can be customized for specific educational contexts", "marzano_audience_3_title": "Self-learners", "marzano_audience_3_text": "Develop structured learning paths for mastering new skills or subjects with clear progression", "marzano_audience_4_title": "Subject Matter Experts", "marzano_audience_4_text": "Convert expertise into teachable frameworks organized by cognitive complexity", "marzano_framework_title": "Marzano's Taxonomy Framework", "marzano_framework_description": "<PERSON><PERSON>'s Taxonomy of Educational Objectives provides a comprehensive framework for understanding the learning process and designing instructional activities across multiple cognitive systems.", "marzano_systems_title": "Marzano's Systems and Knowledge Domain", "marzano_system_1_title": "1. Self-System", "marzano_system_1_text": "Examines motivation and attitudes that determine whether to engage in new tasks", "marzano_system_2_title": "2. Metacognitive System", "marzano_system_2_text": "Sets goals and monitors progress during learning activities", "marzano_system_3_title": "3. Cognitive System", "marzano_system_3_text": "Processes information through various levels of complexity", "marzano_system_4_title": "4. Knowledge Domain", "marzano_system_4_text": "The subject-specific content being learned or taught", "marzano_levels_title": "Processing Levels in Marzano's Taxonomy", "marzano_level_1_title": "1. Retrieval", "marzano_level_1_text": "Recognizing and recalling basic information", "marzano_level_2_title": "2. Comprehension", "marzano_level_2_text": "Understanding the core concepts without extending them", "marzano_level_3_title": "3. Analysis", "marzano_level_3_text": "Examining information more deeply to identify patterns and relationships", "marzano_level_4_title": "4. Knowledge Utilization", "marzano_level_4_text": "Applying knowledge to real-world situations and problems", "marzano_level_5_title": "5. Metacognition", "marzano_level_5_text": "Monitoring and adjusting one's own thinking processes", "marzano_level_6_title": "6. Self-System Thinking", "marzano_level_6_text": "Examining beliefs, motivations, and self-efficacy related to learning", "marzano_advantages_title": "Advantages of Marzano's Taxonomy", "marzano_advantages_description": "Marzano's Taxonomy offers several advantages over other educational frameworks, making it particularly valuable for comprehensive learning design.", "marzano_advantage_1": "Integration of cognitive, affective, and behavioral domains", "marzano_advantage_2": "Focus on thinking strategies rather than just content memorization", "marzano_advantage_3": "Practical orientation toward knowledge application", "marzano_advantage_4": "Emphasis on student self-regulation and self-monitoring", "marzano_advantage_5": "Research-based foundation with proven educational outcomes", "marzano_faq_title": "Frequently Asked Questions", "marzano_faq_1_q": "How is MarzanoBrain different from other educational planning tools?", "marzano_faq_1_a": "MarzanoBrain is unique in its use of Marzano's Taxonomy framework to organize learning progressions. Unlike tools that focus primarily on content delivery, MarzanoBrain builds educational experiences based on cognitive complexity, ensuring that learning activities match appropriate developmental stages.", "marzano_faq_2_q": "Do I need to understand <PERSON><PERSON>'s Taxonomy to use MarzanoBrain?", "marzano_faq_2_a": "No prior understanding of Marzano's Taxonomy is required. MarzanoBrain automatically applies the framework to your topic. However, as you use the tool, you'll naturally become familiar with the Marzano approach, enhancing your instructional design skills.", "marzano_faq_3_q": "Can MarzanoBrain be used for any subject area?", "marzano_faq_3_a": "Yes, MarzanoBrain is designed for use with any subject or topic. Whether you're teaching mathematics, literature, science, arts, or professional skills, the Marzano framework can appropriately structure the learning progression.", "marzano_faq_4_q": "How detailed are the educational plans generated by MarzanoBrain?", "marzano_faq_4_a": "MarzanoBrain generates comprehensive plans that include learning objectives, instructional activities, and assessment methods across all systems of Marzano's Taxonomy. You'll receive a complete educational roadmap that can be used directly or customized further.", "marzano_faq_5_q": "Can I export or share the mind maps created with MarzanoBrain?", "marzano_faq_5_a": "Yes, all mind maps can be exported in various formats or shared directly with colleagues, students, or other stakeholders. This makes MarzanoBrain excellent for collaborative curriculum development and educational planning.", "marzano_faq_6_q": "Is MarzanoBrain suitable for higher education and professional training?", "marzano_faq_6_a": "Absolutely. While <PERSON><PERSON>'s Taxonomy is sometimes associated with K-12 settings, it is equally valuable in higher education and professional training environments. The progression from basic comprehension to creative application and self-system thinking is relevant at all levels of learning.", "marzano_faq_7_q": "How can MarzanoBrain help with student self-directed learning?", "marzano_faq_7_a": "Students can use MarzanoBrain to create learning paths for subjects they want to master. The generated mind maps provide clear milestones and progression pathways, helping students understand where they are in their learning journey and what steps to take next.", "marzano_faq_8_q": "Does MarzanoBrain integrate with other educational tools and platforms?", "marzano_faq_8_a": "MarzanoBrain mind maps can be exported and integrated into various learning management systems, presentation tools, and educational platforms. We continually work to expand integration options to make MarzanoBrain fit seamlessly into your existing educational workflows.", "marzano_faq_9_q": "Is there a cost to using FunBlocks AI MarzanoBrain? Can I use it for free?", "marzano_faq_9_a": "FunBlocks AI MarzanoBrain is available for free use to all users. New users can enjoy free trials, and all users get 10 free AI requests per day simply by logging in."}, "common": {"login": "<PERSON><PERSON>", "logout": "Logout", "pricing": "Pricing", "why_funblocks_ai": "Why FunBlocks AI {{app}}?", "try_now": "Try Now", "english": "English", "chinese": "Chinese", "load_more": "Load More", "delete": "Delete", "like": "Like", "unlike": "Unlike", "share_to_showcase": "Click to share to Shared list, get more likes, and earn free generations", "username_password_required": "Username and password cannot be empty", "check_credentials": "Please check your username and password", "username": "Username", "enter_username": "Enter username", "password": "Password", "enter_password": "Enter password", "logging_in": "Logging in...", "no_cards_yet": "You haven't created any cards yet.", "generate_card": "Generate", "app_slogan_graphics": "Smart Visuals, Effortless Creativity", "app_one_sentence_graphics": "Turn text and data into eye-catching infographics and clever knowledge cards with AI – no design skills required.", "app_slogan_mindmap": "Remap Your Mind with AI", "app_one_sentence_mindmap": "Let AI be your guide to explore the world of knowledge!", "app_slogan_brainstorming": "AI-Powered Brainstorming to Ignite Unlimited Creativity", "app_one_sentence_brainstorming": "Let <PERSON> be your creative partner, breaking through mental barriers and discovering new possibilities", "app_slogan_decision": "AI-Powered Decision Analysis Assistant", "app_one_sentence_decision": "Make better decisions with AI-driven analysis that considers multiple perspectives, risks, and outcomes to help you reach rational conclusions", "app_slogan_planner": "AI Task Planner - Transform Complex Tasks into Actionable Plans", "app_one_sentence_planner": "Harness the power of AI to break down complex tasks, generate detailed action plans, and boost your productivity with intelligent task management.", "app_slogan_slides": "Create Insightful Presentations with AI", "app_one_sentence_slides": "AI-powered presentation tool that helps professionals create compelling presentations efficiently and effectively.", "app_slogan_youtube": "Transform YouTube Videos into Interactive Knowledge Maps", "app_one_sentence_youtube": "Unlock the knowledge within YouTube videos instantly. Our AI-powered tool transforms lengthy videos into concise, interactive summaries and knowledge maps.", "app_slogan_dreamlens": "AI DreamLens", "app_one_sentence_dreamlens": "Unlock the Hidden Meanings of Your Dreams with AI", "app_slogan_art": "Instant Artistic Understanding Through AI", "app_one_sentence_art": "Your AI Guide to Art Appreciation", "app_slogan_photo": "Transform Every Shot into a Learning Journey", "app_one_sentence_photo": "Your AI Photography Mentor for Instant Professional Feedback and Growth", "app_slogan_reading": "Navigate Books, Illuminate Minds", "app_one_sentence_reading": "Your AI-Powered Reading Guide to Knowledge Discovery", "app_slogan_startupmentor": "AI Mentor for Startups", "app_one_sentence_startupmentor": "Elevate Your Startup. Accelerate Your Success.", "app_slogan_businessmodel": "Advanced AI-powered Strategic Analysis Tool", "app_one_sentence_businessmodel": "Provides comprehensive business model analysis and innovation recommendations in seconds.", "app_slogan_okr": "Precision Goals, Limitless Potential", "app_one_sentence_okr": "Transforming Aspirations into Achievements", "app_slogan_poetic": "Transform Images into Cultural Poetry", "app_one_sentence_poetic": "AI-powered tool that turns everyday images into witty, culturally-rich poetic commentaries", "app_slogan_onepageslide": "AI SlideGenius - Create Professional Slides in Seconds", "app_one_sentence_onepageslide": "Transform your text content into visually stunning presentations with the power of AI.", "app_slogan_infographic": "AI-Powered Infographic Generation", "app_one_sentence_infographic": "Transform your text content into beautiful infographics in Seconds", "app_slogan_mindkit": "Where Mental Models Meet AI", "app_one_sentence_mindkit": "Resolve complex problems with modular thinking frameworks", "app_slogan_mindsnap": "Where AI meets mental models to visualize complex ideas", "app_one_sentence_mindsnap": "Explore your topic or question with crystal-clear, structured, and insightful infographics in seconds", "ai_mindsnap_desc": "Explore any topic using AI-powered mental model analysis and create beautiful infographics instantly", "app_slogan_insightcards": "Transform Your Thinking with AI-Powered Insight Cards", "app_one_sentence_insightcards": "Create visually appealing insight cards that reveal hidden connections, contradictions, and perspectives on any topic", "ai_insightcards_desc": "Generate visual insight cards that provide unique perspectives through frameworks like Aha Insights, Paradoxical Interpretations, and more", "app_slogan_horoscope": "Your Destiny, Decoded by AI", "app_one_sentence_horoscope": "Where Ancient Wisdom Meets Modern Intelligence - Personalized Astrological Insights Powered by Advanced AI", "app_slogan_counselor": "Discover Your Best Self Through AI Counseling", "app_one_sentence_counselor": "Expert psychological insights and empathetic support to help you navigate life's challenges", "app_slogan_criticalthinking": "Beyond Intuition, Into Deep Analysis", "app_one_sentence_criticalthinking": "AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths", "app_slogan_reflection": "Beyond First Thoughts", "app_one_sentence_reflection": "Develop stronger reasoning and deeper understanding with an AI coach that challenges your thinking", "app_slogan_feynman": "Master Complex Topics with Your AI <PERSON><PERSON><PERSON>", "app_one_sentence_feynman": "Transform confusion into clarity through AI-powered Feynman learning techniques that break down any subject into intuitive mind maps", "app_slogan_movie": "Discover the Art of Cinema Through AI-Powered Mind Maps", "app_one_sentence_movie": "Transform your movie-watching experience with intelligent analysis that reveals hidden layers, themes, and connections in your favorite films", "app_slogan_refinequestion": "<PERSON> Smarter, <PERSON> Deeper", "app_one_sentence_refinequestion": "Harness AI-powered critical thinking to craft precise, powerful questions that unlock deeper insights and better solutions", "app_slogan_bias": "Uncover the Truth Behind Arguments", "app_one_sentence_bias": "Leverage AI to Analyze Cognitive Biases and Logical Fallacies, Develop Clearer Thinking", "to_original_page": "Open the original page", "gender": "Gender", "male": "Male", "female": "Female", "horoscope_range": "Horoscope Range", "horoscope_today": "Today's Horoscope", "horoscope_all": "All Horoscopes", "mindmap": "Mindmap", "graphics": "Infographics", "mine": "Mine", "my_generations": "My generations", "please_login_to_view_mine": "Please login to view your {{app}}", "generate_new_card": "Generate New Card", "please_select_card_type": "Please select a AI Master", "please_enter_valid_message": "Please enter a valid message", "please_select_language": "Please select a language", "please_enter_valid_url": "Please enter a valid webpage link", "please_enter_valid_video_url": "Please enter a valid YouTube video link", "enter_webpage_url": "Please enter or paste your link", "output_language": "Output", "card_generation_success": "Content generated successfully", "card_generation_error": "An error occurred while generating the card. Please try again later.", "free_trial_exceeded": "You have used up your free trial quota. Please upgrade.", "daily_quota_exceeded": "You have used up your trial or daily generation quota. Please upgrade.", "upgrade": "Upgrade", "generating": "Generating...", "share_to_social_platforms": "Share to Social Platforms", "title_and_link_copied": "Title and link copied", "copy": "Copy", "copied": "Copied to clipboard", "copy_failed": "Co<PERSON> failed", "save_image_and_paste_to_instagram": "Please save the image and paste the title and link to Instagram", "share_to_twitter": "Share to X/Twitter", "share_to_facebook": "Share to Facebook", "share_to_linkedin": "Share to LinkedIn", "share_to_whatsapp": "Share to WhatsApp", "share_to_instagram": "Share to Instagram", "share_via_email": "Share via Email", "copy_title_and_link": "Copy Link", "download_image": "Download Image", "email_verification_login": "Email Verification Login", "please_complete_captcha": "Please complete the CAPTCHA", "please_enter_valid_email": "Please enter a valid email address", "failed_to_send_verification_code": "Failed to send verification code", "verification_failed": "Verification failed", "email_address": "Email Address", "enter_your_email": "Enter your email address", "sending": "Sending...", "send_verification_code": "Send Verification Code", "verifying": "Verifying...", "verify_and_login": "Verify and Login", "select_card": "Select AI Master", "select_graph_type": "Graph Type", "error_loading_svg": "Unable to load SVG content", "loading": "Loading...", "fullscreen_display": "Fullscreen display", "fullscreen_tooltip": "Fullscreen display", "exit_fullscreen_tooltip": "Exit Fullscreen display", "download": "Download", "share": "Share", "save_svg": "Save SVG to local device", "ai_insights": "FunBlocks AI Graphics", "find_more_cards_or_create_your_own": "Find more or create your own", "close_modal": "Close modal", "login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "login_error": "An error occurred during login", "login_with_google": "Login with Google", "login_modal_instruction": "Please complete your login in the new tab, then click 'Completed Login' to confirm.", "open_login_page": "Open Login Page", "completed_login": "Completed Login", "checking": "Checking...", "not_logged_in_yet": "Not logged in yet", "please_complete_login_first": "Please complete the login process first", "check_login_failed": "Failed to check login status", "login_detected": "<PERSON>gin detected", "login_successful_auto_close": "Login successful, modal will close automatically", "auto_check_login_status": "We'll automatically detect when you complete the login", "example_wise_insight": "The nature of happiness", "example_fun_fact": "Interesting facts about space", "example_creative_idea": "Innovative solutions for climate change", "examples": "Examples", "your_input": "Your input...", "your_question": "Your question...", "given_text": "Enter or paste text...", "input_length_warning": "Input length: {{current}}/{{max}}", "provide_text_or_webpage": "Provide text or webpage link", "url_content_fetch_failed": "Failed to fetch URL content", "enter_your_topic": "Please enter your topic or text...", "enter_your_ideas": "Please enter your startup idea or plan...", "please_enter": "Please enter...", "ai_actions": "AI Actions", "fix_codes_bug": "Fix Code Issues", "improve_codes": "Improve Content", "user_input": "User Input", "confirm": "Confirm", "cancel": "Cancel", "please_enter_your_requirements": "Please enter your modification requirements...", "input_hint": "Tip: Press Ctrl+Enter to confirm quickly, press Esc to cancel", "ai_action_success": "AI action completed successfully", "ai_action_error": "AI action failed, please try again", "fun_and_informative": "Fun, Informative, Extraordinary", "explore_ai_thoughts_and_humor": "Explore AI's deep thoughts and sense of humor, let FunBlocks AI Graphics become your creative assistant.", "generate_insightful_and_fun_cards": "Generate insightful and fun infographic cards, easily gain new perspectives and stand out on social platforms.", "why_choose_funblocks_ai_insights": "Why Choose FunBlocks AI Graphics?", "funblocks_ai_insights_combines_llm_and_card_features": "FunBlocks AI Graphics combines the powerful knowledge and reasoning capabilities of LLM with the readability and sharing features of cards. Whether for social media, presentations, or personal notes, we can provide you with unique perspectives and valuable content. Let <PERSON> become your thinking partner and explore the infinite possibilities of creativity together!", "faq": "Frequently Asked Questions", "one_click_joy": "<PERSON>-<PERSON><PERSON>", "experience_ai_insights_now": "Experience the magic of AI Graphics now—bringing creativity and joy right to your fingertips!", "start_generating": "Start Generating", "graphics_feature_1_text": "Generate Informative insights based on your provided topic.", "graphics_feature_2_text": "Create fun, shareable cards with one-click.", "graphics_feature_3_text": "Explore new ideas and perspectives with AI.", "meta": {"title": "FunBlocks AI Tools: AI-Powered Productivity & Creativity Tools | Whiteboards, Mind Map, Presentations, Infographics & More", "description": "Boost productivity & unleash creativity with FunBlocks AI! All-in-one AI platform with whiteboard, mind mapping, brainstorming, document writing, presentation creation, and more. Free trial available!", "keywords": "LLM, ChatGPT, FunBlocks AI, AI Insights, AI productivity tools, AI creativity tools, AI whiteboard, AI mind map, AI brainstorming, AI presentation maker, AI infographic generator, AI decision analyzer, AI task planner, AI YouTube summarizer, AI knowledge assistant, free AI tools, AI platform, visual tools, one-click AI, integrated AI tools, mind mapping software, presentation software, infographic maker, creative content", "og_title": "FunBlocks AI Tools: AI-Powered Productivity & Creativity Tools | Whiteboards, Mind Map, Presentations, Infographics & More", "og_description": "Boost productivity & creativity with FunBlocks AI—your all-in-one tool for whiteboards, mind maps, brainstorming, writing, and presentations. Try it free!"}, "graph_chat": "ChatGraphics", "make_graph": "InfoGraphics", "card_guru": "InsightCards", "image_insights": "WonderLens", "graph_chat_placeholder": "Chat with AI to generate an infographic...", "make_graph_placeholder": "Enter text, upload a file, or provide a website URL...", "generate": "Generate", "create_artwork": "Create Artwork", "input_length_sufficient": "Input length is sufficient", "input_too_short": "Input too short", "input_length_requirement": "Please enter at least 50 characters when not providing a URL.", "explore_other_products": "Explore Other FunBlocks AI Products", "boost_productivity": "Boost your productivity with AI-powered tools for seamless web interactions.", "unleash_creativity": "Unleash your creativity with AI-driven brainstorming and mind mapping.", "create_stunning_presentations": "Create stunning presentations effortlessly with AI-powered slide generation.", "transform_workflow": "Transform your workflow with an all-in-one AI assistant for enhanced creativity and efficiency.", "contact_us": "Contact Us", "image": "Image", "art": "Masterpieces", "upload_image": "Upload Image", "choose_image": "Choose Image", "uploaded_image": "Uploaded Image", "image_url": "Image URL", "paste_image_url": "Paste image URL here", "or": "OR", "please_upload_or_provide_image_url": "Please upload an image or provide an image URL", "upload_failed": "Failed to upload image. Please try again.", "image_load_error": "Failed to load image", "wonderlens_tip": "Generate fun commentary pieces based on the image", "zoom_in": "Zoom in", "zoom_out": "Zoom out", "book": "Book", "movie": "Movie", "video": "Youtube Video", "link": "Web page", "topic": "Topic", "doc": "Document", "others": "Others", "book_name": "Book title, author...", "movie_name": "Movie title", "video_url": "Youtube video link", "webpage_url": "Web page link", "to_aiflow": "Keep exploring with AIFlow", "to_aiflow_tips": "Explore more in-depth with AIFlow", "deep_dive_to_topic": "Generate a detailed introduction to the topic", "expand_ideas": "Break down the topic or generate more ideas", "mental_model": "Mental model", "educational_model": "Educational model", "upgrade_to_vip": "Upgrade your plan", "upgrade_to_vip_msg": "You have exhausted your free AI generation quota for today. Please upgrade to a FunBlocks AI membership to enjoy unlimited access to all FunBlocks AI products and services, and continue to benefit from our premium offerings.", "task_analysis": "Task analysis", "task_breakdown": "Task breakdown", "task_priority": "Adjust task priority", "thinking_model": "Thinking model", "swot_analysis": "SWOT Analysis", "first_principle": "First Principles Thinking", "business_model_canvas": "Business Model Canvas", "fivew1h_method": "5W1H Method", "scamper_method": "SCAMPER Method", "six_thinking_hats": "Six Thinking Hats", "pdca": "PDCA Cycle", "systems_thinking": "Systems Thinking", "dialectical_thinking": "Dialectical Thinking", "probabilistic_thinking": "Probabilistic Thinking", "steep_analysis": "STEEP Analysis", "five_forces": "Five Forces Analysis", "four_p": "4P Marketing Mix", "triz": "Theory of Inventive Problem Solving", "rephrazing": "Problem Rephrasing", "learning_pyramid": "Learning Pyramid", "kwl": "KWL Map", "changing_perspectives": "Changing Perspectives", "reverse_thinking": "Reverse Thinking", "role_playing": "Role Playing", "mckinsey_7S_framework": "McKinsey 7S Framework", "value_proposition_canvas": "Value Proposition Canvas", "pros_cons": "Pros and Cons", "decision_tree": "Decision Tree", "decision_matrix": "Decision Matrix", "cost_benefit_analysis": "Cost-Benefit Analysis", "casual_chain": "Casual Chain", "second_order_thinking": "Second Order Thinking", "inversion_thinking": "Inversion Thinking", "scientific_method": "Scientific Method", "pyramid_principle": "Pyramid Principle", "occams_razor": "Occam's Razor", "mece_principle": "MECE Principle", "eisenhower_matrix": "<PERSON>", "bloom": "Bloom", "marzano": "Marzano", "addie": "ADDIE", "5e": "5E", "gagne": "<PERSON><PERSON><PERSON>", "constructivist": "Constructivist", "auto": "Auto", "other": "Other", "enter_mental_model": "Type modal name", "select_none": "None", "birthdate": "Birth Date", "platform_title": "AI-Powered Tools for Creativity and Productivity", "platform_description": "Engage with AI through visual formats——beyond the text chatbox.", "platform_description_1": "Explore new ways to interact with AI beyond text conversations.", "explore_tools": "Explore AI Tools", "learn_more": "Learn More", "platform_meta_title": "AI-Powered Tools for Creativity and Productivity | AI Mindmap, Infographics, Slides and more | FunBlocks AIFlow", "platform_meta_description": "Discover FunBlocks AI's suite of intelligent tools: AI Graphics, Mindmap Generator, Brainstorming Assistant, Presentation Creator, Decision Analyzer, and Task Planner. Boost your productivity and creativity with our visual AI tools.", "ai_graphics_desc": "Create engaging infographics and cards with unique insights powered by AI", "ai_mindmap_desc": "Effortlessly create detailed mind maps from any source, including books, movies, videos, and documents, or explore a single topic", "ai_brainstorming_desc": "Partner with AI to spark creative ideas and solutions, perfect for both work and study", "ai_slides_desc": "Effortlessly create engaging presentations with AI in just one click.", "ai_decision_desc": "Get rational analysis and insights for better decision making", "ai_planner_desc": "Break down complex tasks into manageable steps", "ai_youtube_desc": "Summarize YouTube videos and transform into interactive knowledge maps", "ai_dreamlens_desc": "Personalized dream analysis with multi-perspective psychological insights, helping you unlock your subconscious", "ai_art_desc": "Instantly generate professional art appreciation mind maps, providing expert analysis and deeper understanding", "ai_photo_desc": "AI Photography Mentor for Instant Professional Feedback and Growth", "ai_reading_desc": "Generates structured mind maps from books, enabling efficient comprehension and knowledge internalization", "ai_okr_desc": "Professional goal management and strategic planning tool that transform grand visions into specific, measurable strategic objectives", "ai_startupmentor_desc": "Transform your entrepreneurial journey with AI-powered startup mentorship. Get instant, expert-level analysis and strategic guidance for your business venture", "ai_businessmodel_desc": "Provides comprehensive business model analysis and innovation recommendations in seconds", "ai_poetic_desc": "Add witty, culturally-rich poetic commentaries to image", "ai_onepageslide_desc": "Transform your text content into visually stunning presentations with the power of AI", "ai_infographic_desc": "Transform your text content into beautiful infographics with AI", "ai_mindkit_desc": "Resolve complex problems with AI-powered Mental Models, e.g. First Principles Thinking, SWOT Analysis, 5W1H Method, SCAMPER Method, etc.", "ai_horoscope_desc": "Personalized Astrological Insights Powered by Advanced AI", "ai_counselor_desc": "Expert psychological insights and empathetic support to help you navigate life's challenges", "ai_criticalthinking_desc": "AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths", "ai_reflection_desc": "Develop stronger reasoning and deeper understanding with an AI coach that challenges your thinking", "ai_feynman_desc": "Transform confusion into clarity through AI-powered Feynman learning techniques that break down any subject into intuitive mind maps", "ai_bias_desc": "Leverage AI to Analyze Cognitive Biases and Logical Fallacies, Develop Clearer Thinking", "ai_movie_desc": "Discover the Art of Cinema Through AI-Powered Mind Maps", "ai_refinequestion_desc": "Harness AI-powered critical thinking to craft precise, powerful questions that unlock deeper insights and better solutions", "why_funblocks": "Why Choose FunBlocks AI?", "get_started": "Get Started", "ai_powered_intelligence": "Advanced AI Intelligence", "ai_powered_intelligence_desc": "Powered by cutting-edge large language models, our tools provide deep insights, creative solutions, and intelligent automation across all features. Experience AI that truly understands and enhances your work.", "integrated_toolset": "Comprehensive Toolset", "integrated_toolset_desc": "Our AI tool suite, from mind mapping to presentation creation, is designed to cater to specific contexts and needs, simplifying usage and enhancing accuracy while enabling ongoing exploration and creation on the FunBlocks AI platform.", "user_friendly": "Innovative AI Interaction Experience", "user_friendly_desc": "Our tools redefine the interaction experience with AI, providing a visual interface and structured expression that is easy to use and aligns with the brain's processing and exploration needs. Complex AI functions and prompts are simplified into one-click operations, making advanced features accessible to every user.", "mental_models_toolify": "Toolification of Classic Mental Models", "mental_models_toolify_desc": "We combine classic mental models with AI to provide exceptional thinking coaching and guidance, allowing the accumulated wisdom of humanity to benefit everyone in the form of tools.", "brainstorming_intro_title": "AI-Powered Brainstorming to Ignite Unlimited Creativity", "brainstorming_intro_description": "Let <PERSON> be your creative partner, breaking through mental barriers and discovering new possibilities", "brainstorming_feature_1_title": "Diverse Perspectives", "brainstorming_feature_1_text": "AI generates ideas from multiple angles, breaking through the limitations of individual thinking", "brainstorming_feature_2_title": "Structured Exploration", "brainstorming_feature_2_text": "Organized approach to idea generation with categorized suggestions and mind mapping", "brainstorming_feature_3_title": "Instant Inspiration", "brainstorming_feature_3_text": "Generate creative ideas instantly for any topic, challenge, or project", "brainstorming_benefits_title": "Benefits of AI-Powered Brainstorming", "brainstorming_benefit_1": "Overcome creative blocks with fresh perspectives", "brainstorming_benefit_2": "Save time in ideation phases with instant idea generation", "brainstorming_benefit_3": "Explore unexpected connections between concepts", "brainstorming_benefit_4": "Create comprehensive solution sets for complex problems", "brainstorming_usecases_title": "Popular Applications", "brainstorming_usecase_1_title": "Content Creation", "brainstorming_usecase_1_text": "Generate creative content ideas for blogs, social media, and marketing", "brainstorming_usecase_2_title": "Problem Solving", "brainstorming_usecase_2_text": "Explore diverse solutions to business, technical, or personal challenges", "brainstorming_usecase_3_title": "Project Planning", "brainstorming_usecase_3_text": "Develop comprehensive project approaches and execution strategies", "brainstorming_usecase_4_title": "Learning & Research", "brainstorming_usecase_4_text": "Explore topics from multiple angles to deepen understanding", "why_not_chatgpt": "Why Not Just Use ChatGPT?", "chatgpt_comparison_intro": "While ChatGPT is powerful, FunBlocks AI Tools offer unique advantages that make information processing and learning more effective:", "comparison_visual_title": "Visual Learning vs Text Overload", "chatgpt_text_heavy": "Generates walls of text that are time-consuming to read and process", "funblocks_visual_friendly": "Creates visual mind maps, infographics, and slides for faster understanding and better retention", "comparison_exploration_title": "Exploration Freedom vs Linear Constraints", "chatgpt_linear_chat": "Linear chat interface limits exploration to a narrow path", "funblocks_multi_perspective": "Whiteboard and mind map interface enables multi-dimensional exploration of ideas", "comparison_guidance_title": "Proactive Guidance vs Passive Responses", "chatgpt_passive_waiting": "Passively waits for user questions, which can be challenging to formulate", "funblocks_proactive_guide": "Proactively generates exploration paths and guides users through complex topics", "comparison_learning_title": "Skill Building vs Quick Answers", "chatgpt_answer_only": "Simply provides answers without developing critical thinking skills", "funblocks_thinking_process": "Collaborates with users to explore solutions while building analytical capabilities", "criticalthinking_intro_title": "Beyond Intuition, Into Deep Analysis", "criticalthinking_intro_description": "AI-powered critical thinking tools that help you develop professional analytical frameworks and clear decision paths", "criticalthinking_feature_1_title": "Structured Analysis", "criticalthinking_feature_1_text": "Apply systematic critical thinking frameworks to any problem or question", "criticalthinking_feature_2_title": "Cognitive Bias Detection", "criticalthinking_feature_2_text": "Identify potential thinking errors and logical fallacies in arguments", "criticalthinking_feature_3_title": "Multi-perspective Evaluation", "criticalthinking_feature_3_text": "Analyze issues from multiple angles to develop comprehensive understanding", "criticalthinking_benefits_title": "Benefits of Critical Thinking", "criticalthinking_benefit_1": "Make more rational, well-reasoned decisions", "criticalthinking_benefit_2": "Develop stronger arguments and logical reasoning skills", "criticalthinking_benefit_3": "Avoid common cognitive pitfalls and biases", "criticalthinking_benefit_4": "Improve problem-solving efficiency with structured approaches", "criticalthinking_frameworks_title": "Critical Thinking Frameworks", "criticalthinking_framework_1_title": "Socratic Questioning", "criticalthinking_framework_1_text": "Probe assumptions and evidence through systematic questioning", "criticalthinking_framework_2_title": "PESTEL Analysis", "criticalthinking_framework_2_text": "Examine political, economic, social, technological, environmental and legal factors", "criticalthinking_framework_3_title": "<PERSON>'s Taxonomy", "criticalthinking_framework_3_text": "Progress through understanding, application, analysis, evaluation, and creation", "criticalthinking_framework_4_title": "Systems Thinking", "criticalthinking_framework_4_text": "Analyze complex systems by examining interconnections and feedback loops", "mindmaps": "Mind Maps", "slides": "Slides", "infographics": "Infographics", "ai_tools_heading": "FunBlocks AI Tools", "ai_tools_description": "The Entrances to Clear Mind and Creative Success", "thinking_benefits_title": "Empowering Your Mind", "deep_thinking": "Deepen Thinking", "deep_thinking_desc": "Analyze cognitive biases and logical fallacies\nRefine questions for better problem-solving\nMake data-driven decisions\nDevelop systematic analytical frameworks\nStrengthen reasoning abilities through AI coaching", "enhanced_productivity": "Enhanced Productivity", "enhanced_productivity_desc": "One-click generation of professional presentations\nInstant creation of engaging infographics\nAI-powered mind mapping for complex topics\nAutomated task planning and breakdown", "boosted_creativity": "Boosted Creativity", "boosted_creativity_desc": "AI-enhanced brainstorming sessions\nIntegration with classic mental models\nUnlimited canvas for idea exploration\nInteractive mind mapping\nCross-pollination of ideas through AI insights", "ai_lessonplans_desc": "Generate comprehensive, research-based lesson plans instantly with just a topic input. Transform your teaching with AI-powered instructional design expertise", "app_slogan_lessonplans": "Smart Lesson Planning, Every Class Extraordinary", "app_one_sentence_lessonplans": "Generate comprehensive, research-based lesson plans instantly with just a topic input. Transform your teaching with AI-powered instructional design expertise", "ai_teachingslides_desc": "Empower every educator to design expert-level teaching content and focus on the art of teaching, not tedious slide creation", "app_slogan_teachingslides": "Create Professional Teaching Slides with AI", "app_one_sentence_teachingslides": "Empower every educator to design expert-level teaching content and focus on the art of teaching, not tedious slide creation", "ai_dokassessment_desc": "Delivers professional-grade evaluations with cognitive depth analysis, implementation guides, and quality standards - all in minutes", "app_slogan_dokassessment": "Create Expert-Level Educational Assessments with AI", "app_one_sentence_dokassessment": "Delivers professional-grade evaluations with cognitive depth analysis, implementation guides, and quality standards - all in minutes", "ai_bloom_desc": "Transform any topic into professional educational mind maps with Bloom's Taxonomy and AI, in just one click", "app_slogan_bloom": "Teaching Design, Brilliantly Reimagined", "app_one_sentence_bloom": "Transform any topic into professional educational mind maps with Bloom's Taxonomy and AI, in just one click", "ai_solo_desc": "Build structured learning using the SOLO Taxonomy, breaking down topics into progressive cognitive levels for effective teaching and learning.", "app_slogan_solo": "Transform Any Topic into Structured Learning with SOLO Taxonomy", "app_one_sentence_solo": "AI-powered educational planning tool that breaks down topics into progressive cognitive levels for effective teaching and learning", "ai_dok_desc": "Generate comprehensive curriculum mind maps in seconds with our Webb's DOK-powered tool", "app_slogan_dok": "AI DOKBrain: The Intelligence Behind Deeper Learning", "app_one_sentence_dok": "From recall to creation, design complete learning journeys across all cognitive levels", "ai_dok_assessment_desc": "Create comprehensive educational assessments based on Webb's DOK framework with AI-powered analysis and professional quality", "app_slogan_dok_assessment": "AI DOK Assessment: Smart Assessment Design, Professional Results", "app_one_sentence_dok_assessment": "Transform any teaching topic into professional, multi-level assessments with DOK-based cognitive progression", "ai_marzano_desc": "Transform any topic into a comprehensive learning design using <PERSON><PERSON>'s Taxonomy of Educational Objectives", "app_slogan_marzano": "Intelligent Learning Design Based on Marzano's Taxonomy", "app_one_sentence_marzano": "Transform any topic into a comprehensive learning journey through all cognitive dimensions", "ai_layeredexplanation_desc": "Scale any concept through progressive layers of knowledge with AI MindLadder, from intuitive basics to profound insights", "app_slogan_layeredexplanation": "AI MindLadder: Climb to Complete Understanding", "app_one_sentence_layeredexplanation": "Scale any concept through progressive layers of knowledge with our 8-tier learning system", "erase_watermark": "Erase Watermarks", "edit_image": "Edit Image", "edit_instructions": "Editing Instructions", "image_edit_placeholder": "Describe what you want to change in the image (e.g., 'remove the background', 'make it brighter', 'change the sky to sunset colors')", "ai_erase_desc": "One-click watermark removal to restore the true beauty of your images", "app_slogan_erase": "AI Erase - Remove Watermarks Instantly, Preserve Image Beauty", "app_one_sentence_erase": "Advanced AI technology that intelligently identifies and removes watermarks while maintaining image integrity", "ai_avatar_desc": "Instantly convert your photos into stylized avatars in a wide range of artistic styles", "app_slogan_avatar": "AI Avatar Generator | Transform Your Photos into Stunning Digital Art!", "app_one_sentence_avatar": "Instantly convert your photos into high-quality, stylized avatars in a wide range of artistic styles!", "ai_imageeditor_desc": "Edit images using natural language instructions - no Photoshop skills required", "app_slogan_imageeditor": "AI Image Editor - Transform Images with Natural Language", "app_one_sentence_imageeditor": "Edit photos and images using simple text instructions - just describe what you want to change and our AI will do the rest!", "ai_sketch_desc": "Transform your simple drawings into stunning artwork with AI-powered style enhancement", "app_slogan_sketch": "AI Sketch - Turn Your Drawings into Art", "app_one_sentence_sketch": "Draw simple sketches and watch AI transform them into professional artwork in multiple artistic styles", "llm_support_title": "Support for All Major Large Language Models", "llm_support_desc": "Access all industry-leading models in one place to enhance your learning and work efficiency. No need to switch between platforms or purchase memberships for each one.", "target_audience_title": "Who is FunBlocks AI Tools For?", "target_audience_desc": "FunBlocks AI Tools are designed for anyone who wants to enhance their learning, thinking, and productivity through AI-powered tools.", "target_audience_students": "Students", "target_audience_students_desc": "Perfect for students looking to improve their study methods, create better notes, and understand complex topics.", "target_audience_professionals": "Professionals", "target_audience_professionals_desc": "Ideal for professionals who need to organize ideas, create presentations, and enhance their decision-making process.", "target_audience_creatives": "Creatives", "target_audience_creatives_desc": "Great for creative professionals who want to explore ideas, generate content, and visualize concepts.", "use_cases_title": "Real-World Applications", "use_cases_desc": "Discover how FunBlocks AI can transform your work and learning experience with these practical examples.", "use_case_1_title": "Academic Excellence", "use_case_1_desc": "Create comprehensive mind maps for complex subjects, generate study guides, and improve your understanding of difficult concepts.", "use_case_2_title": "Business Innovation", "use_case_2_desc": "Develop business strategies, create professional presentations, and analyze market trends with AI-powered insights.", "use_case_3_title": "Creative Projects", "use_case_3_desc": "Transform your ideas into visual stories, generate creative content, and explore new perspectives with AI assistance.", "use_case_4_title": "In-Depth Research & Analysis", "use_case_4_desc": "Conduct thorough research from multiple perspectives, delving deeply into topics and questions for comprehensive understanding.", "use_case_5_title": "Personal Growth & Development", "use_case_5_desc": "Chart your learning journey using visual frameworks. Foster critical thinking through AI-enhanced insights and a multi-perspective interface.", "use_case_6_title": "Team Collaboration", "use_case_6_desc": "Create shared knowledge maps, facilitate brainstorming sessions, and improve team communication with visual thinking tools.", "platform_faq_1_q": "What makes FunBlocks AI different from other AI platforms?", "platform_faq_1_a": "FunBlocks AI stands out by focusing on visual thinking rather than just text. While most AI platforms produce text-heavy outputs, we transform ideas into visual maps, infographics, and presentations. Our platform combines multiple AI models, specialized tools for different thinking styles, and a user-friendly interface designed to enhance your creative and analytical processes.", "platform_faq_2_q": "Do I need to be tech-savvy to use FunBlocks AI?", "platform_faq_2_a": "Not at all! FunBlocks AI is designed with simplicity in mind. Our user-friendly interface guides you through each tool with clear instructions. Just input your ideas or questions, and our AI will handle the complex work of organizing and visualizing information. Whether you're a technology enthusiast or new to AI tools, you'll find FunBlocks approachable and intuitive.", "platform_faq_3_q": "Which AI models does FunBlocks support?", "platform_faq_3_a": "FunBlocks AI integrates all leading language models including GPT-4, Claude, Gemini, and other cutting-edge AI systems. We're continuously adding support for new models as they become available. You can easily switch between different models depending on your specific needs and preferences, all through a single unified interface.", "platform_faq_4_q": "Can I use FunBlocks AI for educational purposes?", "platform_faq_4_a": "Absolutely! Education is one of our core focus areas. FunBlocks AI offers specialized educational frameworks like BloomBrain, MarzanoBrain, SOLOBrain, and DOKBrain that align with established learning taxonomies. These tools help students organize complex topics, create study guides, and deepen understanding through visual learning. Teachers can use our platform to create engaging instructional materials and assess student comprehension.", "platform_faq_5_q": "How does FunBlocks AI help with business and professional work?", "platform_faq_5_a": "FunBlocks AI transforms business thinking through tools like OKR Assistant for goal setting, Business Model Analyzer for strategy development, and AI Task Planner for project management. The platform also excels at creating professional presentations, organizing competitive research, facilitating team brainstorming, and visualizing complex business concepts. These tools help professionals make better decisions, communicate more effectively, and drive innovation.", "platform_faq_6_q": "Is my data secure when using FunBlocks AI?", "platform_faq_6_a": "We take data security very seriously. All data transmitted to and from our platform is encrypted using industry-standard protocols. We don't store your content any longer than necessary to provide our services, and we never use your data to train our models without explicit consent. Our privacy policy clearly outlines how we protect your information and the measures we take to ensure confidentiality.", "platform_faq_7_q": "What is the pricing model for FunBlocks AI?", "platform_faq_7_a": "FunBlocks AI offers a flexible tiered pricing structure to accommodate different needs. We provide a free tier with access to essential tools and limited usage, perfect for individuals just getting started. Our premium plans include unlimited access to all tools, priority processing, and advanced features. Visit our pricing page for detailed information on current plans and pricing.", "platform_faq_8_q": "Can I share and collaborate on projects created with FunBlocks AI?", "platform_faq_8_a": "Yes! Collaboration is a key feature of FunBlocks AI. You can easily share your mind maps, infographics, and presentations with team members or colleagues. You can export your projects in various formats for presentations, reports, or further editing in other software.", "platform_faq_9_q": "How often does FunBlocks AI add new features and tools?", "platform_faq_9_a": "We're constantly evolving! Our development team releases new tools and improvements on a regular basis. We listen closely to user feedback and industry trends to prioritize the most valuable additions. Major feature updates typically occur monthly, with smaller improvements and fixes deployed weekly. Subscribe to our newsletter to stay informed about the latest enhancements and additions to the platform.", "platform_faq_10_q": "Can FunBlocks AI help with creative projects and content creation?", "platform_faq_10_a": "Absolutely! FunBlocks AI excels at boosting creativity through tools like AI Brainstorming, AI MindKit, and AI Poetic Lens. These tools help generate unique ideas, explore different perspectives, and visualize creative concepts. Content creators use our platform to plan content strategies, develop storylines, create visual storyboards, and organize creative projects—all with the assistance of advanced AI models that enhance rather than replace human creativity.", "platform_faq_11_q": "How do AI Tools and AIFlow work together?", "platform_faq_11_a": "AIFlow is the main platform that powers everything, while AI Tools are easy-to-use applications that help you quickly access features within AIFlow. These tools are designed for specific tasks, making it simple for you to explore and create with AIFlow.", "platform_faq_12_q": "What if I can't find the tool I need in AI Tools?", "platform_faq_12_a": "If you can't find a specific tool in AI Tools, don't worry! You can still use AIFlow directly to get the job done. While AI Tools are convenient, they may not cover every need, but AIFlow has all the capabilities you require.", "platform_faq_13_q": "How does FunBlocks AI compare to other visual thinking tools?", "platform_faq_13_a": "Unlike traditional visual thinking tools that require manual creation, FunBlocks AI automatically generates visual content based on your input. Our AI-powered approach saves time, provides deeper insights, and offers multiple perspectives that might not be immediately obvious. We combine the best of AI language models with visual thinking principles to create a unique tool that enhances both creativity and analytical thinking.", "platform_faq_14_q": "Can FunBlocks AI help with learning complex subjects?", "platform_faq_14_a": "Absolutely! FunBlocks AI excels at breaking down complex subjects into understandable visual formats. Our educational tools like MindLadder, BloomBrain, and SOLOBrain are specifically designed to facilitate learning by organizing information according to established educational frameworks. These tools help you visualize connections between concepts, identify knowledge gaps, and build a comprehensive understanding of any subject.", "platform_faq_15_q": "How does FunBlocks AI ensure the quality of generated content?", "platform_faq_15_a": "We employ several strategies to ensure high-quality outputs. First, we use only the most advanced AI models available. Second, we've developed specialized prompting techniques that guide these models to produce well-structured, accurate, and relevant content. Third, our tools are designed with specific frameworks that organize information logically. Finally, we continuously refine our systems based on user feedback to improve quality over time.", "aiflow_and_aitools_title": "What is the Relationship Between AI Tools and AIFlow?", "aitools_not_covered": "Can't find what you need in AI Tools? No problem! Use AIFlow directly.", "try_aiflow_now": "Try FunBlocks AIFlow Now", "about_aiflow_title": "AIFlow: Your Foundation for AI", "about_aiflow_point1": "The core platform for all AI Tools.", "about_aiflow_point2": "A flexible UI that offers a limitless canvas for your ideas.", "about_aiflow_point3": "Access everything you need directly.", "about_aitools_title": "AI Tools: Easy-to-Use Solutions", "about_aitools_point1": "Ready-made applications and demos for quick use.", "about_aitools_point2": "Designed for ease of use.", "about_aitools_point3": "Solutions tailored to specific scenarios.", "continue_exploring_ai_content": "The journey of exploration after AI content generation", "what_after_generation": "What comes next after AI generates content?", "ai_tools_generation_explanation": "Your journey of exploration doesn't have to end after generating content with AI Tools.", "continue_with_aiflow": "Continue your exploration with AIFlow", "click_continue_to_explore": "Click the 'Keep exploring with AI<PERSON><PERSON>' button", "access_funblocks_aiflow": "Access the FunBlocks AIFlow interface", "hero_badge_1": "AI-POWERED VISUAL TOOLS", "hero_badge_2": "CREATIVITY AND PRODUCTIVITY", "hero_heading_1": "Transform", "hero_heading_2": "Your Thinking with", "hero_heading_3": "Visual AI Tools", "hero_description": "Visualize complex concepts, enhance learning, and boost productivity through AI-powered visual thinking.", "discover_categories": "Discover Our AI Tool Categories", "stat_tools": "AI Tools", "stat_thinking": "Faster Thinking", "stat_assistance": "AI Assistance", "deep_dive_capabilities": "Dive deeper into more advanced features and possibilities", "model_openai": "OpenAI GPT", "model_openai_desc": "Leverage the power of GPT series for creative content generation and complex problem-solving.", "model_anthropic": "Anthropic <PERSON>", "model_anthropic_desc": "Use Claude for technical and scientific content, nuanced understanding, reasoning, and exceptional coding capabilities", "model_google": "Google Gemini", "model_google_desc": "Access Google's multimodal AI capabilities for integrated text, image, and code understanding.", "model_deepseek": "Deepseek AI", "model_deepseek_desc": "Specialized models for nuanced understanding, reasoning, detailed explanations with minimal hallucinations.", "model_mistral": "Mistral AI", "model_mistral_desc": "Efficient models balancing performance and speed for everyday tasks and specialized domains.", "model_cohere": "Cohere", "model_cohere_desc": "Advanced models for text generation, summarization and semantic search applications.", "infographic_howto_section_title": "How to Use the AI Infographic Creator?", "infographic_howto_section_description": "Transform information into visually stunning infographics with these easy steps", "infographic_howto_step1_title": "Input Content", "infographic_howto_step1_description": "Enter a topic or paste text into the generation interface that you want to transform into an infographic", "infographic_howto_step2_title": "Customize Settings", "infographic_howto_step2_description": "Adjust additional settings as needed, such as generation language (default is English) and style options for your infographic", "infographic_howto_step3_title": "Instant Infographic Generation", "infographic_howto_step3_description": "Click generate and watch as AI creates a insightful infographic", "infographic_howto_step4_title": "Download or Share", "infographic_howto_step4_description": "Click the Download button to save your infographic or use the Share option to distribute it to others", "infographic_howto_tips_title": "Tips for Great Infographics", "infographic_howto_tip_1": "Focus on clear, concise information to create more effective visuals", "infographic_howto_tip_2": "Try different visual styles to find the one that best communicates your data", "infographic_howto_tip_3": "For complex topics, consider breaking content into multiple infographics focusing on different aspects", "mindmap_howto_section_title": "How to Use the AI Mindmap Tool?", "mindmap_howto_section_description": "Create informative and visually engaging mindmaps with these simple steps", "mindmap_howto_step1_title": "Enter Your Topic", "mindmap_howto_step1_description": "Depending on what's supported in the interface, enter a topic, paste some text, or provide a link—such as to a webpage or video—to start mapping your content", "mindmap_howto_step2_title": "Customize Settings", "mindmap_howto_step2_description": "Adjust options like generation language (default is English), mental models to guide your AI", "mindmap_howto_step3_title": "Instant Mindmap Generation", "mindmap_howto_step3_description": "Click generate and watch as AI creates a comprehensive mind map with multiple branches of ideas", "mindmap_howto_step4_title": "Explore in AIFlow", "mindmap_howto_step4_description": "Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support", "mindmap_howto_tips_title": "Tips for Better Mindmaps", "mindmap_howto_tip_1": "Use specific, focused topics for more detailed and useful mindmaps", "mindmap_howto_tip_2": "Experiment with different settings, such as mental models to see varied perspectives on your topic", "mindmap_howto_tip_3": "For complex subjects, try breaking them down into multiple mindmaps focusing on different aspects", "slides_howto_section_title": "How to Use the FunBlocks AI Slides Creator?", "slides_howto_section_description": "Elevate your content into engaging, visually stunning slides with these simple steps", "slides_howto_step1_title": "Input Content", "slides_howto_step1_description": "Enter a topic or paste text into the generation interface that you want to transform into an slides", "slides_howto_step2_title": "Customize Settings", "slides_howto_step2_description": "Adjust options like generation language (default is English) to suit your needs", "slides_howto_step3_title": "Instant Slides Generation", "slides_howto_step3_description": "Click generate and watch as AI creates a comprehensive slides", "slides_howto_step4_title": "Present or Share Slides Online, or Continue Editing with FunBlocks AI Slides", "slides_howto_step4_description": "After generating your slides, you can present them directly, share them online with others, or continue refining them using FunBlocks AI Slides for further customization.", "slides_howto_tips_title": "Tips for Creating Effective Slides", "slides_howto_tip_1": "Choose specific, focused topics or provide detailed text to create more informative and valuable slides.", "slides_howto_tip_2": "Experiment with different settings to discover the best way to convey your ideas.", "slides_howto_tip_3": "For complex subjects, consider using AIFlow for brainstorming first, then generate slides using the mindmap of explored ideas.", "photo_howto_section_title": "How to Use the FunBlocks AI Photo Tools?", "photo_howto_section_description": "Enhance your photography skills with personalized AI feedback in just a few steps", "photo_howto_step1_title": "Upload Your Photo", "photo_howto_step1_description": "Use the upload interface to select and upload a photograph you'd like feedback on", "photo_howto_step2_title": "Customize Settings", "photo_howto_step2_description": "Adjust options like generation language (default is English) to suit your needs", "photo_howto_step3_title": "Instant Feedback Generation", "photo_howto_step3_description": "Click generate and watch as AI creates a comprehensive mindmap or desired photo", "photo_howto_step4_title": "Explore in AIFlow", "photo_howto_step4_description": "Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support", "photo_howto_tips_title": "Tips for Better Feedback", "photo_howto_tip_1": "Upload high-quality images for more accurate and detailed feedback", "photo_howto_tip_2": "Try different types of photographs to get varied insights about your photography style", "photo_howto_tip_3": "Save feedback to track your progress and improvement over time", "video_howto_section_title": "How to Use FunBlocks AI Vidoe Suammarization Tool?", "video_howto_section_description": "Create informative and visually engaging video summarization mindmaps with these simple steps", "video_howto_step1_title": "Enter Your Youtube Video Link", "video_howto_step1_description": "Use the generation interface to provide a link to the Youtube video you want to map", "video_howto_step2_title": "Customize Settings", "video_howto_step2_description": "Adjust options like generation language (default is English) to suit your needs", "video_howto_step3_title": "Instant Mindmap Generation", "video_howto_step3_description": "Click generate and watch as AI creates a comprehensive mind map with multiple branches of ideas", "video_howto_step4_title": "Explore in AIFlow", "video_howto_step4_description": "Click 'Explore in AIFlow' to continue working with your mindmap in FunBlocks AIFlow's infinite canvas with AI assistant support", "mindsnap_howto_section_title": "How to Use MindSnap?", "mindsnap_howto_section_description": "Transform any topic into visual insights using mental models with these simple steps", "mindsnap_howto_step1_title": "Enter Your Topic", "mindsnap_howto_step1_description": "Input any topic, question, or challenge you want to analyze through the lens of mental models", "mindsnap_howto_step2_title": "Select a Mental Model (Optional)", "mindsnap_howto_step2_description": "Choose a specific mental model or let AI select the most appropriate one for your topic", "mindsnap_howto_step3_title": "Generate Visual Insights", "mindsnap_howto_step3_description": "Click generate and watch as AI analyzes your topic through the selected mental model and creates a beautiful infographic", "mindsnap_howto_step4_title": "Download or Share", "mindsnap_howto_step4_description": "Save your infographic as an SVG file or share it directly with others", "mindsnap_howto_tips_title": "Tips for Better Results", "mindsnap_howto_tip_1": "Be specific with your topic to get more targeted and useful insights", "mindsnap_howto_tip_2": "Try analyzing the same topic through different mental models to gain multiple perspectives", "mindsnap_howto_tip_3": "For complex topics, break them down into smaller, more focused questions for deeper analysis", "insightcards_howto_section_title": "How to Use InsightCards", "insightcards_howto_section_description": "Creating powerful insight cards is simple with our intuitive process:", "insightcards_howto_step1_title": "Enter Your Topic", "insightcards_howto_step1_description": "Type in any concept, idea, or question you want to explore more deeply.", "insightcards_howto_step2_title": "Select a Card Type", "insightcards_howto_step2_description": "Choose from multiple insight frameworks based on your learning goals.", "insightcards_howto_step3_title": "Generate Your InsightCard", "insightcards_howto_step3_description": "Our AI analyzes your topic and creates a visually appealing insight card.", "insightcards_howto_step4_title": "Share and Learn", "insightcards_howto_step4_description": "Download your card, share it with others, or save it to your collection.", "insightcards_howto_tips_title": "Tips for Better InsightCards", "insightcards_howto_tip_1": "Be specific with your topic to get more focused and meaningful insights", "insightcards_howto_tip_2": "Try different card types on the same topic to gain multiple perspectives", "insightcards_howto_tip_3": "Use the generated insights as starting points for deeper exploration and discussion", "app_slogan_promptoptimizer": "Unlock AI Potential: Optimize Your Prompts", "app_one_sentence_promptoptimizer": "Unlock the full potential of AI with clearer, more effective prompts. Get better results, faster", "ai_promptoptimizer_desc": "Unlock the full potential of AI with clearer, more effective prompts. Get better results, faster", "prompt-optimizer_howto_section_title": "Optimizing Your AI Prompts Made Easy", "prompt-optimizer_howto_section_description": "Discover how to refine your AI interactions with these straightforward steps:", "prompt-optimizer_howto_step1_title": "Enter Your Initial Prompt", "prompt-optimizer_howto_step1_description": "Start by inputting the original prompt or question you'd like to optimize. This can range from simple inquiries to complex instructions.", "prompt-optimizer_howto_step2_title": "Select Your Language", "prompt-optimizer_howto_step2_description": "Choose the language for your optimized prompt from our list of available options.", "prompt-optimizer_howto_step3_title": "Get Your Optimized Prompt", "prompt-optimizer_howto_step3_description": "Our AI Prompt Optimizer will analyze your input and generate a refined prompt for you.", "prompt-optimizer_howto_step4_title": "Test and Refine Further", "prompt-optimizer_howto_step4_description": "Put your optimized prompt to the test with your AI system, making any necessary adjustments based on the results.", "prompt-optimizer_howto_tips_title": "Tips for Crafting Effective Prompts", "prompt-optimizer_howto_tip_1": "Clearly define your desired outcome or goal in your initial prompt.", "prompt-optimizer_howto_tip_2": "Avoid including detailed steps in your initial prompt to allow for the full potential of LLM.", "prompt-optimizer_howto_tip_3": "Use the generated prompt as a starting point and iterate for further optimization.", "case_studies_badge": "Real-World Applications", "case_studies_title": "Success Stories: {{appname}} in Action", "case_studies_cta_text": "Ready to create your own success story?", "case_studies_cta_button": "Start Creating Now", "testimonials_title": "What Our Users Say", "testimonials_description": "Join thousands of users who have transformed their experience with {{appname}}.", "testimonials_rating": "{{rating}} average rating", "testimonials_users": "{{users}}+ active users", "comparison_winner": "Best Overall Solution", "comparison_value": "Best Value", "case_study_1_title": "Educational Institution Transformation", "case_study_1_industry": "Higher Education", "case_study_1_challenge": "A leading university struggled with helping students visualize complex concepts across multiple disciplines, resulting in lower comprehension and retention rates.", "case_study_1_solution": "Implemented a comprehensive suite of FunBlocks AI tools across departments: MarzanoBrain and BloomBrain for creating structured learning materials, Slides for generating interactive presentations, and Brainstorming, Critical Thinking, and Creative Thinking tools to develop students' cognitive abilities.", "case_study_1_results": "Significant improvement in student comprehension and retention rates. Students demonstrated enhanced critical thinking skills and ability to connect concepts across disciplines. Faculty reported more engaging classroom discussions and higher quality student work.", "case_study_2_title": "Innovation Thinking Enhancement", "case_study_2_industry": "Product Design & Marketing", "case_study_2_challenge": "A multinational company struggled with fostering innovative thinking among their product design and marketing teams, resulting in predictable solutions and declining market differentiation.", "case_study_2_solution": "Integrated FunBlocks AI Brainstorming, MindKit, MindSnap, OKR Assistant, Task Planner and other FunBlocks AI tools into their ideation process. Teams used these tools to explore multiple perspectives, challenge assumptions, and visualize connections between seemingly unrelated concepts.", "case_study_2_results": "Teams developed more innovative product designs and marketing campaigns that resonated with customers. The company reported increased creative output, more diverse solution sets, and improved cross-team collaboration on complex projects.", "case_studies_description": "See how organizations across different sectors have leveraged FunBlocks AI tools to solve real challenges and achieve measurable results.", "research_title": "Research-Backed Approach", "research_description": "Our tools are built on solid scientific foundations and proven cognitive principles to maximize learning and productivity.", "research_area_1_title": "Cognitive Load Theory", "research_area_1_description": "Our visual tools reduce cognitive load by organizing information spatially, allowing users to process complex concepts more efficiently while minimizing mental effort.", "research_area_2_title": "Visual Learning Efficacy", "research_area_2_description": "Research shows visual learning can improve understanding by up to 400% and retention by 38% compared to text-only learning, making complex information more accessible and memorable.", "research_area_3_title": "Mental Models & Frameworks", "research_area_3_description": "Our tools leverage established mental models and educational frameworks that help structure thinking, improve problem-solving capabilities, and enhance conceptual understanding across disciplines.", "research_area_4_title": "AI-Enhanced Learning", "research_area_4_description": "Studies show that AI-assisted learning tools can personalize the educational experience, provide adaptive feedback, and improve outcomes across diverse learning styles and contexts.", "testimonial_1_content": "<PERSON><PERSON><PERSON><PERSON> completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I've cut my study time by 30% while improving my grades!", "testimonial_1_role": "Medical Student", "testimonial_2_content": "As a product manager, I need to communicate complex ideas to different teams. The AI Infographic Generator has become my secret weapon for creating visually stunning concept maps and comparison charts in minutes instead of hours.", "testimonial_2_role": "Product Manager", "testimonial_3_content": "I've tried many brainstorming tools, but FunBlocks AI Brainstorming Assistant is in a league of its own. It doesn't just generate ideas - it helps structure and refine them in ways I wouldn't have considered. It's like having a creative thinking partner available 24/7.", "testimonial_3_role": "Creative Director", "common_questions": "Common Questions", "faq_description": "Find answers to commonly asked questions about FunBlocks AI tools and how they can help you.", "faq_category_general": "About FunBlocks AI", "faq_category_features": "Tools & Features", "faq_category_usage": "Use Cases & Applications", "faq_category_technical": "Technical & Support", "faq_need_more_help": "Need More Help?", "faq_support_text": "If you have questions not covered here, our support team is ready to help. You can also check our detailed documentation for more information.", "contact_support": "Contact Support", "view_documentation": "View Documentation", "llm_support_notes": "Access all these models and more through a single, unified interface. Switch between models seamlessly based on your task requirements.", "category_mindmap_title": "Mindmaps", "category_mindmap_desc": "AI-powered mind maps for learning, planning, and creative thinking", "category_infographics_title": "Infographics", "category_infographics_desc": "Transform ideas into beautiful visual content with AI", "category_slides_title": "Slides", "category_slides_desc": "Create professional presentations in seconds with AI", "category_images_title": "Images", "category_images_desc": "AI-powered image generation and enhancement tools", "category_text_title": "Text", "category_text_desc": "AI-powered text content generation", "examples_showcase_title": "See Our Tools in Action", "examples_showcase_desc": "Explore real examples of what you can create with our AI-powered tools", "example_mindmap_title": "AI Mindmap Example", "example_mindmap_desc": "Visualize complex topics with our AI Mindmap tool. This example shows a mindmap about the book \"The Great Gatsby\".", "example_infographic_title": "AI Infographic Example", "example_infographic_desc": "Create beautiful infographics instantly. This example shows a witty and attractive infographic generated with InsightCards.", "example_slides_title": "AI Slides Example", "example_slides_desc": "Generate professional presentations in seconds. This example shows a competitor analysis presentation.", "example_mindsnap_title": "AI MindSnap Example", "example_mindsnap_desc": "Transform topics into visual mental models. This example shows a SWOT analysis.", "lesson-plans_howto_section_title": "How to Use the AI Lesson Plans Tool?", "lesson-plans_howto_section_description": "Create professional, framework-based lesson plans with these simple steps", "lesson-plans_howto_step1_title": "Enter Your Teaching Topic", "lesson-plans_howto_step1_description": "Input your lesson topic, subject area, or learning objectives to start creating your lesson plan", "lesson-plans_howto_step2_title": "Select Teaching Framework", "lesson-plans_howto_step2_description": "Choose from proven pedagogical frameworks like Bloom's Taxonomy, Marzano's Strategies, or ADDIE Model", "lesson-plans_howto_step3_title": "Customize Settings", "lesson-plans_howto_step3_description": "Adjust educational level, duration, and specific requirements for your classroom context", "lesson-plans_howto_step4_title": "Generate & Refine", "lesson-plans_howto_step4_description": "Get a comprehensive lesson plan with objectives, activities, assessments, and resources that you can further customize", "lesson-plans_howto_tips_title": "Tips for Better Lesson Plans", "lesson-plans_howto_tip_1": "Be specific about learning objectives and target audience for more tailored lesson plans", "lesson-plans_howto_tip_2": "Experiment with different teaching frameworks to find the best approach for your content", "lesson-plans_howto_tip_3": "Consider your classroom environment and available resources when customizing the generated plan", "dok-assessment_howto_section_title": "How to Use FunBlocks AI Assessment", "dok-assessment_howto_section_description": "Create professional DOK-based assessments in just a few simple steps. Our AI handles the complexity while you focus on teaching.", "dok-assessment_howto_step1_title": "Enter Your Teaching Topic", "dok-assessment_howto_step1_description": "Input any subject, lesson, or learning objective you want to assess. Be as specific or general as you like.", "dok-assessment_howto_step2_title": "AI Analyzes Content", "dok-assessment_howto_step2_description": "Our AI identifies key concepts, determines optimal DOK distribution, and plans comprehensive assessment coverage.", "dok-assessment_howto_step3_title": "Review Generated Assessment", "dok-assessment_howto_step3_description": "Examine the complete assessment with questions across all DOK levels, rubrics, and implementation guidelines.", "dok-assessment_howto_step4_title": "Customize and Implement", "dok-assessment_howto_step4_description": "Modify questions, adjust difficulty levels, and adapt the assessment to your specific teaching context and standards.", "dok-assessment_howto_tips_title": "Pro Tips for Better Assessments", "dok-assessment_howto_tip_1": "Be specific about grade level and subject area for more targeted questions", "dok-assessment_howto_tip_2": "Specify assessment type (formative/summative) for appropriate question styles", "dok-assessment_howto_tip_3": "Include learning objectives to ensure perfect alignment with your curriculum", "example_try_tool": "Try this tool"}}}, "initialLocale": "en", "ns": ["marzano", "common"], "userConfig": {"i18n": {"defaultLocale": "en", "locales": ["en", "zh"]}, "localePath": "/Users/<USER>/workspace/wise-card/public/locales", "trailingSlash": true, "default": {"i18n": {"defaultLocale": "en", "locales": ["en", "zh"]}, "localePath": "/Users/<USER>/workspace/wise-card/public/locales", "trailingSlash": true}}}}, "__N_SSG": true}