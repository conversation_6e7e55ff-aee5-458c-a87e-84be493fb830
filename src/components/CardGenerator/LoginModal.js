import React, { useEffect, useState, useRef } from 'react';
import {
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalCloseButton,
    Button,
    VStack,
    Text,
    useToast,
} from '@chakra-ui/react';
import { useAuth } from '../../contexts/AuthContext';
import { FaExternalLinkAlt, FaCheck } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';

const LoginModal = ({ }) => {
    const { t } = useTranslation('common');
    const { isLoginModalOpen, closeLoginModal, checkUserInfo } = useAuth();
    const [isChecking, setIsChecking] = useState(false);
    const toast = useToast();
    const pollingIntervalRef = useRef(null);

    const handleOpenLoginPage = () => {
        window.open('https://app.funblocks.net/#/login', '_blank');
    };

    const handleCheckLoginStatus = async () => {
        setIsChecking(true);
        try {
            const isLoggedIn = await checkUserInfo();
            if (isLoggedIn) {
                toast({
                    title: t('login_success'),
                    status: "success",
                    duration: 2000,
                    isClosable: true,
                });
                closeLoginModal();
            } else {
                toast({
                    title: t('not_logged_in_yet'),
                    description: t('please_complete_login_first'),
                    status: "warning",
                    duration: 3000,
                    isClosable: true,
                });
            }
        } catch (error) {
            toast({
                title: t('check_login_failed'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsChecking(false);
        }
    };

    // 自动轮询检查登录状态
    useEffect(() => {
        if (isLoginModalOpen) {
            // 每3秒检查一次登录状态
            pollingIntervalRef.current = setInterval(async () => {
                try {
                    const isLoggedIn = await checkUserInfo();
                    if (isLoggedIn) {
                        toast({
                            title: t('login_detected'),
                            description: t('login_successful_auto_close'),
                            status: "success",
                            duration: 2000,
                            isClosable: true,
                        });
                        closeLoginModal();
                    }
                } catch (error) {
                    // 静默处理轮询错误，避免频繁提示
                    console.error('Polling login status error:', error);
                }
            }, 3000);
        } else {
            // 清除轮询
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = null;
            }
        }

        return () => {
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = null;
            }
        };
    }, [isLoginModalOpen, checkUserInfo, closeLoginModal, toast, t]);

    return (
        <Modal isOpen={isLoginModalOpen} onClose={closeLoginModal} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>{t('login')}</ModalHeader>
                <ModalCloseButton />
                <ModalBody pt={4} pb={10}>
                    <VStack spacing={4}>
                        <Text textAlign="center" color="gray.600" mb={2}>
                            {t('login_modal_instruction')}
                        </Text>

                        <Button
                            onClick={handleOpenLoginPage}
                            width="100%"
                            colorScheme="blue"
                            leftIcon={<FaExternalLinkAlt />}
                        >
                            {t('open_login_page')}
                        </Button>

                        <Button
                            onClick={handleCheckLoginStatus}
                            width="100%"
                            variant="outline"
                            colorScheme="green"
                            leftIcon={<FaCheck />}
                            isLoading={isChecking}
                            loadingText={t('checking')}
                        >
                            {t('completed_login')}
                        </Button>

                        <Text fontSize="sm" color="gray.500" textAlign="center">
                            {t('auto_check_login_status')}
                        </Text>
                    </VStack>
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};

export default LoginModal;
